import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Crear una nueva transacción de créditos
export const createTransaction = mutation({
  args: {
    action: v.string(), // "featured_property", "premium_home", "message_received"
    creditsCost: v.number(),
    description: v.string(),
    propertyId: v.optional(v.id("properties")),
    messageId: v.optional(v.id("messages")),
    duration: v.optional(v.number()), // duración en días
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const now = Date.now();
    const expiresAt = args.duration ? now + (args.duration * 24 * 60 * 60 * 1000) : undefined;

    // Crear la transacción
    const transactionId = await ctx.db.insert("transactions", {
      userId: identity.subject,
      action: args.action,
      creditsCost: args.creditsCost,
      description: args.description,
      propertyId: args.propertyId,
      messageId: args.messageId,
      duration: args.duration,
      expiresAt,
      status: "active",
      createdAt: now,
    });

    return transactionId;
  },
});

// Obtener transacciones de un usuario
export const getUserTransactions = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    const transactions = await ctx.db
      .query("transactions")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .order("desc")
      .take(args.limit || 50);

    // Enriquecer con datos de propiedades y mensajes
    const enrichedTransactions = await Promise.all(
      transactions.map(async (transaction) => {
        let property = null;
        let message = null;

        if (transaction.propertyId) {
          property = await ctx.db.get(transaction.propertyId);
        }

        if (transaction.messageId) {
          message = await ctx.db.get(transaction.messageId);
        }

        return {
          ...transaction,
          property: property ? {
            title: property.title,
            images: property.images,
            city: property.city,
          } : null,
          message: message ? {
            subject: message.subject,
            senderName: message.senderName,
          } : null,
        };
      })
    );

    return enrichedTransactions;
  },
});

// Obtener transacciones activas que están por expirar
export const getExpiringTransactions = query({
  args: {
    daysAhead: v.optional(v.number()), // días hacia adelante para notificar
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    const daysAhead = args.daysAhead || 3; // por defecto 3 días
    const futureTime = Date.now() + (daysAhead * 24 * 60 * 60 * 1000);

    const expiringTransactions = await ctx.db
      .query("transactions")
      .withIndex("by_user_status", (q) => 
        q.eq("userId", identity.subject).eq("status", "active")
      )
      .filter((q) => 
        q.and(
          q.neq(q.field("expiresAt"), undefined),
          q.lte(q.field("expiresAt"), futureTime),
          q.gt(q.field("expiresAt"), Date.now())
        )
      )
      .collect();

    // Enriquecer con datos de propiedades
    const enrichedTransactions = await Promise.all(
      expiringTransactions.map(async (transaction) => {
        let property = null;
        if (transaction.propertyId) {
          property = await ctx.db.get(transaction.propertyId);
        }

        return {
          ...transaction,
          property: property ? {
            title: property.title,
            images: property.images,
          } : null,
        };
      })
    );

    return enrichedTransactions;
  },
});

// Marcar transacciones como expiradas
export const expireTransaction = mutation({
  args: {
    transactionId: v.id("transactions"),
  },
  handler: async (ctx, args) => {
    const transaction = await ctx.db.get(args.transactionId);
    if (!transaction) {
      throw new Error("Transacción no encontrada");
    }

    // Actualizar estado a expirado
    await ctx.db.patch(args.transactionId, {
      status: "expired",
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Función para expirar servicios automáticamente (se ejecutará diariamente)
export const expireServices = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    // Buscar transacciones activas que ya expiraron
    const expiredTransactions = await ctx.db
      .query("transactions")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .filter((q) => 
        q.and(
          q.neq(q.field("expiresAt"), undefined),
          q.lt(q.field("expiresAt"), now)
        )
      )
      .collect();

    const processed = [];

    for (const transaction of expiredTransactions) {
      try {
        // Desactivar el servicio según el tipo
        if (transaction.action === "featured_property" && transaction.propertyId) {
          await ctx.db.patch(transaction.propertyId, {
            featured: false,
            featuredUntil: undefined,
            featuredTransaction: undefined,
          });
        } else if (transaction.action === "premium_home" && transaction.propertyId) {
          await ctx.db.patch(transaction.propertyId, {
            premiumHomeUntil: undefined,
            premiumTransaction: undefined,
          });
        }

        // Marcar transacción como expirada
        await ctx.db.patch(transaction._id, {
          status: "expired",
          updatedAt: now,
        });

        processed.push(transaction._id);
      } catch (error) {
        console.error(`Error expirando transacción ${transaction._id}:`, error);
      }
    }

    return {
      processed: processed.length,
      expired: processed,
    };
  },
});

// Obtener estadísticas de gastos del usuario
export const getUserSpendingStats = query({
  args: {
    period: v.optional(v.string()), // "week", "month", "year"
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const period = args.period || "month";
    const now = Date.now();
    
    // Calcular fecha de inicio según el período
    let startDate = now;
    switch (period) {
      case "week":
        startDate = now - (7 * 24 * 60 * 60 * 1000);
        break;
      case "month":
        startDate = now - (30 * 24 * 60 * 60 * 1000);
        break;
      case "year":
        startDate = now - (365 * 24 * 60 * 60 * 1000);
        break;
    }

    // Obtener transacciones del período
    const transactions = await ctx.db
      .query("transactions")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .filter((q) => q.gte(q.field("createdAt"), startDate))
      .collect();

    // Calcular estadísticas
    const totalSpent = transactions.reduce((sum, t) => sum + t.creditsCost, 0);
    const byAction = transactions.reduce((acc, t) => {
      acc[t.action] = (acc[t.action] || 0) + t.creditsCost;
      return acc;
    }, {} as Record<string, number>);

    const activeServices = transactions.filter(t => t.status === "active").length;

    return {
      period,
      totalSpent,
      transactionCount: transactions.length,
      byAction,
      activeServices,
      transactions: transactions.slice(0, 10), // últimas 10
    };
  },
});

// Cancelar una transacción activa (por ejemplo, desactivar servicio manualmente)
export const cancelTransaction = mutation({
  args: {
    transactionId: v.id("transactions"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const transaction = await ctx.db.get(args.transactionId);
    if (!transaction) {
      throw new Error("Transacción no encontrada");
    }

    if (transaction.userId !== identity.subject) {
      throw new Error("No autorizado");
    }

    if (transaction.status !== "active") {
      throw new Error("Solo se pueden cancelar transacciones activas");
    }

    // Desactivar el servicio
    if (transaction.action === "featured_property" && transaction.propertyId) {
      await ctx.db.patch(transaction.propertyId, {
        featured: false,
        featuredUntil: undefined,
        featuredTransaction: undefined,
      });
    } else if (transaction.action === "premium_home" && transaction.propertyId) {
      await ctx.db.patch(transaction.propertyId, {
        premiumHomeUntil: undefined,
        premiumTransaction: undefined,
      });
    }

    // Marcar como cancelada
    await ctx.db.patch(args.transactionId, {
      status: "cancelled",
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Obtener todas las transacciones con información de usuarios (para admin)
export const getAllTransactionsWithUsers = query({
  args: {
    limit: v.optional(v.number()),
    showAll: v.optional(v.boolean()), // Si es true, muestra todas; si es false o undefined, solo activas
  },
  handler: async (ctx, args) => {
    // Esta query es solo para administradores
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Verificar si el usuario es admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .unique();

    if (!user || user.role !== "admin") {
      throw new Error("Acceso denegado: Solo administradores");
    }

    let transactions;
    
    if (args.showAll) {
      // Mostrar todas las transacciones
      transactions = await ctx.db
        .query("transactions")
        .order("desc")
        .take(args.limit || 50);
    } else {
      // Mostrar solo transacciones activas por defecto
      transactions = await ctx.db
        .query("transactions")
        .withIndex("by_status", (q) => q.eq("status", "active"))
        .order("desc")
        .take(args.limit || 50);
    }

    // Enriquecer con datos de usuarios y propiedades
    const enrichedTransactions = await Promise.all(
      transactions.map(async (transaction) => {
        let userInfo = null;
        let property = null;

        // Obtener información del usuario
        const user = await ctx.db
          .query("users")
          .withIndex("by_token", (q) => q.eq("tokenIdentifier", transaction.userId))
          .unique();

        if (user) {
          userInfo = {
            name: user.name || "Usuario sin nombre",
            email: user.email,
            role: user.role || "buyer",
            avatar: user.avatar || user.image,
          };
        }

        // Obtener información de la propiedad si existe
        if (transaction.propertyId) {
          property = await ctx.db.get(transaction.propertyId);
        }

        return {
          ...transaction,
          user: userInfo,
          property: property ? {
            title: property.title,
            images: property.images,
            city: property.city,
          } : null,
        };
      })
    );

    return enrichedTransactions;
  },
}); 