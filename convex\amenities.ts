import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Obtener todas las amenidades activas
export const getActiveAmenities = query({
  handler: async (ctx) => {
    return await ctx.db.query("amenities")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .order("asc")
      .collect();
  },
});

// Obtener amenidades por categoría
export const getAmenitiesByCategory = query({
  args: { category: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db.query("amenities")
      .withIndex("by_category", (q) => q.eq("category", args.category))
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("asc")
      .collect();
  },
});

// Obtener todas las categorías disponibles
export const getCategories = query({
  handler: async (ctx) => {
    const amenities = await ctx.db.query("amenities")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .collect();
    
    const categories = [...new Set(amenities.map(a => a.category))];
    return categories.sort();
  },
});

// Buscar amenidades por palabras clave (para IA)
export const searchAmenitiesByKeywords = query({
  args: { keywords: v.array(v.string()) },
  handler: async (ctx, args) => {
    const amenities = await ctx.db.query("amenities")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .collect();
    
    return amenities.filter(amenity => 
      args.keywords.some(keyword => 
        amenity.searchKeywords.some(searchKey => 
          searchKey.toLowerCase().includes(keyword.toLowerCase())
        ) || amenity.name.toLowerCase().includes(keyword.toLowerCase())
      )
    );
  },
});

// Obtener amenidades agrupadas por categoría
export const getAmenitiesGroupedByCategory = query({
  handler: async (ctx) => {
    const amenities = await ctx.db.query("amenities")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .order("asc")
      .collect();
    
    const grouped = amenities.reduce((acc, amenity) => {
      if (!acc[amenity.category]) {
        acc[amenity.category] = [];
      }
      acc[amenity.category].push(amenity);
      return acc;
    }, {} as Record<string, typeof amenities>);
    
    return grouped;
  },
});

// Crear nueva amenidad
export const createAmenity = mutation({
  args: {
    name: v.string(),
    category: v.string(),
    description: v.optional(v.string()),
    icon: v.optional(v.string()),
    searchKeywords: v.array(v.string()),
    sortOrder: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();
    
    return await ctx.db.insert("amenities", {
      ...args,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Actualizar amenidad
export const updateAmenity = mutation({
  args: {
    id: v.id("amenities"),
    name: v.optional(v.string()),
    category: v.optional(v.string()),
    description: v.optional(v.string()),
    icon: v.optional(v.string()),
    searchKeywords: v.optional(v.array(v.string())),
    isActive: v.optional(v.boolean()),
    sortOrder: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;
    
    await ctx.db.patch(id, {
      ...updates,
      updatedAt: new Date().toISOString(),
    });
    
    return await ctx.db.get(id);
  },
});

// Eliminar amenidad (soft delete)
export const deleteAmenity = mutation({
  args: { id: v.id("amenities") },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.id, {
      isActive: false,
      updatedAt: new Date().toISOString(),
    });
    
    return await ctx.db.get(args.id);
  },
});

// Seed inicial de amenidades
export const seedAmenities = mutation({
  handler: async (ctx) => {
    // Verificar si ya hay amenidades
    const existing = await ctx.db.query("amenities").first();
    if (existing) {
      return "Las amenidades ya están inicializadas";
    }

    const now = new Date().toISOString();
    
    const amenitiesData = [
      // Servicios
      {
        name: "Wi-Fi",
        category: "servicios",
        description: "Conexión a internet inalámbrica",
        icon: "wifi",
        searchKeywords: ["wifi", "internet", "conexion", "red", "wireless"],
        sortOrder: 1,
      },
      {
        name: "Aire Acondicionado",
        category: "servicios",
        description: "Sistema de climatización",
        icon: "wind",
        searchKeywords: ["aire", "acondicionado", "clima", "frio", "ac", "climatizacion"],
        sortOrder: 2,
      },
      {
        name: "Calefacción",
        category: "servicios",
        description: "Sistema de calefacción",
        icon: "thermometer",
        searchKeywords: ["calefaccion", "calor", "caliente", "heating", "temperatura"],
        sortOrder: 3,
      },
      {
        name: "Seguridad",
        category: "servicios",
        description: "Seguridad 24/7",
        icon: "shield",
        searchKeywords: ["seguridad", "vigilancia", "guardia", "proteccion", "24/7"],
        sortOrder: 4,
      },
      {
        name: "Lavandería",
        category: "servicios",
        description: "Servicio de lavandería",
        icon: "washing-machine",
        searchKeywords: ["lavanderia", "lavado", "ropa", "laundry"],
        sortOrder: 5,
      },

      // Comodidades
      {
        name: "Piscina",
        category: "comodidades",
        description: "Piscina para residentes",
        icon: "waves",
        searchKeywords: ["piscina", "alberca", "natacion", "pool", "agua", "nadar"],
        sortOrder: 10,
      },
      {
        name: "Gimnasio",
        category: "comodidades",
        description: "Gimnasio equipado",
        icon: "dumbbell",
        searchKeywords: ["gimnasio", "gym", "fitness", "ejercicio", "deporte", "entrenamiento"],
        sortOrder: 11,
      },
      {
        name: "Ascensor",
        category: "comodidades",
        description: "Ascensor en el edificio",
        icon: "arrow-up",
        searchKeywords: ["ascensor", "elevador", "elevator"],
        sortOrder: 12,
      },
      {
        name: "Balcón",
        category: "comodidades",
        description: "Balcón privado",
        icon: "square",
        searchKeywords: ["balcon", "terraza", "exterior", "balcony"],
        sortOrder: 13,
      },
      {
        name: "Terraza",
        category: "comodidades",
        description: "Terraza amplia",
        icon: "square",
        searchKeywords: ["terraza", "balcon", "exterior", "terrace", "patio"],
        sortOrder: 14,
      },
      {
        name: "Jardín",
        category: "comodidades",
        description: "Jardín privado o común",
        icon: "tree",
        searchKeywords: ["jardin", "verde", "plantas", "garden", "pasto"],
        sortOrder: 15,
      },
      {
        name: "Chimenea",
        category: "comodidades",
        description: "Chimenea funcional",
        icon: "flame",
        searchKeywords: ["chimenea", "fuego", "fireplace", "hogar"],
        sortOrder: 16,
      },

      // Ubicación
      {
        name: "Cerca Metro",
        category: "ubicacion",
        description: "Cerca de estación de metro",
        icon: "train",
        searchKeywords: ["metro", "transporte", "estacion", "subway", "tren"],
        sortOrder: 20,
      },
      {
        name: "Cerca Colegios",
        category: "ubicacion",
        description: "Cerca de instituciones educativas",
        icon: "graduation-cap",
        searchKeywords: ["colegio", "escuela", "educacion", "school", "universidad"],
        sortOrder: 21,
      },
      {
        name: "Centro Ciudad",
        category: "ubicacion",
        description: "Ubicado en el centro de la ciudad",
        icon: "map-pin",
        searchKeywords: ["centro", "downtown", "ciudad", "urbano", "comercial"],
        sortOrder: 22,
      },
      {
        name: "Zona Comercial",
        category: "ubicacion",
        description: "Cerca de centros comerciales",
        icon: "shopping-bag",
        searchKeywords: ["comercial", "tiendas", "shopping", "mall", "compras"],
        sortOrder: 23,
      },

      // Vistas
      {
        name: "Vista al Mar",
        category: "vistas",
        description: "Vista panorámica al océano",
        icon: "waves",
        searchKeywords: ["vista", "mar", "oceano", "playa", "water", "sea"],
        sortOrder: 30,
      },
      {
        name: "Vista a la Montaña",
        category: "vistas",
        description: "Vista a las montañas",
        icon: "mountain",
        searchKeywords: ["vista", "montaña", "cerro", "mountain", "cordillera"],
        sortOrder: 31,
      },
      {
        name: "Vista Ciudad",
        category: "vistas",
        description: "Vista panorámica de la ciudad",
        icon: "building",
        searchKeywords: ["vista", "ciudad", "urbano", "skyline", "edificios"],
        sortOrder: 32,
      },
      {
        name: "Vista Jardín",
        category: "vistas",
        description: "Vista a jardines o áreas verdes",
        icon: "tree",
        searchKeywords: ["vista", "jardin", "verde", "parque", "naturaleza"],
        sortOrder: 33,
      },
    ];

    const results = [];
    for (const amenityData of amenitiesData) {
      const id = await ctx.db.insert("amenities", {
        ...amenityData,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      results.push(id);
    }

    return `Se crearon ${results.length} amenidades en ${amenitiesData.reduce((acc, a) => {
      acc[a.category] = (acc[a.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>)}`;
  },
}); 