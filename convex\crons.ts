import { cronJobs } from "convex/server";
import { api } from "./_generated/api";

// 🕐 CRON JOBS AUTOMATIZADOS

const crons = cronJobs();

// ⏰ Ejecutar cada hora para verificar vencimientos
crons.interval(
  "check-expirations",
  { minutes: 60 }, // Cada hora
  api.featuredProperties.processExpirations,
  {}
);

// 🧹 Ejecutar cada día a las 3 AM para limpiar datos inconsistentes
crons.cron(
  "daily-cleanup",
  "0 3 * * *", // Cada día a las 3:00 AM
  api.featuredProperties.cleanInconsistentData,
  {}
);

// 📊 Los stats se pueden consultar mediante queries, no necesitan cron job

export default crons; 