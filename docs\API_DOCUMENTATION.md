# 🔌 API de Convex para Integraciones Externas

## 📋 Resumen

Convex expone **3 formas principales** para que herramientas externas como **n8n**, **Zapier**, **Make.com** o agentes de IA se conecten:

## 🚀 **1. HTTP Actions (Recomendado para n8n)**

### **URLs Base**
```
Desarrollo: https://tu-deployment.convex.cloud/api/
Producción: https://tu-deployment.convex.cloud/api/
```

### **Endpoints Disponibles**

#### **GET /api/properties**
Obtener todas las propiedades con paginación y filtros.

**Parámetros de consulta:**
- `limit` (opcional): Número de resultados (default: 50)
- `offset` (opcional): Saltar resultados (default: 0)
- `type` (opcional): house, apartment, office, land, commercial
- `status` (opcional): for_sale, for_rent, sold, rented
- `city` (opcional): Filtrar por ciudad
- `minPrice` (opcional): Precio mínimo
- `maxPrice` (opcional): Precio máximo
- `featured` (opcional): true/false

**Ejemplo de uso en n8n:**
```javascript
// HTTP Request Node
Method: GET
URL: https://tu-deployment.convex.cloud/api/properties?limit=10&city=Santiago
```

**Respuesta:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "k17abc123...",
      "title": "Casa en Las Condes",
      "price": 250000000,
      "currency": "CLP",
      "type": "house",
      "status": "for_sale",
      "address": "Av. Las Condes 1234",
      "city": "Santiago",
      "bedrooms": 3,
      "bathrooms": 2,
      "area": 120,
      "images": ["url1", "url2"],
      "createdAt": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "limit": 10,
    "offset": 0,
    "total": 1
  }
}
```

#### **GET /api/properties/{id}**
Obtener una propiedad específica.

**Ejemplo:**
```javascript
// HTTP Request Node
Method: GET
URL: https://tu-deployment.convex.cloud/api/properties/k17abc123def456
```

#### **POST /api/properties**
Crear nueva propiedad.

**Body requerido:**
```json
{
  "title": "Casa Nueva",
  "description": "Hermosa casa en zona residencial",
  "price": 180000000,
  "currency": "CLP",
  "type": "house",
  "status": "draft",
  "address": "Calle Falsa 123",
  "city": "Santiago",
  "state": "RM",
  "country": "Chile",
  "area": 100,
  "bedrooms": 3,
  "bathrooms": 2,
  "images": ["https://cloudinary.com/image1.jpg"],
  "ownerId": "user_abc123"
}
```

## 🔐 **2. Convex Client SDK (Para aplicaciones)**

Si tienes una aplicación externa que puede usar JavaScript/TypeScript:

```javascript
import { ConvexHttpClient } from "convex/browser";

const client = new ConvexHttpClient("https://tu-deployment.convex.cloud");

// Obtener propiedades
const properties = await client.query(api.properties.getProperties, {
  limit: 10,
  city: "Santiago"
});

// Crear propiedad
const newProperty = await client.mutation(api.properties.createProperty, {
  title: "Casa Nueva",
  price: 180000000,
  // ... otros campos
});
```

## 🤖 **3. Webhooks (Para eventos en tiempo real)**

Puedes configurar webhooks para notificar a n8n cuando ocurran eventos:

```typescript
// En convex/properties.ts
export const createPropertyWithWebhook = mutation({
  // ... args
  handler: async (ctx, args) => {
    const propertyId = await ctx.db.insert("properties", args);
    
    // Notificar a n8n
    await fetch("https://tu-n8n-webhook-url.com/webhook", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        event: "property_created",
        propertyId,
        data: args
      })
    });
    
    return propertyId;
  }
});
```

## 🛠️ **Configuración en n8n**

### **Workflow Ejemplo: Sincronizar Propiedades**

1. **Trigger Node**: Schedule Trigger (cada hora)
2. **HTTP Request Node**: 
   - Method: GET
   - URL: `https://tu-deployment.convex.cloud/api/properties`
   - Headers: `Content-Type: application/json`
3. **Function Node**: Procesar datos
4. **Webhook Node**: Enviar a otro sistema

### **Workflow Ejemplo: Crear Propiedad desde Formulario**

1. **Webhook Trigger**: Recibir datos de formulario
2. **Function Node**: Transformar datos
3. **HTTP Request Node**:
   - Method: POST
   - URL: `https://tu-deployment.convex.cloud/api/properties`
   - Body: JSON con datos de la propiedad

## 🔑 **Autenticación (Próximamente)**

Para endpoints protegidos, puedes agregar autenticación:

```typescript
// En convex/http.ts
http.route({
  path: "/api/properties",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const authHeader = request.headers.get("Authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return new Response("Unauthorized", { status: 401 });
    }
    
    // Validar token...
    // Continuar con la lógica
  })
});
```

## 📊 **Casos de Uso Comunes**

### **1. Agente de IA para Recomendaciones**
```javascript
// n8n puede obtener propiedades y enviarlas a OpenAI
const properties = await fetch("/api/properties?city=Santiago&type=house");
const recommendations = await openai.chat.completions.create({
  messages: [
    { role: "system", content: "Eres un agente inmobiliario experto" },
    { role: "user", content: `Recomienda estas propiedades: ${JSON.stringify(properties)}` }
  ]
});
```

### **2. Sincronización con CRM**
```javascript
// Obtener propiedades nuevas cada hora
const newProperties = await fetch("/api/properties?status=for_sale");
// Enviar a Salesforce, HubSpot, etc.
```

### **3. Notificaciones Automáticas**
```javascript
// Cuando se crea una propiedad, notificar por WhatsApp, Email, etc.
```

## 🚀 **Próximas Funcionalidades**

- [ ] Autenticación con API Keys
- [ ] Rate limiting
- [ ] Webhooks bidireccionales
- [ ] GraphQL endpoint
- [ ] Filtros avanzados (geolocalización, rangos de fechas)
- [ ] Bulk operations (crear/actualizar múltiples propiedades)

## 📞 **Soporte**

Para dudas específicas sobre integración con n8n o otros servicios, consulta la documentación de Convex: https://docs.convex.dev/http-actions 