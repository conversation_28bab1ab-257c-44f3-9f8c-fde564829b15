{"buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install --legacy-peer-deps", "framework": "nextjs", "regions": ["iad1"], "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "env": {"NEXT_PUBLIC_SITE_URL": "https://your-app.vercel.app"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}]}]}