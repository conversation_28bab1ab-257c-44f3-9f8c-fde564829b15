import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { AlertTriangle, Crown, Zap, Building } from "lucide-react";
import { useRouter } from "next/navigation";

interface UsageAlertProps {
  type: "properties" | "credits";
  used: number;
  limit: number;
  isUnlimited?: boolean;
  plan: "free" | "pro" | "premium";
  className?: string;
}

export function UsageAlert({ 
  type, 
  used, 
  limit, 
  isUnlimited = false, 
  plan,
  className = ""
}: UsageAlertProps) {
  const router = useRouter();
  
  // No mostrar nada si es ilimitado
  if (isUnlimited) return null;

  const percentage = (used / limit) * 100;
  const isNearLimit = percentage >= 80;
  const isAtLimit = used >= limit;

  // No mostrar alerta si no está cerca del límite
  if (!isNearLimit && !isAtLimit) return null;

  const getIcon = () => {
    return type === "properties" ? <Building className="h-4 w-4" /> : <Zap className="h-4 w-4" />;
  };

  const getTitle = () => {
    if (isAtLimit) {
      return type === "properties" 
        ? "Límite de propiedades alcanzado"
        : "Créditos agotados";
    }
    return type === "properties"
      ? "Te acercas al límite de propiedades"
      : "Pocos créditos restantes";
  };

  const getDescription = () => {
    if (isAtLimit) {
      return type === "properties"
        ? `Has usado todas tus ${limit} propiedades disponibles. Actualiza tu plan para publicar más.`
        : `Has usado todos tus ${limit} créditos. Actualiza tu plan para recibir más consultas.`;
    }
    
    const remaining = limit - used;
    return type === "properties"
      ? `Te quedan ${remaining} de ${limit} propiedades disponibles.`
      : `Te quedan ${remaining} de ${limit} créditos disponibles.`;
  };

  const getVariant = () => {
    return isAtLimit ? "destructive" : "default";
  };

  const canUpgrade = plan !== "premium";

  return (
    <Alert variant={getVariant()} className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle className="flex items-center gap-2">
        {getIcon()}
        {getTitle()}
      </AlertTitle>
      <AlertDescription className="space-y-3">
        <p>{getDescription()}</p>
        
        {!isAtLimit && (
          <Progress 
            value={percentage} 
            className="w-full"
          />
        )}
        
        {canUpgrade && (
          <div className="flex gap-2">
            <Button 
              size="sm"
              onClick={() => router.push("/pricing")}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Crown className="h-3 w-3 mr-1" />
              Actualizar Plan
            </Button>
            
            {!isAtLimit && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => router.push("/dashboard/finance")}
              >
                Ver Uso Detallado
              </Button>
            )}
          </div>
        )}
      </AlertDescription>
    </Alert>
  );
} 