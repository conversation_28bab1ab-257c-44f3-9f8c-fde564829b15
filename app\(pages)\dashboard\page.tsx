"use client";

import { useUser } from "@clerk/nextjs";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { UsageAlert } from "@/components/ui/usage-alert";
import { CreditsDisplay } from "@/components/ui/credits-display";
import { 
  Building2, 
  Users, 
  TrendingUp, 
  DollarSign,
  Plus,
  Eye,
  Heart,
  MessageSquare,
  Building,
  Zap,
  Calendar,
  Crown,
  Star,
  AlertTriangle
} from "lucide-react";
import Link from "next/link";

export default function DashboardPage() {
  const { user } = useUser();
  const router = useRouter();
  
  const currentUser = useQuery(api.users.getUserByToken, 
    user ? { tokenIdentifier: user.id } : "skip"
  );

  // Obtener estadísticas reales
  const usageStats = useQuery(api.subscriptions.getUsageStats);
  const userSubscription = useQuery(api.subscriptions.getUserSubscription);
  const messageStats = useQuery(api.messages.getMessageStats);
  const userProperties = useQuery(api.properties.getUserProperties, 
    user ? { userId: user.id } : "skip"
  );

  // Redirigir a onboarding si no tiene rol
  useEffect(() => {
    if (currentUser && !currentUser.role) {
      router.push('/onboarding');
    }
  }, [currentUser, router]);

  // Mostrar loading mientras se verifica el usuario
  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Cargando usuario...</p>
        </div>
      </div>
    );
  }

  if (currentUser === undefined) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Sincronizando datos...</p>
        </div>
      </div>
    );
  }

  // Si no hay usuario en Convex o no tiene rol, no mostrar nada (se redirigirá)
  if (!currentUser || !currentUser.role) {
    return null;
  }

  const getRoleInfo = (role: string) => {
    switch (role) {
      case 'buyer':
        return { label: 'Comprador', color: 'bg-green-100 text-green-800' };
      case 'seller':
        return { label: 'Vendedor/Propietario', color: 'bg-blue-100 text-blue-800' };
      case 'agent':
        return { label: 'Agente Inmobiliario', color: 'bg-purple-100 text-purple-800' };
      case 'admin':
        return { label: 'Administrador', color: 'bg-red-100 text-red-800' };
      default:
        return { label: 'Usuario', color: 'bg-gray-100 text-gray-800' };
    }
  };

  const roleInfo = getRoleInfo(currentUser.role);

  return (
    <div className="flex flex-col gap-6 p-4 md:p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex-1">
          <h1 className="text-2xl md:text-3xl font-semibold tracking-tight">
            ¡Bienvenido, {currentUser.name || user?.firstName}! 👋
          </h1>
          <div className="flex flex-wrap items-center gap-2 mt-2">
            <p className="text-muted-foreground">Tu rol:</p>
            <Badge className={roleInfo.color}>
              {roleInfo.label}
            </Badge>
            {usageStats && (
              <Badge variant="outline" className="text-xs">
                Plan {usageStats.plan}
              </Badge>
            )}
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
          <CreditsDisplay variant="compact" />
          {(currentUser.role === 'seller' || currentUser.role === 'agent') && (
            <Link href="/dashboard/properties/new">
              <Button className="gap-2 w-full sm:w-auto">
                <Plus className="h-4 w-4" />
                <span className="hidden sm:inline">Publicar Propiedad</span>
                <span className="sm:hidden">Publicar</span>
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* Alertas de Uso */}
      {usageStats && (
        <div className="space-y-4">
          <UsageAlert
            type="properties"
            used={usageStats.propertiesUsed}
            limit={usageStats.propertiesLimit}
            isUnlimited={usageStats.propertiesLimit === 999999}
            plan={usageStats.plan as "free" | "pro" | "premium"}
          />
          
          <UsageAlert
            type="credits"
            used={usageStats.creditsUsed}
            limit={usageStats.creditsLimit}
            isUnlimited={usageStats.creditsLimit === 999999}
            plan={usageStats.plan as "free" | "pro" | "premium"}
          />
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Propiedades
            </CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {usageStats ? usageStats.propertiesUsed : "0"}
            </div>
            <p className="text-xs text-muted-foreground">
              {usageStats ? (usageStats.propertiesLimit === 999999 ? "sin límite" : `de ${usageStats.propertiesLimit}`) : "de 5"} publicadas
            </p>
            {usageStats && usageStats.propertiesLimit !== 999999 && (
              <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all" 
                  style={{ width: `${usageStats.percentage.properties}%` }}
                />
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Créditos
            </CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {usageStats ? usageStats.currentCredits : "10"}
            </div>
            <p className="text-xs text-muted-foreground">
              {usageStats ? (usageStats.creditsLimit === 999999 ? "ilimitados" : `de ${usageStats.creditsLimit}`) : "de 10"} disponibles
            </p>
            {usageStats && usageStats.creditsLimit !== 999999 && (
              <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all" 
                  style={{ width: `${Math.max(0, 100 - usageStats.percentage.credits)}%` }}
                />
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Mensajes
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {messageStats?.totalCount || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              consultas recibidas
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Acciones Rápidas</CardTitle>
            <CardDescription>
              {currentUser.role === 'buyer' 
                ? 'Encuentra tu propiedad ideal'
                : 'Gestiona tus propiedades'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            {currentUser.role === 'buyer' ? (
              <>
                <Link href="/properties">
                  <Button variant="outline" className="w-full justify-start">
                    <Building2 className="mr-2 h-4 w-4" />
                    Explorar Propiedades
                  </Button>
                </Link>
                <Link href="/properties?favorites=true">
                  <Button variant="outline" className="w-full justify-start">
                    <Heart className="mr-2 h-4 w-4" />
                    Mis Favoritos
                  </Button>
                </Link>
              </>
            ) : currentUser.role === 'seller' ? (
              <>
                <Link href="/dashboard/properties">
                  <Button variant="outline" className="w-full justify-start">
                    <Building2 className="mr-2 h-4 w-4" />
                    Mis Propiedades
                  </Button>
                </Link>
                <Link href="/dashboard/properties/new">
                  <Button variant="outline" className="w-full justify-start">
                    <Plus className="mr-2 h-4 w-4" />
                    Nueva Propiedad
                  </Button>
                </Link>
              </>
            ) : (
              // Agente
              <>
                <Link href="/dashboard/properties">
                  <Button variant="outline" className="w-full justify-start">
                    <Building2 className="mr-2 h-4 w-4" />
                    Propiedades Gestionadas
                  </Button>
                </Link>
                <Link href="/dashboard/clients">
                  <Button variant="outline" className="w-full justify-start">
                    <Users className="mr-2 h-4 w-4" />
                    Gestionar Clientes
                  </Button>
                </Link>
                <Link href="/dashboard/properties/new">
                  <Button variant="outline" className="w-full justify-start">
                    <Plus className="mr-2 h-4 w-4" />
                    Agregar Propiedad
                  </Button>
                </Link>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Perfil</CardTitle>
            <CardDescription>Gestiona tu información personal</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="text-sm">
              <p><strong>Email:</strong> {currentUser.email}</p>
              {currentUser.phone && (
                <p><strong>Teléfono:</strong> {currentUser.phone}</p>
              )}
              {currentUser.company && (
                <p><strong>Empresa:</strong> {currentUser.company}</p>
              )}
            </div>
            <Link href="/dashboard/settings">
              <Button variant="outline" className="w-full">
                Editar Perfil
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
