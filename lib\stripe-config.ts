// Configuración de Stripe para Inmova
export const STRIPE_CONFIG = {
  // URLs base
  apiVersion: "2023-10-16" as const,
  
  // Productos y precios
  products: {
    pro: {
      name: "Inmova Pro",
      priceId: process.env.STRIPE_PRICE_PRO || "price_pro_dev", // Se configurará en .env
      price: 29,
      currency: "USD",
      interval: "month",
      features: [
        "25 propiedades máximo",
        "100 consultas por mes", 
        "Destacado en búsquedas",
        "Soporte prioritario"
      ]
    },
    premium: {
      name: "Inmova Premium",
      priceId: process.env.STRIPE_PRICE_PREMIUM || "price_premium_dev",
      price: 79,
      currency: "USD",
      interval: "month",
      features: [
        "100 propiedades máximo",
        "300 consultas por mes",
        "Máxima prioridad en búsquedas", 
        "Analytics avanzados",
        "Soporte 24/7",
        "API access",
        "Multi-usuario"
      ]
    }
  },
  
  // Límites por plan
  limits: {
    free: {
      properties: 5,
      credits: 10,
      priority: 0,
      features: [
        "5 propiedades máximo",
        "10 créditos por mes",
        "Búsqueda básica"
      ]
    },
    pro: {
      properties: 25,
      credits: 100,
      priority: 1,
      features: [
        "25 propiedades máximo",
        "100 créditos por mes", 
        "Destacado en búsquedas",
        "Soporte prioritario"
      ]
    },
    premium: {
      properties: 100,
      credits: 300,
      priority: 2,
      features: [
        "100 propiedades máximo",
        "300 créditos por mes",
        "Máxima prioridad en búsquedas", 
        "Analytics avanzados",
        "Soporte 24/7",
        "API access",
        "Multi-usuario"
      ]
    }
  },
  
  // URLs de redirección
  urls: {
    success: process.env.NEXT_PUBLIC_APP_URL + "/dashboard/finance?success=true&session_id={CHECKOUT_SESSION_ID}",
    cancel: process.env.NEXT_PUBLIC_APP_URL + "/dashboard/finance?canceled=true"
  },

  // Configuración de checkout
  checkout: {
    mode: "subscription" as const,
    payment_method_types: ["card"] as const,
    allow_promotion_codes: true,
    billing_address_collection: "required" as const,
    locale: "es" as const, // ¡ESPAÑOL POR DEFECTO!
    // Branding personalizado
    custom_text: {
      shipping_address: {
        message: "Información de facturación"
      },
      submit: {
        message: "Procesar suscripción"
      }
    }
  }
};

// Helper para obtener información del plan
export function getPlanInfo(plan: "free" | "pro" | "premium") {
  const limits = STRIPE_CONFIG.limits[plan];
  const product = plan !== "free" ? STRIPE_CONFIG.products[plan as keyof typeof STRIPE_CONFIG.products] : null;
  
  return {
    name: product?.name || "Plan Gratuito",
    price: product?.price || 0,
    currency: product?.currency || "USD",
    interval: product?.interval || "month",
    priceDisplay: product?.price ? formatPrice(product.price) + "/mes" : "Gratis",
    features: limits.features,
    limits: {
      properties: limits.properties,
      credits: limits.credits,
      priority: limits.priority
    },
    priceId: product?.priceId,
    isProduction: process.env.NODE_ENV === "production"
  };
}

// Helper para formatear precio
export function formatPrice(amount: number, currency: string = "USD") {
  return new Intl.NumberFormat("es-GT", {
    style: "currency",
    currency: currency,
  }).format(amount);
} 