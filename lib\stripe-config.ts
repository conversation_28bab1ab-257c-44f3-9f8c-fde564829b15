// Configuración de Stripe para Inmova
export const STRIPE_CONFIG = {
  // URLs base
  apiVersion: "2023-10-16" as const,
  
  // Productos y precios
  products: {
    // DEPRECADO: Mantener para usuarios existentes
    pro: {
      name: "Inmova Pro (Legacy)",
      priceId: process.env.STRIPE_PRICE_PRO || "price_pro_dev",
      price: 29,
      currency: "USD",
      interval: "month",
      deprecated: true,
      features: [
        "25 propiedades máximo",
        "100 consultas por mes",
        "Destacado en búsquedas",
        "Soporte prioritario"
      ]
    },
    // DEPRECADO: Mantener para usuarios existentes
    premium_legacy: {
      name: "Inmova Premium (Legacy)",
      priceId: process.env.STRIPE_PRICE_PREMIUM || "price_premium_dev",
      price: 79,
      currency: "USD",
      interval: "month",
      deprecated: true,
      features: [
        "100 propiedades máximo",
        "300 consultas por mes",
        "Máxima prioridad en búsquedas",
        "Analytics avanzados",
        "Soporte 24/7",
        "API access",
        "Multi-usuario"
      ]
    },
    // NUEVO: Plan Premium con trial
    premium: {
      name: "Inmova Premium",
      priceId: process.env.STRIPE_PRICE_PREMIUM_NEW || "price_premium_new_dev",
      price: 99,
      currency: "USD",
      interval: "month",
      trial_period_days: 15,
      features: [
        "Propiedades ilimitadas",
        "300 créditos por mes",
        "Posición premium en home",
        "Destacados ilimitados",
        "Analytics avanzados",
        "Soporte prioritario 24/7",
        "Sin marca de agua",
        "15 días de prueba gratis"
      ]
    }
  },
  
  // Límites por plan
  limits: {
    free: {
      properties: 5,
      credits: 10,
      priority: 0,
      features: [
        "5 propiedades máximo",
        "10 créditos por mes",
        "Búsqueda básica"
      ]
    },
    // DEPRECADO: Mantener para usuarios existentes
    pro: {
      properties: 25,
      credits: 100,
      priority: 1,
      deprecated: true,
      features: [
        "25 propiedades máximo",
        "100 créditos por mes",
        "Destacado en búsquedas",
        "Soporte prioritario"
      ]
    },
    // NUEVO: Plan Premium unificado
    premium: {
      properties: 999999, // Ilimitadas
      credits: 300,
      priority: 2,
      features: [
        "Propiedades ilimitadas",
        "300 créditos por mes",
        "Posición premium en home",
        "Destacados ilimitados",
        "Analytics avanzados",
        "Soporte prioritario 24/7",
        "Sin marca de agua",
        "15 días de prueba gratis"
      ]
    },
    // Para usuarios en trial
    trial: {
      properties: 999999, // Ilimitadas durante trial
      credits: 300,
      priority: 2,
      features: [
        "Acceso completo por 15 días",
        "Propiedades ilimitadas",
        "300 créditos incluidos",
        "Todas las funciones premium"
      ]
    }
  },
  
  // URLs de redirección
  urls: {
    success: process.env.NEXT_PUBLIC_APP_URL + "/dashboard/finance?success=true&session_id={CHECKOUT_SESSION_ID}",
    cancel: process.env.NEXT_PUBLIC_APP_URL + "/dashboard/finance?canceled=true"
  },

  // Configuración de checkout
  checkout: {
    mode: "subscription" as const,
    payment_method_types: ["card"] as const,
    allow_promotion_codes: true,
    billing_address_collection: "required" as const,
    locale: "es" as const, // ¡ESPAÑOL POR DEFECTO!
    // Branding personalizado
    custom_text: {
      shipping_address: {
        message: "Información de facturación"
      },
      submit: {
        message: "Procesar suscripción"
      }
    }
  }
};

// Helper para obtener información del plan
export function getPlanInfo(plan: "free" | "pro" | "premium" | "trial") {
  const limits = STRIPE_CONFIG.limits[plan];
  const product = (plan !== "free" && plan !== "trial") ? STRIPE_CONFIG.products[plan as keyof typeof STRIPE_CONFIG.products] : null;

  return {
    name: product?.name || (plan === "trial" ? "Trial Premium" : "Plan Gratuito"),
    price: product?.price || 0,
    currency: product?.currency || "USD",
    interval: product?.interval || "month",
    priceDisplay: plan === "trial" ? "Gratis por 15 días" : (product?.price ? formatPrice(product.price) + "/mes" : "Gratis"),
    features: limits.features,
    limits: {
      properties: limits.properties,
      credits: limits.credits,
      priority: limits.priority
    },
    priceId: product?.priceId,
    isProduction: process.env.NODE_ENV === "production",
    deprecated: product?.deprecated || false,
    trial_period_days: product?.trial_period_days
  };
}

// Helper para formatear precio
export function formatPrice(amount: number, currency: string = "USD") {
  return new Intl.NumberFormat("es-GT", {
    style: "currency",
    currency: currency,
  }).format(amount);
}

// Helper para verificar si un rol necesita suscripción
export function needsSubscription(role: string): boolean {
  return role === 'seller' || role === 'agent';
}

// Helper para determinar si debe iniciar trial
export function shouldStartTrial(role: string, isNewUser: boolean, hasUsedTrial: boolean): boolean {
  return needsSubscription(role) && isNewUser && !hasUsedTrial;
}

// Helper para obtener límites efectivos (considerando trial)
export function getEffectiveLimits(subscription: any) {
  if (!subscription) {
    return STRIPE_CONFIG.limits.free;
  }

  // Si está en trial activo, usar límites de trial
  if (subscription.isTrialActive) {
    return STRIPE_CONFIG.limits.trial;
  }

  // Usar límites del plan actual
  return STRIPE_CONFIG.limits[subscription.plan as keyof typeof STRIPE_CONFIG.limits] || STRIPE_CONFIG.limits.free;
}