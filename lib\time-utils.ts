import { format, parseISO } from 'date-fns';
import { es } from 'date-fns/locale';

// Configuración para Guatemala/México
export const TIMEZONE = 'America/Guatemala'; // GMT-6
export const LOCALE = 'es-GT';

// Formatear hora en formato 12h (AM/PM) para la región
export function formatTimeFor12h(date: Date | string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return new Intl.DateTimeFormat(LOCALE, {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
    timeZone: TIMEZONE
  }).format(dateObj);
}

// Formatear slot de tiempo en zona horaria de Guatemala
export function formatSlotTime(isoString: string): string {
  const date = new Date(isoString);
  
  // Convertir a zona horaria de Guatemala (UTC-6)
  const guatemalaTime = new Date(date.getTime() - (6 * 60 * 60 * 1000));
  const localHours = guatemalaTime.getUTCHours();
  const localMinutes = guatemalaTime.getUTCMinutes();
  
  // Formatear en formato 12h
  const period = localHours >= 12 ? 'p. m.' : 'a. m.';
  const displayHours = localHours === 0 ? 12 : localHours > 12 ? localHours - 12 : localHours;
  
  return `${displayHours}:${localMinutes.toString().padStart(2, '0')} ${period}`;
}

// Formatear hora en formato 24h
export function formatTimeFor24h(date: Date | string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, 'HH:mm');
}

// Formatear fecha completa con hora
export function formatDateTimeLocal(date: Date | string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return new Intl.DateTimeFormat(LOCALE, {
    weekday: 'long',
    year: 'numeric',
    month: 'long', 
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
    timeZone: TIMEZONE
  }).format(dateObj);
}

// Formatear fecha sin hora
export function formatDateLocal(date: Date | string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, 'EEEE, dd MMMM yyyy', { locale: es });
}

// Convertir hora local (HH:mm) a Date en la zona horaria correcta
export function createDateInTimezone(dateStr: string, timeStr: string): Date {
  const [year, month, day] = dateStr.split('-').map(Number);
  const [hour, minute] = timeStr.split(':').map(Number);
  
  // Crear fecha en zona horaria local
  const date = new Date();
  date.setFullYear(year, month - 1, day);
  date.setHours(hour, minute, 0, 0);
  
  return date;
}

// Obtener horarios de trabajo típicos para la región
export const DEFAULT_BUSINESS_HOURS = {
  start: '09:00',
  end: '17:00',
  slotDuration: 60, // minutos
  breakTime: 15 // minutos
};

// Validar si una hora está en horario de trabajo razonable
export function isReasonableWorkingHour(timeStr: string): boolean {
  const [hour] = timeStr.split(':').map(Number);
  return hour >= 7 && hour <= 21; // 7 AM a 9 PM
}

// Formatear rango de tiempo
export function formatTimeRange(startTime: Date | string, endTime: Date | string): string {
  const start = formatTimeFor12h(startTime);
  const end = formatTimeFor12h(endTime);
  return `${start} - ${end}`;
}

// Obtener fecha actual en la zona horaria correcta
export function getCurrentDateInTimezone(): Date {
  const now = new Date();
  return new Date(now.toLocaleString("en-US", { timeZone: TIMEZONE }));
}

// Verificar si una fecha/hora ya pasó
export function isPastDateTime(dateTime: Date | string): boolean {
  const dateObj = typeof dateTime === 'string' ? parseISO(dateTime) : dateTime;
  return dateObj < getCurrentDateInTimezone();
}

// Agregar tiempo mínimo de anticipación (ej: 2 horas)
export function getMinimumBookingTime(): Date {
  const now = getCurrentDateInTimezone();
  now.setHours(now.getHours() + 2); // 2 horas de anticipación mínima
  return now;
}

// Helper para datetime-local input (formato requerido por el input HTML)
export function formatForDateTimeLocal(date: Date | string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day}T${hours}:${minutes}`;
}

// Convertir de datetime-local input a Date
export function parseFromDateTimeLocal(datetimeLocal: string): Date {
  return new Date(datetimeLocal);
} 