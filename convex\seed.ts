import { mutation } from "./_generated/server";
import { PROPERTY_TYPES, PROPERTY_STATUS } from "./schema";
import { api } from "./_generated/api";

export const seedProperties = mutation({
  args: {},
  handler: async (ctx) => {
    // Verificar si ya hay datos
    const existingProperties = await ctx.db.query("properties").take(1);
    if (existingProperties.length > 0) {
      return "Los datos ya existen";
    }

    const now = new Date().toISOString();
    
    const sampleProperties = [
      {
        title: "Casa moderna en Las Condes",
        description: "Hermosa casa de 3 pisos con jardín privado, piscina y estacionamiento para 2 autos. Ubicada en zona residencial exclusiva con fácil acceso a colegios y centros comerciales.",
        price: 450000000,
        currency: "CLP",
        type: PROPERTY_TYPES.HOUSE,
        status: PROPERTY_STATUS.FOR_SALE,
        address: "Av. Apoquindo 1234",
        city: "Las Condes",
        state: "Región Metropolitana",
        country: "Chile",
        zipCode: "7550000",
        coordinates: { lat: -33.4084, lng: -70.5454 },
        bedrooms: 4,
        bathrooms: 3,
        area: 280,
        builtYear: 2018,
        parking: 2,
        images: [
          "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800",
          "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=800"
        ],
        ownerId: "user_demo_1",
        featured: true,
        createdAt: now,
        updatedAt: now,
        publishedAt: now,
      },
      {
        title: "Apartamento con vista al mar en Viña del Mar",
        description: "Moderno departamento en piso alto con vista panorámica al océano Pacífico. Totalmente amoblado, con terraza y acceso directo a la playa.",
        price: 180000000,
        currency: "CLP", 
        type: PROPERTY_TYPES.APARTMENT,
        status: PROPERTY_STATUS.FOR_SALE,
        address: "Av. San Martín 567",
        city: "Viña del Mar",
        state: "Valparaíso",
        country: "Chile",
        zipCode: "2520000",
        coordinates: { lat: -33.0244, lng: -71.5519 },
        bedrooms: 2,
        bathrooms: 2,
        area: 95,
        builtYear: 2020,
        parking: 1,
        images: [
          "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800",
          "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800"
        ],
        ownerId: "user_demo_2",
        featured: true,
        createdAt: now,
        updatedAt: now,
        publishedAt: now,
      },
      {
        title: "Oficina premium en Providencia",
        description: "Elegante oficina completamente equipada en edificio corporativo AAA. Incluye sala de reuniones, recepción y estacionamientos asignados.",
        price: 2500000,
        currency: "CLP",
        type: PROPERTY_TYPES.OFFICE,
        status: PROPERTY_STATUS.FOR_RENT,
        address: "Av. Providencia 1234",
        city: "Providencia", 
        state: "Región Metropolitana",
        country: "Chile",
        zipCode: "7500000",
        coordinates: { lat: -33.4378, lng: -70.6504 },
        area: 120,
        builtYear: 2019,
        parking: 3,
        images: [
          "https://images.unsplash.com/photo-1497366216548-37526070297c?w=800",
          "https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=800"
        ],
        ownerId: "user_demo_3",
        featured: false,
        createdAt: now,
        updatedAt: now,
        publishedAt: now,
      },
      {
        title: "Penthouse de lujo en Vitacura",
        description: "Exclusivo penthouse de 2 pisos con terraza privada de 100m². Vista 360° de la cordillera y la ciudad. Acabados premium y domótica integrada.",
        price: 890000000,
        currency: "CLP",
        type: PROPERTY_TYPES.APARTMENT,
        status: PROPERTY_STATUS.FOR_SALE,
        address: "Av. Vitacura 3456",
        city: "Vitacura",
        state: "Región Metropolitana", 
        country: "Chile",
        zipCode: "7630000",
        coordinates: { lat: -33.3823, lng: -70.5698 },
        bedrooms: 4,
        bathrooms: 4,
        area: 220,
        builtYear: 2021,
        parking: 3,
        images: [
          "https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800",
          "https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?w=800"
        ],
        ownerId: "user_demo_1",
        featured: true,
        createdAt: now,
        updatedAt: now,
        publishedAt: now,
      },
      {
        title: "Casa familiar en Ñuñoa",
        description: "Acogedora casa de 2 pisos ideal para familias. Patio amplio, parrilla, y cerca de colegios y parques. Excelente conectividad con el metro.",
        price: 195000000,
        currency: "CLP",
        type: PROPERTY_TYPES.HOUSE,
        status: PROPERTY_STATUS.FOR_SALE,
        address: "José Pedro Alessandri 987",
        city: "Ñuñoa",
        state: "Región Metropolitana",
        country: "Chile", 
        zipCode: "7750000",
        coordinates: { lat: -33.4569, lng: -70.5969 },
        bedrooms: 3,
        bathrooms: 2,
        area: 160,
        builtYear: 2015,
        parking: 1,
        images: [
          "https://images.unsplash.com/photo-1600047509807-ba8f99d2cdde?w=800",
          "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800"
        ],
        ownerId: "user_demo_4",
        featured: false,
        createdAt: now,
        updatedAt: now,
        publishedAt: now,
      },
      {
        title: "Departamento nuevo en Maipú",
        description: "Moderno departamento a estrenar en condominio con áreas verdes, quincho y juegos infantiles. Excelente ubicación cerca del metro.",
        price: 85000000,
        currency: "CLP",
        type: PROPERTY_TYPES.APARTMENT,
        status: PROPERTY_STATUS.FOR_SALE,
        address: "Av. Pajaritos 1456",
        city: "Maipú",
        state: "Región Metropolitana",
        country: "Chile",
        zipCode: "9250000",
        coordinates: { lat: -33.5084, lng: -70.7565 },
        bedrooms: 2,
        bathrooms: 1,
        area: 58,
        builtYear: 2023,
        parking: 1,
        images: [
          "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800",
          "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800"
        ],
        ownerId: "user_demo_5",
        featured: false,
        createdAt: now,
        updatedAt: now,
        publishedAt: now,
      }
    ];

    // Insertar todas las propiedades
    const results = [];
    for (const property of sampleProperties) {
      const id = await ctx.db.insert("properties", property);
      results.push(id);
    }

    return `Se crearon ${results.length} propiedades de prueba`;
  },
});

// Función para crear propiedades de ejemplo en Guatemala
export const createGuatemalaProperties = mutation({
  args: {},
  handler: async (ctx) => {
    const propertiesData = [
      {
        title: "Casa moderna en Zona 10",
        description: "Hermosa casa de 3 niveles en la exclusiva Zona 10 de Guatemala. Cuenta con acabados de lujo, jardín privado y garaje para 2 vehículos.",
        price: 450000,
        currency: "USD",
        type: "house" as const,
        status: "for_sale" as const,
        address: "15 Avenida 12-45 Zona 10",
        city: "Guatemala",
        state: "Guatemala",
        country: "Guatemala",
        zipCode: "01010",
        bedrooms: 4,
        bathrooms: 3,
        area: 350,
        builtYear: 2020,
        parking: 2,
        amenities: ["Piscina", "Vista Jardín", "Seguridad 24h", "Área Social"],
        images: [
          "https://res.cloudinary.com/demo/image/upload/sample.jpg",
          "https://res.cloudinary.com/demo/image/upload/sample2.jpg"
        ],
        ownerId: "user_demo_owner",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        publishedAt: new Date().toISOString(),
        featured: true
      },
      {
        title: "Apartamento en Zona 14",
        description: "Moderno apartamento en torre residencial con amenidades completas. Vista panorámica de la ciudad.",
        price: 280000,
        currency: "USD", 
        type: "apartment" as const,
        status: "for_sale" as const,
        address: "Avenida Las Américas 14-55 Zona 14",
        city: "Guatemala",
        state: "Guatemala", 
        country: "Guatemala",
        zipCode: "01014",
        bedrooms: 3,
        bathrooms: 2,
        area: 120,
        builtYear: 2019,
        parking: 1,
        amenities: ["Gimnasio", "Piscina", "Salón de Eventos", "Seguridad 24h"],
        images: [
          "https://res.cloudinary.com/demo/image/upload/sample3.jpg"
        ],
        ownerId: "user_demo_owner",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        publishedAt: new Date().toISOString(),
        featured: false
      },
      {
        title: "Casa en alquiler Zona 15",
        description: "Acogedora casa familiar en zona residencial tranquila. Perfecta para familias.",
        price: 1200,
        currency: "USD",
        type: "house" as const,
        status: "for_rent" as const,
        address: "23 Calle 5-12 Zona 15",
        city: "Guatemala",
        state: "Guatemala",
        country: "Guatemala", 
        zipCode: "01015",
        bedrooms: 3,
        bathrooms: 2,
        area: 200,
        builtYear: 2015,
        parking: 2,
        amenities: ["Vista Jardín", "Terraza", "Lavandería"],
        images: [
          "https://res.cloudinary.com/demo/image/upload/sample4.jpg"
        ],
        ownerId: "user_demo_owner",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        publishedAt: new Date().toISOString(),
        featured: false
      },
      {
        title: "Apartamento amueblado Zona 10",
        description: "Lujoso apartamento completamente amueblado en el corazón de la Zona Rosa. Ideal para ejecutivos.",
        price: 2500,
        currency: "USD",
        type: "apartment" as const,
        status: "for_rent" as const,
        address: "6a Avenida 10-25 Zona 10",
        city: "Guatemala",
        state: "Guatemala",
        country: "Guatemala",
        zipCode: "01010", 
        bedrooms: 2,
        bathrooms: 2,
        area: 85,
        builtYear: 2021,
        parking: 1,
        amenities: ["Amoblado", "WiFi", "TV Cable", "Gimnasio", "Seguridad 24h"],
        images: [
          "https://res.cloudinary.com/demo/image/upload/sample5.jpg"
        ],
        ownerId: "user_demo_owner",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        publishedAt: new Date().toISOString(),
        featured: true
      },
      {
        title: "Casa en Mixco",
        description: "Hermosa casa en residencial privado en Mixco. Excelente ubicación y precio.",
        price: 180000,
        currency: "USD",
        type: "house" as const,
        status: "for_sale" as const,
        address: "Residencial Los Álamos, Mixco",
        city: "Mixco",
        state: "Guatemala",
        country: "Guatemala",
        zipCode: "01057",
        bedrooms: 3,
        bathrooms: 2,
        area: 160,
        builtYear: 2018,
        parking: 2,
        amenities: ["Seguridad Privada", "Áreas Verdes", "Cancha Deportiva"],
        images: [
          "https://res.cloudinary.com/demo/image/upload/sample6.jpg"
        ],
        ownerId: "user_demo_owner",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        publishedAt: new Date().toISOString(),
        featured: false
      }
    ];

    const createdProperties = [];
    
    for (const propertyData of propertiesData) {
      const propertyId = await ctx.db.insert("properties", propertyData);
      createdProperties.push(propertyId);
    }

    return {
      success: true,
      message: `Se crearon ${createdProperties.length} propiedades de ejemplo en Guatemala`,
      propertyIds: createdProperties
    };
  }
});

