"use client";

import { useState, useEffect, Suspense } from "react";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { PropertyCard } from "@/components/marketplace/property-card";
import { AdvancedSearch } from "@/components/marketplace/advanced-search";
import { Button } from "@/components/ui/button";
import { Grid, List, Search, Sparkles } from "lucide-react";
import { Property, PropertyFilters } from "@/types/marketplace";
import { useDebounce } from "use-debounce";
import { useSearchParams } from "next/navigation";

function PropertiesPageContent() {
  const searchParams = useSearchParams();
  const [searchData, setSearchData] = useState<PropertyFilters & { searchTerm?: string }>({});
  const [debouncedSearchTerm] = useDebounce(searchData.searchTerm || "", 300);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [aiSearchResults, setAiSearchResults] = useState<any[] | null>(null);

  // Inicializar filtros desde URL params
  useEffect(() => {
    const urlFilters: PropertyFilters & { searchTerm?: string } = {};
    
    // Leer parámetros de la URL
    const search = searchParams.get('search');
    const type = searchParams.get('type');
    const status = searchParams.get('status');
    const city = searchParams.get('city');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const amenities = searchParams.get('amenities');
    
    if (search) urlFilters.searchTerm = search;
    if (type && ['house', 'apartment', 'office', 'land', 'commercial'].includes(type)) {
      urlFilters.type = [type as 'house' | 'apartment' | 'office' | 'land' | 'commercial'];
    }
    if (status && ['for_sale', 'for_rent', 'sold', 'rented', 'draft'].includes(status)) {
      urlFilters.status = [status as 'for_sale' | 'for_rent' | 'sold' | 'rented' | 'draft'];
    }
    if (city) urlFilters.city = city;
    if (minPrice) urlFilters.minPrice = Number(minPrice);
    if (maxPrice) urlFilters.maxPrice = Number(maxPrice);
    if (amenities) urlFilters.amenities = amenities.split(',');
    
    setSearchData(urlFilters);
  }, [searchParams]);

  // Queries
  const properties = useQuery(api.properties.advancedSearchProperties, {
    searchTerm: debouncedSearchTerm || undefined,
    limit: 20,
    type: searchData.type?.[0],
    status: searchData.status?.[0],
    city: searchData.city,
    minPrice: searchData.minPrice,
    maxPrice: searchData.maxPrice,
    featured: searchData.featured,
    requiredAmenities: searchData.amenities,
  });

  const displayProperties = aiSearchResults || properties;

  const handleSearch = (data: PropertyFilters & { searchTerm?: string }) => {
    setSearchData(data);
    setAiSearchResults(null); // Limpiar resultados de IA
  };

  const handleAISearch = (results: any[]) => {
    setAiSearchResults(results);
  };

  const clearFilters = () => {
    setSearchData({});
    setAiSearchResults(null);
  };

  return (
    <div className="w-full">
      <div className="container mx-auto px-4 py-6 md:py-8">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-2 md:mb-4 text-gray-900">Propiedades</h1>
          <p className="text-gray-600 text-base md:text-lg">
            Encuentra tu propiedad ideal entre {displayProperties?.length || 0} opciones disponibles
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-6 md:mb-8">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-4">
            <div className="flex-1">
              <AdvancedSearch 
                onSearch={handleSearch}
                onAISearch={handleAISearch}
                variant="page" 
                showAdvancedByDefault={false}
              />
            </div>
            
            {/* View Mode Toggle */}
            <div className="flex gap-2 justify-center lg:justify-end">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="icon"
                onClick={() => setViewMode("grid")}
                className={viewMode === "grid" ? "bg-blue-600 hover:bg-blue-700" : "border-2 border-gray-200 hover:border-blue-500"}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="icon"
                onClick={() => setViewMode("list")}
                className={viewMode === "list" ? "bg-blue-600 hover:bg-blue-700" : "border-2 border-gray-200 hover:border-blue-500"}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Results */}
        <div className="space-y-4 md:space-y-6">
          {/* Results Count */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              <p className="text-base md:text-lg font-medium text-gray-700">
                {displayProperties?.length || 0} propiedades encontradas
              </p>
              {aiSearchResults && (
                <div className="flex items-center gap-2 bg-gradient-to-r from-purple-100 to-blue-100 px-3 py-1 rounded-full">
                  <Sparkles className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium text-purple-700">Búsqueda IA</span>
                </div>
              )}
            </div>
          </div>

          {/* Properties Grid/List */}
          {displayProperties && displayProperties.length > 0 ? (
            <div className={
              viewMode === "grid" 
                ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8"
                : "space-y-4 md:space-y-6"
            }>
              {displayProperties.map((property, index) => (
                <PropertyCard
                  key={property._id || `ai-result-${index}`}
                  property={property as Property}
                  onFavorite={(id) => {
                    // TODO: Implementar favoritos
                    console.log("Toggle favorite:", id);
                  }}
                  isFavorite={false}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 md:py-20 bg-white rounded-2xl shadow-lg">
              <div className="w-16 h-16 md:w-20 md:h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 md:mb-6">
                <Search className="w-8 h-8 md:w-10 md:h-10 text-gray-400" />
              </div>
              <h3 className="text-lg md:text-xl font-semibold text-gray-900 mb-2">No se encontraron propiedades</h3>
              <p className="text-gray-600 mb-4 md:mb-6 px-4">
                Intenta ajustar tus filtros de búsqueda para encontrar más resultados
              </p>
              <Button onClick={clearFilters} className="bg-blue-600 hover:bg-blue-700 text-white rounded-xl">
                Limpiar todos los filtros
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Loading fallback component
function PropertiesLoading() {
  return (
    <div className="w-full">
      <div className="container mx-auto px-4 py-6 md:py-8">
        <div className="mb-6 md:mb-8">
          <div className="h-8 bg-gray-200 rounded animate-pulse mb-4"></div>
          <div className="h-4 bg-gray-200 rounded animate-pulse w-2/3"></div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={`loading-skeleton-${i}`} className="bg-gray-200 rounded-lg h-64 animate-pulse"></div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default function PropertiesPage() {
  return (
    <Suspense fallback={<PropertiesLoading />}>
      <PropertiesPageContent />
    </Suspense>
  );
} 