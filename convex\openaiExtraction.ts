import { action, internalAction } from "./_generated/server";
import { v } from "convex/values";
import { api, internal } from "./_generated/api";
import OpenAI from 'openai';

// Inicializar OpenAI solo si la API key está disponible
let openai: OpenAI | null = null;

try {
  if (process.env.OPENAI_API_KEY) {
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }
} catch (error) {
  console.warn("OpenAI no pudo inicializarse:", error);
}

export const extractSearchCriteriaWithAI = internalAction({
  args: { 
    searchText: v.string(),
    context: v.optional(v.string()) // Contexto adicional como historial de conversación
  },
  handler: async (ctx, args) => {
    // Verificar si OpenAI está disponible
    if (!openai) {
      return {
        success: false,
        error: "OpenAI no está configurado",
        fallbackToRegex: true
      };
    }

    try {
      const prompt = `
Analiza el siguiente mensaje de búsqueda de propiedades inmobiliarias y extrae los criterios específicos.

Mensaje del usuario: "${args.searchText}"
${args.context ? `Contexto: ${args.context}` : ''}

Instrucciones:
1. Extrae solo los criterios que están EXPLÍCITAMENTE mencionados
2. No asumas valores que no están claros
3. Maneja variaciones de escritura (ej: "pisina" = "piscina")
4. Reconoce abreviaciones (ej: "depto" = "apartamento")
5. Convierte formatos de precio (ej: "200k" = 200000)

Responde en formato JSON con esta estructura exacta:
{
  "type": "apartment|house|office|land|commercial", // solo si está explícito
  "status": "for_sale|for_rent", // solo si está explícito  
  "city": "string", // ciudades, zonas, barrios mencionados
  "minPrice": number, // precio mínimo en números
  "maxPrice": number, // precio máximo en números
  "bedrooms": number, // número de habitaciones/cuartos
  "bathrooms": number, // número de baños
  "minArea": number, // área mínima en m²
  "maxArea": number, // área máxima en m²
  "amenities": ["string"], // amenidades específicas mencionadas
  "keywords": ["string"], // palabras clave relevantes (moderno, amueblado, etc.)
  "zones": ["string"], // zonas específicas como "zona 14"
  "searchTerms": ["string"] // términos de búsqueda general
}

IMPORTANTE: 
- Solo incluye campos que estén explícitamente mencionados
- No incluyas campos con valores null o undefined
- Para precios, reconoce: "200k"=200000, "200 mil"=200000, "hasta 300"=maxPrice:300000
- Para amenidades, reconoce variaciones: "pisina"="piscina", "parqueo"="parking"
`;

      const completion = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: "Eres un experto en análisis de criterios de búsqueda inmobiliaria. Responde solo con JSON válido, sin texto adicional."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 500
      });

      const response = completion.choices[0]?.message?.content?.trim();
      
      if (!response) {
        throw new Error("No se recibió respuesta de OpenAI");
      }

      try {
        // Limpiar la respuesta por si tiene markdown
        const cleanResponse = response.replace(/```json\n?|\n?```/g, '').trim();
        const extractedCriteria = JSON.parse(cleanResponse);
        
        // Validar que los tipos sean correctos
        const validTypes = ['apartment', 'house', 'office', 'land', 'commercial'];
        const validStatuses = ['for_sale', 'for_rent'];
        
        if (extractedCriteria.type && !validTypes.includes(extractedCriteria.type)) {
          delete extractedCriteria.type;
        }
        
        if (extractedCriteria.status && !validStatuses.includes(extractedCriteria.status)) {
          delete extractedCriteria.status;
        }

        // Limpiar campos vacíos
        Object.keys(extractedCriteria).forEach(key => {
          const value = extractedCriteria[key];
          if (value === null || value === undefined || value === '' || 
              (Array.isArray(value) && value.length === 0)) {
            delete extractedCriteria[key];
          }
        });

        console.log("Criterios extraídos con AI:", extractedCriteria);
        
        return {
          success: true,
          criteria: extractedCriteria,
          originalText: args.searchText
        };

      } catch (parseError) {
        console.error("Error parseando respuesta de OpenAI:", parseError);
        console.error("Respuesta recibida:", response);
        
        // Fallback a extracción básica si falla el parsing
        return {
          success: false,
          error: "Error parseando criterios AI",
          fallbackToRegex: true
        };
      }

    } catch (error) {
      console.error("Error en extracción AI:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
        fallbackToRegex: true
      };
    }
  },
});

// Función auxiliar para limpiar y validar criterios
export function validateAndCleanCriteria(criteria: any) {
  const cleaned: any = {};
  
  // Validar type
  const validTypes = ['apartment', 'house', 'office', 'land', 'commercial'];
  if (criteria.type && validTypes.includes(criteria.type)) {
    cleaned.type = criteria.type;
  }
  
  // Validar status
  const validStatuses = ['for_sale', 'for_rent'];
  if (criteria.status && validStatuses.includes(criteria.status)) {
    cleaned.status = criteria.status;
  }
  
  // Validar strings
  if (criteria.city && typeof criteria.city === 'string' && criteria.city.trim()) {
    cleaned.city = criteria.city.trim();
  }
  
  // Validar números
  const numericFields = ['minPrice', 'maxPrice', 'bedrooms', 'bathrooms', 'minArea', 'maxArea'];
  numericFields.forEach(field => {
    if (criteria[field] && typeof criteria[field] === 'number' && criteria[field] > 0) {
      cleaned[field] = criteria[field];
    }
  });
  
  // Validar arrays
  const arrayFields = ['amenities', 'keywords', 'zones', 'searchTerms'];
  arrayFields.forEach(field => {
    if (criteria[field] && Array.isArray(criteria[field]) && criteria[field].length > 0) {
      // Filtrar elementos vacíos
      const filteredArray = criteria[field].filter((item: any) => 
        item && typeof item === 'string' && item.trim()
      );
      if (filteredArray.length > 0) {
        cleaned[field] = filteredArray;
      }
    }
  });
  
  return cleaned;
}

// Generar embedding para una propiedad
export const generatePropertyEmbedding = internalAction({
  args: {
    propertyId: v.id("properties")
  },
  handler: async (ctx, { propertyId }): Promise<{ success: boolean; error?: string; embeddingLength?: number; text?: string }> => {
    if (!openai) {
      return { success: false, error: "OpenAI no disponible" };
    }

    // Obtener la propiedad
    const property: any = await ctx.runQuery(internal.properties.getPropertyInternal, { id: propertyId });
    if (!property) {
      return { success: false, error: "Propiedad no encontrada" };
    }

    try {
      // Crear texto descriptivo completo
      const propertyText: string = [
        property.title,
        property.description,
        `Tipo: ${property.type}`,
        `Ciudad: ${property.city}`,
        `Estado: ${property.status}`,
        `Precio: ${property.price} ${property.currency}`,
        `${property.bedrooms} habitaciones`,
        `${property.bathrooms} baños`,
        `${property.area} m²`,
        property.amenities?.join(', ') || '',
        property.address || ''
      ].filter(Boolean).join('. ');

      console.log(`Generando embedding para: ${property.title}`);

      // Generar embedding
      const response = await openai.embeddings.create({
        model: "text-embedding-3-small",
        input: propertyText,
      });

      const embedding = response.data[0].embedding;

      // Guardar embedding en la propiedad
      await ctx.runMutation(internal.properties.updatePropertyEmbedding, {
        id: propertyId,
        embedding: embedding,
        embeddingText: propertyText
      });

      return { 
        success: true, 
        embeddingLength: embedding.length,
        text: propertyText 
      };

    } catch (error) {
      console.error("Error generando embedding:", error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : "Error desconocido" 
      };
    }
  }
});

// Generar embeddings para todas las propiedades
export const generateAllPropertyEmbeddings = internalAction({
  handler: async (ctx): Promise<{ success: boolean; error?: string; totalProperties?: number; successCount?: number; errorCount?: number }> => {
    if (!openai) {
      return { success: false, error: "OpenAI no disponible" };
    }

    // Obtener todas las propiedades activas
    const properties: any[] = await ctx.runQuery(internal.properties.getAllProperties);
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const property of properties) {
      try {
        const result = await ctx.runAction(internal.openaiExtraction.generatePropertyEmbedding, {
          propertyId: property._id
        });
        
        if (result.success) {
          successCount++;
          console.log(`✅ Embedding generado: ${property.title}`);
        } else {
          errorCount++;
          console.log(`❌ Error embedding: ${property.title} - ${result.error}`);
        }
        
        // Pausa para evitar rate limits
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        errorCount++;
        console.log(`❌ Error embedding: ${property.title} - ${error}`);
      }
    }

    return {
      success: true,
      totalProperties: properties.length,
      successCount,
      errorCount
    };
  }
});

// Búsqueda semántica real usando embeddings
export const semanticSearch = internalAction({
  args: {
    searchText: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, { searchText, limit = 10 }): Promise<{ success: boolean; error?: string; properties?: any[]; searchText?: string; totalFound?: number }> => {
    if (!openai) {
      return { success: false, error: "OpenAI no disponible" };
    }

    try {
      console.log(`🔍 Búsqueda semántica: "${searchText}"`);

      // Generar embedding para la búsqueda
      const searchResponse = await openai.embeddings.create({
        model: "text-embedding-3-small",
        input: searchText,
      });

      const searchEmbedding = searchResponse.data[0].embedding;

      // Obtener todas las propiedades con embeddings
      const properties: any[] = await ctx.runQuery(internal.properties.getPropertiesWithEmbeddings);

      if (properties.length === 0) {
        return { 
          success: false, 
          error: "No hay propiedades con embeddings. Ejecuta generateAllPropertyEmbeddings primero." 
        };
      }

      // Calcular similitud coseno con cada propiedad
      const propertiesWithScores: any[] = properties.map((property: any) => {
        if (!property.embedding) return null;
        
        const similarity = cosineSimilarity(searchEmbedding, property.embedding);
        
        return {
          ...property,
          similarityScore: similarity
        };
      }).filter(Boolean);

      // Ordenar por similitud (mayor a menor)
      propertiesWithScores.sort((a: any, b: any) => (b?.similarityScore || 0) - (a?.similarityScore || 0));

      // Tomar los top resultados
      const topResults: any[] = propertiesWithScores.slice(0, limit);

      console.log(`📊 Devolviendo ${topResults.length} resultados (top ${Math.min(3, topResults.length)}):`);
      topResults.slice(0, 3).forEach((prop: any, index: number) => {
        console.log(`${index + 1}. ${prop?.title} (score: ${prop?.similarityScore?.toFixed(3)})`);
      });

      return {
        success: true,
        properties: topResults,
        searchText: searchText,
        totalFound: propertiesWithScores.length
      };

    } catch (error) {
      console.error("Error en búsqueda semántica:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      };
    }
  }
});

// Función para calcular similitud coseno
function cosineSimilarity(vecA: number[], vecB: number[]): number {
  if (vecA.length !== vecB.length) {
    throw new Error("Los vectores deben tener la misma dimensión");
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < vecA.length; i++) {
    dotProduct += vecA[i] * vecB[i];
    normA += vecA[i] * vecA[i];
    normB += vecB[i] * vecB[i];
  }

  normA = Math.sqrt(normA);
  normB = Math.sqrt(normB);

  if (normA === 0 || normB === 0) {
    return 0;
  }

  return dotProduct / (normA * normB);
} 