import { mutation, query, internalQuery, action, internalAction, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { PROPERTY_TYPES, PROPERTY_STATUS } from "./schema";
import { validateAndCleanCriteria } from "./openaiExtraction";
import { internal, api } from "./_generated/api";
import * as openaiExtraction from "./openaiExtraction";

// Crear propiedad
export const createProperty = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    price: v.number(),
    currency: v.string(),
    type: v.union(
      v.literal(PROPERTY_TYPES.HOUSE),
      v.literal(PROPERTY_TYPES.APARTMENT),
      v.literal(PROPERTY_TYPES.OFFICE),
      v.literal(PROPERTY_TYPES.LAND),
      v.literal(PROPERTY_TYPES.COMMERCIAL)
    ),
    status: v.union(
      v.literal(PROPERTY_STATUS.FOR_SALE),
      v.literal(PROPERTY_STATUS.FOR_RENT),
      v.literal(PROPERTY_STATUS.SOLD),
      v.literal(PROPERTY_STATUS.RENTED),
      v.literal(PROPERTY_STATUS.DRAFT)
    ),
    address: v.string(),
    city: v.string(),
    state: v.string(),
    country: v.string(),
    zipCode: v.optional(v.string()),
    coordinates: v.optional(v.object({
      lat: v.number(),
      lng: v.number()
    })),
    bedrooms: v.optional(v.number()),
    bathrooms: v.optional(v.number()),
    area: v.number(),
    builtYear: v.optional(v.number()),
    parking: v.optional(v.number()),
    amenities: v.optional(v.array(v.string())),
    images: v.array(v.string()),
    virtualTour: v.optional(v.string()),
    floorPlan: v.optional(v.string()),
    ownerId: v.string(),
    agentId: v.optional(v.string()),
    slug: v.optional(v.string()),
    featured: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();
    
    const propertyId = await ctx.db.insert("properties", {
      ...args,
      createdAt: now,
      updatedAt: now,
      publishedAt: args.status !== PROPERTY_STATUS.DRAFT ? now : undefined,
    });

    // 🤖 AUTO-GENERAR EMBEDDING para la nueva propiedad
    console.log(`🔄 Generando embedding automático para nueva propiedad: ${args.title}`);
    
    // Programar generación de embedding de forma asíncrona para no bloquear la creación  
    ctx.scheduler.runAfter(0, internal.openaiExtraction.generatePropertyEmbedding, {
      propertyId: propertyId
    });

    return propertyId;
  },
});

// Obtener propiedades con filtros
export const getProperties = query({
  args: {
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    type: v.optional(v.string()),
    status: v.optional(v.string()),
    city: v.optional(v.string()),
    minPrice: v.optional(v.number()),
    maxPrice: v.optional(v.number()),
    featured: v.optional(v.boolean()),
    ownerId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Obtener todas las propiedades primero
    const allProperties = await ctx.db.query("properties").collect();

    // Aplicar filtros
    let filteredProperties = allProperties.filter(property => {
      // Filtros básicos
      if (args.type && property.type !== args.type) return false;
      if (args.status && property.status !== args.status) return false;
      if (args.city && !property.city.toLowerCase().includes(args.city.toLowerCase())) return false;
      if (args.minPrice !== undefined && property.price < args.minPrice) return false;
      if (args.maxPrice !== undefined && property.price > args.maxPrice) return false;
      if (args.ownerId && property.ownerId !== args.ownerId) return false;
      
      // Filtro de featured (considerando expiración)
      if (args.featured !== undefined) {
        const isCurrentlyFeatured = property.featured && 
                                   property.featuredUntil && 
                                   property.featuredUntil > now;
        if (args.featured !== isCurrentlyFeatured) return false;
      }

      // Solo propiedades publicadas por defecto (excepto si es el owner)
      if (!args.ownerId && property.status === PROPERTY_STATUS.DRAFT) return false;

      return true;
    });

    // Ordenamiento con prioridad: Premium Home > Destacadas > Normales
    filteredProperties.sort((a, b) => {
      // Prioridad 1: Premium home activo
      const aPremium = a.premiumHomeUntil && a.premiumHomeUntil > now;
      const bPremium = b.premiumHomeUntil && b.premiumHomeUntil > now;
      
      if (aPremium && !bPremium) return -1;
      if (!aPremium && bPremium) return 1;
      
      // Prioridad 2: Destacadas activas
      const aFeatured = a.featured && a.featuredUntil && a.featuredUntil > now;
      const bFeatured = b.featured && b.featuredUntil && b.featuredUntil > now;
      
      if (aFeatured && !bFeatured) return -1;
      if (!aFeatured && bFeatured) return 1;
      
      // Prioridad 3: Fecha de creación (más recientes primero)
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    // Aplicar offset y limit
    const startIndex = args.offset || 0;
    const endIndex = startIndex + (args.limit || 20);
    
    return filteredProperties.slice(startIndex, endIndex);
  },
});

// Obtener propiedad por ID
export const getPropertyById = query({
  args: { id: v.id("properties") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Obtener propiedades del usuario (como propietario Y como agente)
export const getUserProperties = query({
  args: { 
    userId: v.string(),
    status: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Obtener propiedades donde el usuario es propietario
    let ownerQuery = ctx.db.query("properties")
      .withIndex("by_owner", (q) => q.eq("ownerId", args.userId));

    if (args.status) {
      ownerQuery = ownerQuery.filter((q) => q.eq(q.field("status"), args.status));
    }

    const ownerProperties = await ownerQuery.order("desc").collect();

    // Obtener propiedades donde el usuario es agente
    let agentQuery = ctx.db.query("properties")
      .withIndex("by_agent", (q) => q.eq("agentId", args.userId));

    if (args.status) {
      agentQuery = agentQuery.filter((q) => q.eq(q.field("status"), args.status));
    }

    const agentProperties = await agentQuery.order("desc").collect();

    // Combinar ambas listas evitando duplicados
    const propertyIds = new Set();
    const allProperties = [];

    // Agregar propiedades como propietario
    for (const prop of ownerProperties) {
      propertyIds.add(prop._id);
      allProperties.push({
        ...prop,
        relationshipType: 'owner' as const
      });
    }

    // Agregar propiedades como agente (si no están ya incluidas)
    for (const prop of agentProperties) {
      if (!propertyIds.has(prop._id)) {
        allProperties.push({
          ...prop,
          relationshipType: 'agent' as const
        });
      }
    }

    // Ordenar por fecha de creación descendente
    allProperties.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    return allProperties;
  },
});

// Actualizar propiedad
export const updateProperty = mutation({
  args: {
    id: v.id("properties"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    price: v.optional(v.number()),
    currency: v.optional(v.string()),
    type: v.optional(v.union(
      v.literal(PROPERTY_TYPES.HOUSE),
      v.literal(PROPERTY_TYPES.APARTMENT),
      v.literal(PROPERTY_TYPES.OFFICE),
      v.literal(PROPERTY_TYPES.LAND),
      v.literal(PROPERTY_TYPES.COMMERCIAL)
    )),
    status: v.optional(v.union(
      v.literal(PROPERTY_STATUS.FOR_SALE),
      v.literal(PROPERTY_STATUS.FOR_RENT),
      v.literal(PROPERTY_STATUS.SOLD),
      v.literal(PROPERTY_STATUS.RENTED),
      v.literal(PROPERTY_STATUS.DRAFT)
    )),
    address: v.optional(v.string()),
    city: v.optional(v.string()),
    state: v.optional(v.string()),
    country: v.optional(v.string()),
    zipCode: v.optional(v.string()),
    coordinates: v.optional(v.object({
      lat: v.number(),
      lng: v.number()
    })),
    bedrooms: v.optional(v.number()),
    bathrooms: v.optional(v.number()),
    area: v.optional(v.number()),
    builtYear: v.optional(v.number()),
    parking: v.optional(v.number()),
    amenities: v.optional(v.array(v.string())),
    images: v.optional(v.array(v.string())),
    virtualTour: v.optional(v.string()),
    floorPlan: v.optional(v.string()),
    agentId: v.optional(v.string()),
    slug: v.optional(v.string()),
    featured: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado para editar una propiedad");
    }

    const { id, ...updates } = args;
    
    // Verificar que la propiedad existe
    const property = await ctx.db.get(id);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    // Verificar permisos: debe ser el propietario O el agente asignado
    const userId = identity.subject;
    const isOwner = property.ownerId === userId;
    const isAgent = property.agentId === userId;
    
    if (!isOwner && !isAgent) {
      throw new Error("No tienes permisos para editar esta propiedad");
    }
    
    // Actualizar publishedAt si cambia de draft a publicado
    if (updates.status && updates.status !== PROPERTY_STATUS.DRAFT) {
      if (property?.status === PROPERTY_STATUS.DRAFT) {
        (updates as any).publishedAt = new Date().toISOString();
      }
    }

    await ctx.db.patch(id, {
      ...updates,
      updatedAt: new Date().toISOString(),
    });

    // 🤖 AUTO-REGENERAR EMBEDDING al actualizar propiedad
    console.log(`🔄 Regenerando embedding automático para propiedad actualizada: ${property.title}`);
    
    // Programar regeneración de embedding de forma asíncrona para no bloquear la actualización
    ctx.scheduler.runAfter(0, internal.openaiExtraction.generatePropertyEmbedding, {
      propertyId: id
    });

    return await ctx.db.get(id);
  },
});

// Eliminar propiedad
export const deleteProperty = mutation({
  args: { id: v.id("properties") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado para eliminar una propiedad");
    }

    // Verificar que la propiedad existe
    const property = await ctx.db.get(args.id);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    // Verificar permisos: debe ser el propietario O el agente asignado
    const userId = identity.subject;
    const isOwner = property.ownerId === userId;
    const isAgent = property.agentId === userId;
    
    if (!isOwner && !isAgent) {
      throw new Error("No tienes permisos para eliminar esta propiedad");
    }

    await ctx.db.delete(args.id);
  },
});

// Buscar propiedades por texto
export const searchProperties = query({
  args: {
    searchTerm: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const allProperties = await ctx.db.query("properties")
      .filter((q) => q.neq(q.field("status"), PROPERTY_STATUS.DRAFT))
      .collect();

    const searchLower = args.searchTerm.toLowerCase();
    
    const filtered = allProperties.filter(property => 
      property.title.toLowerCase().includes(searchLower) ||
      property.description.toLowerCase().includes(searchLower) ||
      property.address.toLowerCase().includes(searchLower) ||
      property.city.toLowerCase().includes(searchLower) ||
      property.state.toLowerCase().includes(searchLower)
    );

    return filtered.slice(0, args.limit || 20);
  },
});

// Buscar propiedades por amenidades (para IA)
export const searchPropertiesByAmenities = query({
  args: { 
    amenityKeywords: v.optional(v.array(v.string())),
    requiredAmenities: v.optional(v.array(v.string())),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let properties = await ctx.db.query("properties")
      .filter((q) => q.neq(q.field("status"), PROPERTY_STATUS.DRAFT))
      .collect();
    
    // Filtrar por amenidades requeridas exactas
    if (args.requiredAmenities?.length) {
      properties = properties.filter(property => 
        args.requiredAmenities!.every(required => 
          property.amenities?.includes(required)
        )
      );
    }
    
    // Filtrar por palabras clave de amenidades (búsqueda semántica)
    if (args.amenityKeywords?.length) {
      // Obtener amenidades que coincidan con las palabras clave
      const amenities = await ctx.db.query("amenities")
        .withIndex("by_active", (q) => q.eq("isActive", true))
        .collect();
      
      const matchingAmenities = amenities.filter(amenity => 
        args.amenityKeywords!.some(keyword => 
          amenity.searchKeywords.some(searchKey => 
            searchKey.toLowerCase().includes(keyword.toLowerCase())
          ) || amenity.name.toLowerCase().includes(keyword.toLowerCase())
        )
      ).map(a => a.name);
      
      if (matchingAmenities.length > 0) {
        properties = properties.filter(property => 
          matchingAmenities.some(amenity => 
            property.amenities?.includes(amenity)
          )
        );
      }
    }
    
    return properties.slice(0, args.limit || 20);
  },
});

// Búsqueda avanzada combinada (texto + amenidades + filtros)
export const advancedSearchProperties = query({
  args: {
    searchTerm: v.optional(v.string()),
    type: v.optional(v.string()),
    status: v.optional(v.string()),
    city: v.optional(v.string()),
    minPrice: v.optional(v.number()),
    maxPrice: v.optional(v.number()),
    minArea: v.optional(v.number()),
    maxArea: v.optional(v.number()),
    bedrooms: v.optional(v.number()),
    bathrooms: v.optional(v.number()),
    amenityKeywords: v.optional(v.array(v.string())),
    requiredAmenities: v.optional(v.array(v.string())),
    featured: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let properties = await ctx.db.query("properties")
      .filter((q) => q.neq(q.field("status"), PROPERTY_STATUS.DRAFT))
      .collect();

    // Filtros básicos
    if (args.type) {
      properties = properties.filter(p => p.type === args.type);
    }
    if (args.status) {
      properties = properties.filter(p => p.status === args.status);
    }
    if (args.city) {
      properties = properties.filter(p => 
        p.city.toLowerCase().includes(args.city!.toLowerCase())
      );
    }
    if (args.minPrice !== undefined) {
      properties = properties.filter(p => p.price >= args.minPrice!);
    }
    if (args.maxPrice !== undefined) {
      properties = properties.filter(p => p.price <= args.maxPrice!);
    }
    if (args.minArea !== undefined) {
      properties = properties.filter(p => p.area >= args.minArea!);
    }
    if (args.maxArea !== undefined) {
      properties = properties.filter(p => p.area <= args.maxArea!);
    }
    if (args.bedrooms !== undefined) {
      properties = properties.filter(p => (p.bedrooms || 0) >= args.bedrooms!);
    }
    if (args.bathrooms !== undefined) {
      properties = properties.filter(p => (p.bathrooms || 0) >= args.bathrooms!);
    }
    if (args.featured !== undefined) {
      properties = properties.filter(p => p.featured === args.featured);
    }

    // Búsqueda por texto
    if (args.searchTerm) {
      const searchLower = args.searchTerm.toLowerCase();
      properties = properties.filter(property => 
        property.title.toLowerCase().includes(searchLower) ||
        property.description.toLowerCase().includes(searchLower) ||
        property.address.toLowerCase().includes(searchLower) ||
        property.city.toLowerCase().includes(searchLower) ||
        property.state.toLowerCase().includes(searchLower)
      );
    }

    // Filtrar por amenidades requeridas exactas
    if (args.requiredAmenities?.length) {
      properties = properties.filter(property => 
        args.requiredAmenities!.every(required => 
          property.amenities?.includes(required)
        )
      );
    }
    
    // Filtrar por palabras clave de amenidades (búsqueda semántica)
    if (args.amenityKeywords?.length) {
      const amenities = await ctx.db.query("amenities")
        .withIndex("by_active", (q) => q.eq("isActive", true))
        .collect();
      
      const matchingAmenities = amenities.filter(amenity => 
        args.amenityKeywords!.some(keyword => 
          amenity.searchKeywords.some(searchKey => 
            searchKey.toLowerCase().includes(keyword.toLowerCase())
          ) || amenity.name.toLowerCase().includes(keyword.toLowerCase())
        )
      ).map(a => a.name);
      
      if (matchingAmenities.length > 0) {
        properties = properties.filter(property => 
          matchingAmenities.some(amenity => 
            property.amenities?.includes(amenity)
          )
        );
      }
    }

    // ✅ AGREGAR ORDENAMIENTO: Premium Home > Destacadas > Normales
    const now = Date.now();
    
    properties.sort((a, b) => {
      // Prioridad 1: Premium home activo
      const aPremium = a.premiumHomeUntil && a.premiumHomeUntil > now;
      const bPremium = b.premiumHomeUntil && b.premiumHomeUntil > now;
      
      if (aPremium && !bPremium) return -1;
      if (!aPremium && bPremium) return 1;
      
      // Prioridad 2: Destacadas activas
      const aFeatured = a.featured && a.featuredUntil && a.featuredUntil > now;
      const bFeatured = b.featured && b.featuredUntil && b.featuredUntil > now;
      
      if (aFeatured && !bFeatured) return -1;
      if (!aFeatured && bFeatured) return 1;
      
      // Prioridad 3: Fecha de creación (más recientes primero)
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
    
    return properties.slice(0, args.limit || 20);
  },
});

// Migración: Agregar amenidades a propiedades existentes
export const addAmenitiesToExistingProperties = mutation({
  handler: async (ctx) => {
    const properties = await ctx.db.query("properties").collect();
    
    const amenitiesOptions = [
      ['Wi-Fi', 'Seguridad', 'Aire Acondicionado'],
      ['Wi-Fi', 'Ascensor', 'Balcón', 'Seguridad'],
      ['Wi-Fi', 'Aire Acondicionado', 'Gimnasio', 'Piscina', 'Seguridad'],
      ['Wi-Fi', 'Vista al Mar', 'Terraza', 'Seguridad'],
      ['Wi-Fi', 'Cerca Metro', 'Ascensor', 'Seguridad'],
      ['Wi-Fi', 'Jardín', 'Lavandería', 'Calefacción', 'Seguridad']
    ];
    
    for (const property of properties) {
      if (!property.amenities) {
        const randomAmenities = amenitiesOptions[Math.floor(Math.random() * amenitiesOptions.length)];
        
        await ctx.db.patch(property._id, {
          amenities: randomAmenities,
          updatedAt: new Date().toISOString()
        });
      }
    }
    
    return { updated: properties.length };
  },
});

// Función para agregar/quitar favorito
export const toggleFavorite = mutation({
  args: {
    propertyId: v.id("properties"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado para agregar favoritos");
    }

    // Verificar si ya existe el favorito
    const existingFavorite = await ctx.db
      .query("favorites")
      .withIndex("by_user_property", (q) => 
        q.eq("userId", identity.subject).eq("propertyId", args.propertyId)
      )
      .unique();

    if (existingFavorite) {
      // Quitar de favoritos
      await ctx.db.delete(existingFavorite._id);
      return { action: "removed", isFavorite: false };
    } else {
      // Agregar a favoritos
      await ctx.db.insert("favorites", {
        userId: identity.subject,
        propertyId: args.propertyId,
        createdAt: new Date().toISOString(),
      });
      return { action: "added", isFavorite: true };
    }
  },
});

// Función para verificar si una propiedad es favorita
export const isFavorite = query({
  args: {
    propertyId: v.id("properties"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return false;
    }

    const favorite = await ctx.db
      .query("favorites")
      .withIndex("by_user_property", (q) => 
        q.eq("userId", identity.subject).eq("propertyId", args.propertyId)
      )
      .unique();

    return !!favorite;
  },
});

// Función para obtener los favoritos del usuario
export const getUserFavorites = query({
  args: {
    paginationOpts: v.object({
      numItems: v.number(),
      cursor: v.optional(v.union(v.string(), v.null())),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return { page: [], isDone: true, continueCursor: "" };
    }

    // Mapear paginationOpts para asegurar compatibilidad de tipos
    const paginationOpts = {
      numItems: args.paginationOpts.numItems,
      cursor: args.paginationOpts.cursor ?? null,
    };

    // Obtener favoritos del usuario
    const favoritesQuery = await ctx.db
      .query("favorites")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .order("desc")
      .paginate(paginationOpts);

    // Obtener las propiedades correspondientes
    const properties = await Promise.all(
      favoritesQuery.page.map(async (favorite) => {
        const property = await ctx.db.get(favorite.propertyId);
        if (!property) return null;

        // Obtener información del propietario
        const owner = await ctx.db
          .query("users")
          .withIndex("by_token", (q) => q.eq("tokenIdentifier", property.ownerId))
          .unique();

        return {
          ...property,
          owner: owner ? {
            name: owner.name || "Usuario",
            avatar: owner.avatar,
            company: owner.company,
            phone: owner.phone,
          } : null,
          addedToFavoritesAt: favorite.createdAt,
        };
      })
    );

    // Filtrar propiedades nulas
    const validProperties = properties.filter(p => p !== null);

    return {
      page: validProperties,
      isDone: favoritesQuery.isDone,
      continueCursor: favoritesQuery.continueCursor,
    };
  },
});

// Función para contar favoritos del usuario
export const getUserFavoritesCount = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return 0;
    }

    const favorites = await ctx.db
      .query("favorites")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .collect();

    return favorites.length;
  },
});

// Función para extraer criterios específicos del texto de búsqueda
function extractSearchCriteria(searchText: string) {
  const text = searchText.toLowerCase();
  const criteria: any = {};
  
  // Extraer precio (200k, 200 mil, $200,000, etc.)
  const pricePatterns = [
    /(\d+)k/g,                           // 200k
    /(\d+)\s*mil/g,                      // 200 mil
    /(\d+)\s*thousand/g,                 // 200 thousand
    /\$?\s*(\d{1,3}(?:,\d{3})+)/g,      // $200,000 o 200,000
    /hasta\s+(\d+)/g,                    // hasta 200
    /máximo\s+(\d+)/g,                   // máximo 200
    /presupuesto\s+(\d+)/g,              // presupuesto 200
  ];
  
  for (const pattern of pricePatterns) {
    const matches = Array.from(text.matchAll(pattern));
    if (matches.length > 0) {
      let price = parseInt(matches[0][1]);
      // Convertir K a miles
      if (pattern.source.includes('k')) {
        price = price * 1000;
      }
      criteria.maxPrice = price;
      break;
    }
  }
  
  // Extraer número de cuartos/habitaciones/dormitorios
  const bedroomPatterns = [
    /(\d+)\s*cuartos?/g,
    /(\d+)\s*habitacion(es)?/g,
    /(\d+)\s*dormitorios?/g,
    /(\d+)\s*bedroom(s)?/g,
    /(\d+)\s*hab/g,
  ];
  
  for (const pattern of bedroomPatterns) {
    const match = pattern.exec(text);
    if (match) {
      criteria.bedrooms = parseInt(match[1]);
      break;
    }
  }
  
  // Extraer número de baños
  const bathroomPatterns = [
    /(\d+)\s*baños?/g,
    /(\d+)\s*banos?/g,  // Para cuando escriben mal
    /(\d+)\s*bathroom(s)?/g,
  ];
  
  for (const pattern of bathroomPatterns) {
    const match = pattern.exec(text);
    if (match) {
      criteria.bathrooms = parseInt(match[1]);
      break;
    }
  }
  
  // Extraer zonas específicas (devolver como strings)
  const zonePatterns = [
    /zona\s*(\d+)/g,
    /zone\s*(\d+)/g,
    /z\s*(\d+)/g,         // Para "z 10", "z10"
    /z(\d+)/g,            // Para "z10" sin espacio
  ];
  
  const zones: string[] = [];
  for (const pattern of zonePatterns) {
    const matches = Array.from(text.matchAll(pattern));
    matches.forEach(match => {
      zones.push(match[1]);
    });
  }
  if (zones.length > 0) {
    criteria.zones = zones;
  }
  
  // Extraer ciudades/áreas específicas de Guatemala
  const cities: string[] = [];
  const cityPatterns = [
    'mixco', 'zona rosa', 'antigua', 'escuintla', 'quetzaltenango', 
    'xela', 'chimaltenango', 'villa nueva', 'amatitlán',
    'carretera salvador', 'roosevelt', 'guatemala'
  ];
  
  cityPatterns.forEach(city => {
    if (text.includes(city)) {
      cities.push(city);
    }
  });
  
  if (cities.length > 0) {
    criteria.cities = cities;
  }
  
  // Extraer tipo de propiedad
  if (text.includes('casa') || text.includes('house')) {
    criteria.type = 'house';
  } else if (text.includes('apartamento') || text.includes('apartment') || text.includes('depto')) {
    criteria.type = 'apartment';
  } else if (text.includes('oficina') || text.includes('office')) {
    criteria.type = 'office';
  }
  
  // Extraer status (venta/alquiler)
  if (text.includes('comprar') || text.includes('venta') || text.includes('buy')) {
    criteria.status = 'for_sale';
  } else if (text.includes('alquiler') || text.includes('rentar') || text.includes('rent')) {
    criteria.status = 'for_rent';
  }
  
  // Extraer amenidades básicas
  const amenities: string[] = [];
  if (text.includes('piscina') || text.includes('pisina')) amenities.push('piscina');
  if (text.includes('gimnasio')) amenities.push('gimnasio');
  if (text.includes('jardín') || text.includes('jardin')) amenities.push('jardín');
  if (text.includes('balcón') || text.includes('balcon')) amenities.push('balcón');
  if (text.includes('parking') || text.includes('garage')) amenities.push('parking');
  if (text.includes('ascensor') || text.includes('elevador')) amenities.push('ascensor');
  if (text.includes('seguridad')) amenities.push('seguridad');
  
  if (amenities.length > 0) {
    criteria.amenities = amenities;
  }
  
  return criteria;
}

// Función interna para búsqueda de propiedades (para el agente de WhatsApp)
export const searchForAgent = internalQuery({
  args: {
    searchCriteria: v.object({
      type: v.optional(v.string()),
      status: v.optional(v.string()),
      city: v.optional(v.string()),
      minPrice: v.optional(v.union(v.number(), v.string())),
      maxPrice: v.optional(v.union(v.number(), v.string())),
      bedrooms: v.optional(v.number()),
      bathrooms: v.optional(v.number()),
      minArea: v.optional(v.number()),
      maxArea: v.optional(v.number()),
      amenities: v.optional(v.array(v.string())),
      keywords: v.optional(v.array(v.string())),
      zones: v.optional(v.array(v.string())),
      searchTerms: v.optional(v.array(v.string())),
      searchText: v.optional(v.string())
    })
  },
  handler: async (ctx, { searchCriteria }) => {
    // Si hay búsqueda por texto, hacer búsqueda directa
    if (searchCriteria.searchText) {
      console.log("Búsqueda por texto:", searchCriteria.searchText);
      
      // Extraer criterios específicos del texto usando función básica (fallback regex)
      const extractedCriteria = extractSearchCriteria(searchCriteria.searchText);
      console.log("Criterios extraídos:", extractedCriteria);
      
      // Obtener todas las propiedades publicadas SOLO DE GUATEMALA
      let properties = await ctx.db.query("properties")
        .filter((q) => q.neq(q.field("status"), "draft"))
        .filter((q) => q.eq(q.field("country"), "Guatemala"))
        .collect();
      
      console.log(`Total propiedades encontradas: ${properties.length}`);
      
      // Mostrar algunas propiedades de ejemplo para debug
      if (properties.length > 0) {
        console.log("Ejemplos de propiedades:");
        properties.slice(0, 3).forEach(p => {
          console.log(`- ${p.title} | ${p.type} | ${p.city} | ${p.status}`);
        });
      }
      
      // Aplicar búsqueda por texto
      const searchLower = searchCriteria.searchText.toLowerCase();
      console.log(`Buscando: "${searchLower}"`);
      
      // Dividir la búsqueda en palabras clave
      const searchWords = searchLower.split(' ').filter(word => word.length > 2);
      console.log(`Palabras clave: ${searchWords.join(', ')}`);
      
      properties = properties.filter(property => {
        // 1. Aplicar filtros específicos extraídos
        
        // Filtro de precio máximo
        if (extractedCriteria.maxPrice && property.price > extractedCriteria.maxPrice) {
          return false;
        }
        
        // Filtro de habitaciones
        if (extractedCriteria.bedrooms && (property.bedrooms || 0) < extractedCriteria.bedrooms) {
          return false;
        }
        
        // Filtro de baños
        if (extractedCriteria.bathrooms && (property.bathrooms || 0) < extractedCriteria.bathrooms) {
          return false;
        }
        
        // Filtro de tipo de propiedad
        if (extractedCriteria.type && property.type !== extractedCriteria.type) {
          return false;
        }
        
        // Filtro de status
        if (extractedCriteria.status && property.status !== extractedCriteria.status) {
          return false;
        }
        
        // Filtro de zonas específicas
        if (extractedCriteria.zones && extractedCriteria.zones.length > 0) {
          const searchableText = [property.title, property.address, property.city].join(' ').toLowerCase();
          const zoneMatch = extractedCriteria.zones.some((zone: string) => 
            searchableText.includes(zone.toLowerCase()) ||
            searchableText.includes(`zona ${zone}`) || 
            searchableText.includes(`zone ${zone}`)
          );
          if (!zoneMatch) {
            return false;
          }
        }

        // Usar las zonas también del searchCriteria (nuevos campos)
        if (searchCriteria.zones && searchCriteria.zones.length > 0) {
          const searchableText = [property.title, property.address, property.city].join(' ').toLowerCase();
          const zoneMatch = searchCriteria.zones.some((zone: string) => 
            searchableText.includes(zone.toLowerCase()) ||
            searchableText.includes(`zona ${zone}`) || 
            searchableText.includes(`zone ${zone}`)
          );
          if (!zoneMatch) {
            return false;
          }
        }

        // Filtro de amenidades específicas
        if (searchCriteria.amenities && searchCriteria.amenities.length > 0) {
          const propertyAmenities = (property.amenities || []).map(a => a.toLowerCase());
          const hasRequiredAmenities = searchCriteria.amenities.some((requiredAmenity: string) => 
            propertyAmenities.some(propAmenity => 
              propAmenity.includes(requiredAmenity.toLowerCase()) ||
              requiredAmenity.toLowerCase().includes(propAmenity)
            )
          );
          if (!hasRequiredAmenities) {
            return false;
          }
        }

        // Filtro de habitaciones específicas
        if (searchCriteria.bedrooms && (property.bedrooms || 0) < searchCriteria.bedrooms) {
          return false;
        }

        // Filtro de baños específicos
        if (searchCriteria.bathrooms && (property.bathrooms || 0) < searchCriteria.bathrooms) {
          return false;
        }

        // Filtro de área específica
        if (searchCriteria.minArea && property.area < searchCriteria.minArea) {
          return false;
        }
        if (searchCriteria.maxArea && property.area > searchCriteria.maxArea) {
          return false;
        }
        
        // Filtro de amenidades
        if (extractedCriteria.amenities && extractedCriteria.amenities.length > 0) {
          const propertyAmenities = (property.amenities || []).map(a => a.toLowerCase());
          const hasRequiredAmenities = extractedCriteria.amenities.some((requiredAmenity: string) => 
            propertyAmenities.some(propAmenity => 
              propAmenity.includes(requiredAmenity.toLowerCase()) ||
              requiredAmenity.toLowerCase().includes(propAmenity)
            )
          );
          if (!hasRequiredAmenities) {
            return false;
          }
        }
        
        // Filtro de área
        if (extractedCriteria.minArea && property.area < extractedCriteria.minArea) {
          return false;
        }
        if (extractedCriteria.maxArea && property.area > extractedCriteria.maxArea) {
          return false;
        }
        
        // Filtro de ciudad
        if (extractedCriteria.city) {
          const cityMatch = property.city.toLowerCase().includes(extractedCriteria.city.toLowerCase());
          if (!cityMatch) {
            return false;
          }
        }
        
        // Filtro de ciudades múltiples
        if (extractedCriteria.cities && extractedCriteria.cities.length > 0) {
          const searchableText = [property.title, property.address, property.city].join(' ').toLowerCase();
          const cityMatch = extractedCriteria.cities.some((city: string) => 
            searchableText.includes(city.toLowerCase())
          );
          if (!cityMatch) {
            return false;
          }
        }
        
        // 2. Aplicar búsqueda por palabras clave (solo si no hay criterios específicos suficientes)
        // Si ya tenemos criterios específicos (type, status, etc), no aplicar filtro por palabras
        const hasCriteria = extractedCriteria.type || extractedCriteria.status || extractedCriteria.city;
        
        if (!hasCriteria) {
          const searchableText = [
            property.title,
            property.description,
            property.address,
            property.city,
            property.state,
            property.type
          ].join(' ').toLowerCase();
          
          // Buscar que al menos una palabra clave coincida
          const matches = searchWords.some(word => searchableText.includes(word));
          
          if (matches) {
            console.log(`Coincidencia encontrada: ${property.title} - ${property.city} - $${property.price}`);
          }
          
          return matches;
        }
        
        // Si tenemos criterios específicos, ya pasó todos los filtros arriba
        console.log(`Propiedad válida con criterios: ${property.title} - ${property.city} - $${property.price}`);
        return true;
      });
      
      // Ordenar: Premium Home > Destacadas > Normales
      const now = Date.now();
      properties.sort((a, b) => {
        // Prioridad 1: Premium home activo
        const aPremium = a.premiumHomeUntil && a.premiumHomeUntil > now;
        const bPremium = b.premiumHomeUntil && b.premiumHomeUntil > now;
        
        if (aPremium && !bPremium) return -1;
        if (!aPremium && bPremium) return 1;
        
        // Prioridad 2: Destacadas activas
        const aFeatured = a.featured && a.featuredUntil && a.featuredUntil > now;
        const bFeatured = b.featured && b.featuredUntil && b.featuredUntil > now;
        
        if (aFeatured && !bFeatured) return -1;
        if (!aFeatured && bFeatured) return 1;
        
        // Prioridad 3: Fecha de creación (más recientes primero)
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });
      
      // Limitar a 10 resultados para WhatsApp
      properties = properties.slice(0, 10);
      
      console.log(`Resultados finales: ${properties.length}`);
      
      // Mostrar filtros aplicados
      if (Object.keys(extractedCriteria).length > 0) {
        console.log("Filtros aplicados:", JSON.stringify(extractedCriteria, null, 2));
      }
      
      // Formatear para mantener compatibilidad
      const formattedProperties = properties.map((property, index) => ({
        id: property._id,
        index: index + 1,
        title: property.title,
        type: property.type,
        status: property.status,
        price: property.price,
        currency: property.currency,
        address: property.address,
        city: property.city,
        state: property.state,
        bedrooms: property.bedrooms || 0,
        bathrooms: property.bathrooms || 0,
        area: property.area,
        parking: property.parking || 0,
        amenities: property.amenities || [],
        images: property.images?.slice(0, 3) || [],
        featured: property.featured || false,
        description: property.description?.substring(0, 200) + "..." || "",
      }));

      return {
        properties: formattedProperties,
        total: formattedProperties.length
      };
    }
    
    // Función para limpiar valores que vienen con prefijo "=" de N8N y strings vacíos
    const cleanValue = (value: any) => {
      if (typeof value === 'string') {
        // Remover prefijo "=" de N8N
        const cleaned = value.startsWith('=') ? value.substring(1) : value;
        // Si está vacío después de limpiar, retornar undefined
        return cleaned === '' ? undefined : cleaned;
      }
      return value;
    };
    
    // Función para convertir strings a números
    const toNumber = (value: any, isInteger = false) => {
      if (typeof value === 'number') return value;
      if (typeof value === 'string') {
        const cleaned = cleanValue(value);
        if (cleaned === undefined) return undefined;
        const num = isInteger ? parseInt(cleaned) : parseFloat(cleaned);
        return isNaN(num) ? undefined : num;
      }
      return undefined;
    };
    
    // Limpiar criterios
    const cleanCriteria = {
      type: cleanValue(searchCriteria.type),
      status: cleanValue(searchCriteria.status),
      city: cleanValue(searchCriteria.city),
      minPrice: toNumber(searchCriteria.minPrice),
      maxPrice: toNumber(searchCriteria.maxPrice)
    };
    
    console.log("Criterios después de LIMPIAR:", cleanCriteria);
    
    // Convertir valores del español al inglés para compatibilidad con la base de datos
    const normalizedCriteria = { ...cleanCriteria };
    
    // Convertir type de español a inglés
    if (normalizedCriteria.type) {
      const typeMapping: { [key: string]: any } = {
        "casa": "house",
        "apartamento": "apartment",
        "oficina": "office", 
        "terreno": "land",
        "comercial": "commercial"
      };
      if (typeMapping[normalizedCriteria.type]) {
        normalizedCriteria.type = typeMapping[normalizedCriteria.type];
      }
    }
    
    // Convertir status de español a inglés
    if (normalizedCriteria.status) {
      const statusMapping: { [key: string]: any } = {
        "venta": "for_sale",
        "alquiler": "for_rent"
      };
      if (statusMapping[normalizedCriteria.status]) {
        normalizedCriteria.status = statusMapping[normalizedCriteria.status];
      }
    }
    
    console.log("Criterios después de NORMALIZAR:", normalizedCriteria);
    
    // Construir query dinámicamente
    let query = ctx.db.query("properties");
    
    // Solo buscar propiedades publicadas DE GUATEMALA
    query = query.filter((q) => q.neq(q.field("status"), "draft"))
      .filter((q) => q.eq(q.field("country"), "Guatemala"));
    
    // Aplicar filtros dinámicos
    if (normalizedCriteria.type) {
      query = query.filter((q) => q.eq(q.field("type"), normalizedCriteria.type));
    }
    
    if (normalizedCriteria.status) {
      query = query.filter((q) => q.eq(q.field("status"), normalizedCriteria.status));
    }
    
    if (normalizedCriteria.city) {
      // Para simplificar, filtraremos en memoria en lugar de usar query complejo
      // Esto es temporal hasta que podamos implementar búsqueda de texto completa
    }
    

    
    // Obtener resultados base sin filtros de precio (los aplicaremos en memoria)
    let results = await query
      .order("desc")
      .collect();

    // Filtrar por ciudad en memoria 
    if (normalizedCriteria.city) {
      const searchTerm = normalizedCriteria.city.toLowerCase();
      results = results.filter(property => {
        const cityMatch = property.city?.toLowerCase().includes(searchTerm);
        const titleMatch = property.title?.toLowerCase().includes(searchTerm);
        const addressMatch = property.address?.toLowerCase().includes(searchTerm);
        return cityMatch || titleMatch || addressMatch;
      });
    }

    // Filtrar por rango de precios en memoria
    if (normalizedCriteria.minPrice && normalizedCriteria.minPrice > 0) {
      results = results.filter(property => 
        property.price && property.price >= normalizedCriteria.minPrice!
      );
    }
    
    if (normalizedCriteria.maxPrice && normalizedCriteria.maxPrice > 0) {
      results = results.filter(property => 
        property.price && property.price <= normalizedCriteria.maxPrice!
      );
    }
    


    // Limitar a 10 resultados para WhatsApp
    results = results.slice(0, 10);

    // Formatear resultados para el agente
    const formattedProperties = results.map((property, index) => ({
      id: property._id,
      index: index + 1,
      title: property.title,
      type: property.type,
      status: property.status,
      price: property.price,
      currency: property.currency,
      address: property.address,
      city: property.city,
      state: property.state,
      bedrooms: property.bedrooms || 0,
      bathrooms: property.bathrooms || 0,
      area: property.area,
      parking: property.parking || 0,
      amenities: property.amenities || [],
      images: property.images?.slice(0, 3) || [], // Solo primeras 3 imágenes
      featured: property.featured || false,
      description: property.description?.substring(0, 200) + "..." || "",
    }));

    console.log(`Encontradas ${formattedProperties.length} propiedades`);
    
    return {
      properties: formattedProperties,
      total: formattedProperties.length
    };
  }
});

// Función interna para obtener detalles de una propiedad (para el agente de WhatsApp)
export const getDetailsForAgent = internalQuery({
  args: {
    propertyId: v.id("properties")
  },
  handler: async (ctx, { propertyId }) => {
    const property = await ctx.db.get(propertyId);
    
    if (!property) {
      return { property: null };
    }

    // Obtener información del propietario
    const owner = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("userId"), property.ownerId))
      .first();

    // Obtener información del agente si existe
    let agent = null;
    if (property.agentId) {
      agent = await ctx.db
        .query("users")
        .filter((q) => q.eq(q.field("userId"), property.agentId))
        .first();
    }

    const detailedProperty = {
      id: property._id,
      title: property.title,
      description: property.description,
      type: property.type,
      status: property.status,
      price: property.price,
      currency: property.currency,
      
      // Ubicación completa
      address: property.address,
      city: property.city,
      state: property.state,
      country: property.country,
      zipCode: property.zipCode,
      coordinates: property.coordinates,
      
      // Características
      bedrooms: property.bedrooms || 0,
      bathrooms: property.bathrooms || 0,
      area: property.area,
      builtYear: property.builtYear,
      parking: property.parking || 0,
      
      // Media
      images: property.images || [],
      virtualTour: property.virtualTour,
      floorPlan: property.floorPlan,
      
      // Amenidades
      amenities: property.amenities || [],
      
      // Contacto
      owner: owner ? {
        name: owner.name,
        email: owner.email,
        phone: owner.phone,
        company: owner.company
      } : null,
      
      agent: agent ? {
        name: agent.name,
        email: agent.email,
        phone: agent.phone,
        company: agent.company,
        license: agent.license
      } : null,
      
      // Metadata
      featured: property.featured,
      createdAt: property.createdAt,
      publishedAt: property.publishedAt
    };

    return { property: detailedProperty };
  }
});

// Función para obtener todas las amenidades disponibles dinámicamente
export const getAvailableAmenities = query({
  args: {},
  handler: async (ctx) => {
    const properties = await ctx.db.query("properties")
      .filter((q) => q.neq(q.field("status"), "draft"))
      .collect();
    
    const amenitiesSet = new Set<string>();
    
    properties.forEach(property => {
      if (property.amenities) {
        property.amenities.forEach(amenity => {
          amenitiesSet.add(amenity);
        });
      }
    });
    
    return Array.from(amenitiesSet).sort();
  }
});

// Función para asignar propiedad a un usuario (agente)
export const assignPropertyToAgent = mutation({
  args: {
    propertyId: v.id("properties"),
    agentId: v.string(), // userId del agente
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado");
    }

    // Verificar que la propiedad existe
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    // Verificar que el usuario/agente existe
    const agent = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("userId"), args.agentId))
      .first();

    if (!agent) {
      throw new Error("Usuario/agente no encontrado");
    }

    // Actualizar la propiedad con el agentId
    await ctx.db.patch(args.propertyId, {
      agentId: args.agentId,
      updatedAt: new Date().toISOString(),
    });

    return {
      success: true,
      message: `Propiedad "${property.title}" asignada a ${agent.name}`,
      propertyId: args.propertyId,
      agentId: args.agentId,
    };
  },
});

// Función interna para asignar propiedad (sin autenticación - para admin)
export const adminAssignPropertyToAgent = internalAction({
  args: {
    propertyId: v.id("properties"),
    agentId: v.string(),
  },
  handler: async (ctx, args) => {
    // Verificar que la propiedad existe
    const property = await ctx.runQuery(internal.properties.getPropertyInternal, { id: args.propertyId });
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    // Actualizar la propiedad con el agentId
    await ctx.runMutation(internal.properties.updatePropertyAgent, {
      propertyId: args.propertyId,
      agentId: args.agentId,
    });

    return {
      success: true,
      message: `Propiedad asignada exitosamente`,
      propertyId: args.propertyId,
      agentId: args.agentId,
    };
  },
});

// Función auxiliar interna para actualizar el agente de una propiedad
export const updatePropertyAgent = internalMutation({
  args: {
    propertyId: v.id("properties"),
    agentId: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.propertyId, {
      agentId: args.agentId,
      updatedAt: new Date().toISOString(),
    });
  },
});

// Obtener una propiedad por ID
export const getProperty = query({
  args: { id: v.id("properties") },
  handler: async (ctx, { id }) => {
    return await ctx.db.get(id);
  }
});

// Función para listar usuarios disponibles para asignación
export const listAvailableAgents = query({
  args: {},
  handler: async (ctx) => {
    const users = await ctx.db.query("users").collect();
    
    return users.map(user => ({
      userId: user.userId,
      name: user.name,
      email: user.email,
      role: user.role,
      company: user.company,
      phone: user.phone,
    }));
  },
});

// Función temporal para debug - verificar propiedades en base de datos
export const debugListAllProperties = query({
  args: {},
  handler: async (ctx) => {
    const allProperties = await ctx.db.query("properties").collect();
    
    console.log(`Total propiedades en DB: ${allProperties.length}`);
    
    const summary = allProperties.map(p => ({
      id: p._id,
      title: p.title,
      type: p.type,
      status: p.status,
      city: p.city,
      price: p.price
    }));
    
    console.log("Resumen de propiedades:", summary);
    
    return {
      total: allProperties.length,
      properties: summary
    };
  }
});

// Función debug específica para probar búsqueda
export const debugSearchTest = query({
  args: {},
  handler: async (ctx) => {
    const searchCriteria = {
      city: 'zona 14',
      status: 'for_sale',
      type: 'apartment'
    };
    
    console.log("Testing search with:", searchCriteria);
    
    // Construir query como en la función real
    let query = ctx.db.query("properties");
    
    // Solo buscar propiedades publicadas
    query = query.filter((q) => q.neq(q.field("status"), "draft"));
    
    // Type filter
    if (searchCriteria.type) {
      query = query.filter((q) => q.eq(q.field("type"), searchCriteria.type));
    }
    
    // Status filter  
    if (searchCriteria.status) {
      query = query.filter((q) => q.eq(q.field("status"), searchCriteria.status));
    }
    
         console.log("After type and status filters...");
     
     const afterTypeStatus = await query.collect();
     console.log("Properties after type+status:", afterTypeStatus.map(p => ({
       title: p.title,
       type: p.type,
       status: p.status,
       city: p.city
     })));
     
     // Simular filtro de ciudad en memoria como en la función principal
     let finalResults = afterTypeStatus;
     if (searchCriteria.city) {
       const cityLower = searchCriteria.city.toLowerCase();
       console.log("Searching for city:", cityLower);
       
       finalResults = afterTypeStatus.filter(property => {
         const cityMatch = property.city?.toLowerCase().includes(cityLower);
         const titleMatch = property.title?.toLowerCase().includes(cityLower);
         return cityMatch || titleMatch;
       });
     }
    
         console.log("Final results:", finalResults.length);
     
     return {
       criteria: searchCriteria,
       afterTypeStatus: afterTypeStatus.length,
       finalResults: finalResults.length,
       properties: finalResults.map(p => ({
         title: p.title,
         type: p.type,
         status: p.status,
         city: p.city
       }))
     };
  }
});

// Nueva acción para búsqueda con OpenAI (para reemplazar el endpoint actual)
export const searchForAgentWithAI = internalAction({
  args: {
    searchCriteria: v.object({
      type: v.optional(v.string()),
      status: v.optional(v.string()),
      city: v.optional(v.string()),
      minPrice: v.optional(v.union(v.number(), v.string())),
      maxPrice: v.optional(v.union(v.number(), v.string())),
      bedrooms: v.optional(v.number()),
      bathrooms: v.optional(v.number()),
      minArea: v.optional(v.number()),
      maxArea: v.optional(v.number()),
      amenities: v.optional(v.array(v.string())),
      keywords: v.optional(v.array(v.string())),
      zones: v.optional(v.array(v.string())),
      searchTerms: v.optional(v.array(v.string())),
      searchText: v.optional(v.string())
    })
  },
  handler: async (ctx, { searchCriteria }): Promise<any> => {
    // Si hay búsqueda por texto, usar BÚSQUEDA SEMÁNTICA REAL primero
    if (searchCriteria.searchText) {
      console.log("🔍 N8N Búsqueda Semántica:", searchCriteria.searchText);
      
      // PASO 1: Intentar búsqueda semántica con embeddings primero
      try {
        const semanticResult = await ctx.runAction(internal.openaiExtraction.semanticSearch, {
          searchText: searchCriteria.searchText,
          limit: 8  // Más resultados para el agente
        });
        
        if (semanticResult.success && semanticResult.properties && semanticResult.properties.length > 0) {
          console.log(`🎯 N8N Búsqueda semántica exitosa: ${semanticResult.properties.length} resultados`);
          
          // Formatear resultados para compatibilidad con el agente
          const formattedProperties = semanticResult.properties.map((property: any, index: number) => {
            
            // Analizar coincidencias exactas vs aproximadas
            const searchText = searchCriteria.searchText?.toLowerCase() || '';
            const exactMatches: string[] = [];
            const approximateMatches: string[] = [];
            
            // Verificar coincidencias exactas en ubicación
            const propertyCity = property.city?.toLowerCase() || '';
            const propertyAddress = property.address?.toLowerCase() || '';
            const locationText = `${propertyCity} ${propertyAddress}`;
            
            // Detectar zonas específicas mencionadas
            const zoneRegex = /zona\s*(\d+)/gi;
            const searchZones = [...searchText.matchAll(zoneRegex)].map(match => match[1]);
            const propertyZones = [...locationText.matchAll(zoneRegex)].map(match => match[1]);
            
            // Verificar coincidencia exacta de zona
            const hasExactZone = searchZones.length > 0 && searchZones.some(zone => propertyZones.includes(zone));
            
            // Verificar coincidencia exacta de tipo
            const typeMap = { apartamento: 'apartment', casa: 'house', oficina: 'office' };
            const searchHasType = Object.keys(typeMap).some(key => searchText.includes(key));
            const searchType = Object.entries(typeMap).find(([key]) => searchText.includes(key))?.[1];
            const hasExactType = !searchHasType || property.type === searchType;
            
            // Verificar coincidencia exacta de ciudad
            const hasExactCity = !searchText.includes('guatemala') || propertyCity.includes('guatemala');
            
            // Clasificar tipo de coincidencia
            let matchType = "approximate";
            let matchExplanation = "";
            
            if (hasExactZone && hasExactType && hasExactCity) {
              matchType = "exact";
              exactMatches.push("ubicación", "tipo");
            } else if (hasExactZone && hasExactType) {
              matchType = "partial_exact";
              if (hasExactZone) exactMatches.push("zona");
              if (hasExactType) exactMatches.push("tipo");
            } else if (hasExactZone) {
              matchType = "zone_exact";
              exactMatches.push("zona");
            } else {
              matchType = "approximate";
              
              // Explicar por qué es aproximada
              if (searchZones.length > 0 && !hasExactZone) {
                const nearbyZones = propertyZones.length > 0 ? propertyZones : ['ubicación cercana'];
                approximateMatches.push(`zona cercana (${nearbyZones.join(', ')})`);
              }
              
              if (searchHasType && !hasExactType) {
                approximateMatches.push(`tipo similar (${property.type})`);
              }
            }
            
            // Generar explicación para el agente
            if (matchType === "exact") {
              matchExplanation = "Coincidencia exacta con todos los criterios";
            } else if (matchType === "partial_exact") {
              matchExplanation = `Coincidencia exacta en: ${exactMatches.join(', ')}`;
            } else if (matchType === "zone_exact") {
              matchExplanation = "Coincidencia exacta de zona";
            } else {
              matchExplanation = approximateMatches.length > 0 
                ? `Resultado similar: ${approximateMatches.join(', ')}`
                : "Resultado relevante por características similares";
            }
            
            return {
              id: property._id,
              title: property.title,
              type: property.type,
              status: property.status,
              price: property.price,
              currency: property.currency,
              address: property.address,
              city: property.city,
              state: property.state,
              bedrooms: property.bedrooms || 0,
              bathrooms: property.bathrooms || 0,
              area: property.area,
              parking: property.parking || 0,
              amenities: property.amenities || [],
              images: property.images || [],
              featured: property.featured || false,
              description: property.description || "",
              similarityScore: property.similarityScore,
              searchRelevance: "high",
              // Nuevos metadatos para el agente
              matchType: matchType,
              matchExplanation: matchExplanation,
              exactMatches: exactMatches,
              approximateMatches: approximateMatches,
              isExactMatch: matchType === "exact" || matchType === "zone_exact"
            };
          });
          
          return {
            properties: formattedProperties,
            total: formattedProperties.length,
            searchType: "semantic",
            success: true
          };
        }
      } catch (error) {
        console.log("⚠️ N8N Error en búsqueda semántica:", error);
      }
      
      // PASO 2: Fallback a búsqueda con criterios extraídos por IA
      console.log("🔄 N8N Fallback a extracción de criterios con IA");
      
      const aiResult = await ctx.runAction(internal.openaiExtraction.extractSearchCriteriaWithAI, {
        searchText: searchCriteria.searchText
      });
      
      let extractedCriteria: any = {};
      
      if (aiResult.success) {
        console.log("🤖 N8N IA extrajo criterios:", aiResult.criteria);
        extractedCriteria = aiResult.criteria;
      } else {
        console.log("⚠️ N8N IA falló, usando regex fallback:", aiResult.error);
        // Fallback a regex si IA falla
        extractedCriteria = extractSearchCriteria(searchCriteria.searchText);
        extractedCriteria = validateAndCleanCriteria(extractedCriteria);
      }
      
      console.log("📋 N8N Criterios finales:", extractedCriteria);
      
      // Usar los criterios extraídos para buscar propiedades
      const searchCriteriaWithAI = {
        ...searchCriteria,
        ...extractedCriteria
      };
      
      const result = await ctx.runQuery(internal.properties.searchForAgent, {
        searchCriteria: searchCriteriaWithAI
      });
      
      return {
        ...result,
        searchType: "structured"
      };
    }
    
    // Si no hay texto de búsqueda, usar la función existente
    return await ctx.runQuery(internal.properties.searchForAgent, {
      searchCriteria
    });
  },
});

// Acción pública para búsqueda inteligente desde el frontend
export const intelligentSearch = action({
  args: {
    searchText: v.string()
  },
  handler: async (ctx, { searchText }): Promise<any> => {
    console.log("🔍 Búsqueda Semántica IA:", searchText);
    
    // PASO 1: Intentar búsqueda semántica primero
          const semanticResult = await ctx.runAction(internal.openaiExtraction.semanticSearch, {
      searchText: searchText,
      limit: 6
    });
    
    if (semanticResult.success && semanticResult.properties && semanticResult.properties.length > 0) {
      console.log(`🎯 Búsqueda semántica exitosa: ${semanticResult.properties.length} resultados`);
      
      // Formatear resultados para compatibilidad
      const formattedProperties = semanticResult.properties.map((property: any, index: number) => ({
        _id: property._id,
        index: index + 1,
        title: property.title,
        type: property.type,
        status: property.status,
        price: property.price,
        currency: property.currency,
        address: property.address,
        city: property.city,
        state: property.state,
        bedrooms: property.bedrooms || 0,
        bathrooms: property.bathrooms || 0,
        area: property.area,
        parking: property.parking || 0,
        amenities: property.amenities || [],
        images: property.images?.slice(0, 3) || [],
        featured: property.featured || false,
        description: property.description?.substring(0, 200) + "..." || "",
        similarityScore: property.similarityScore
      }));
      
      return {
        properties: formattedProperties,
        total: formattedProperties.length,
        searchType: "semantic"
      };
    }
    
    // PASO 2: Fallback a búsqueda estructurada si semántica falla
    console.log("⚠️ Búsqueda semántica falló, usando criterios estructurados");
    
    // Usar IA para extraer criterios de lenguaje natural
    const aiResult = await ctx.runAction(internal.openaiExtraction.extractSearchCriteriaWithAI, {
      searchText: searchText
    });
    
    let extractedCriteria: any = {};
    
    if (aiResult.success) {
      console.log("🤖 IA extrajo criterios:", aiResult.criteria);
      extractedCriteria = aiResult.criteria;
    } else {
      console.log("⚠️ IA falló, usando regex fallback:", aiResult.error);
      // Fallback a regex si IA falla
      extractedCriteria = extractSearchCriteria(searchText);
    }
    
    console.log("📋 Criterios finales:", extractedCriteria);
    
    // Usar la función searchForAgent que ya funciona con los criterios extraídos
    const result = await ctx.runQuery(internal.properties.searchForAgent, {
      searchCriteria: {
        type: extractedCriteria.type,
        status: extractedCriteria.status,
        city: extractedCriteria.city,
        minPrice: extractedCriteria.minPrice,
        maxPrice: extractedCriteria.maxPrice,
        bedrooms: extractedCriteria.bedrooms,
        bathrooms: extractedCriteria.bathrooms,
        amenities: extractedCriteria.amenities,
        searchText: searchText
      }
    });
    
    console.log(`✅ Encontradas ${result?.properties?.length || 0} propiedades`);
    
    return {
      properties: result?.properties || [],
      total: result?.total || 0,
      searchType: "structured"
    };
  },
});

// Migración: Corregir amenidades con guiones bajos
export const fixAmenitiesWithUnderscores = mutation({
  handler: async (ctx) => {
    const properties = await ctx.db.query("properties").collect();
    
    const amenityMapping: Record<string, string> = {
      "piscina": "Piscina",
      "jardín": "Vista Jardín", 
      "jardin": "Vista Jardín",
      "seguridad_24h": "Seguridad 24h",
      "área_social": "Área Social",
      "area_social": "Área Social",
      "gimnasio": "Gimnasio",
      "salón_de_eventos": "Salón de Eventos",
      "salon_de_eventos": "Salón de Eventos",
      "terraza": "Terraza",
      "lavandería": "Lavandería",
      "lavanderia": "Lavandería",
      "amueblado": "Amoblado",
      "wifi": "WiFi",
      "cable": "TV Cable",
      "seguridad_privada": "Seguridad Privada",
      "áreas_verdes": "Áreas Verdes",
      "areas_verdes": "Áreas Verdes",
      "cancha_deportiva": "Cancha Deportiva"
    };
    
    let updatedCount = 0;
    
    for (const property of properties) {
      if (property.amenities && property.amenities.length > 0) {
        const correctedAmenities = property.amenities.map((amenity: string) => {
          return amenityMapping[amenity] || amenity;
        });
        
        // Solo actualizar si hubo cambios
        if (JSON.stringify(correctedAmenities) !== JSON.stringify(property.amenities)) {
          await ctx.db.patch(property._id, {
            amenities: correctedAmenities,
            updatedAt: new Date().toISOString()
          });
          updatedCount++;
        }
      }
    }
    
    return { 
      message: `Se actualizaron ${updatedCount} propiedades con amenidades corregidas`,
      totalProperties: properties.length 
    };
  },
});

// Actualizar embedding de una propiedad
export const updatePropertyEmbedding = internalMutation({
  args: {
    id: v.id("properties"),
    embedding: v.array(v.number()),
    embeddingText: v.string()
  },
  handler: async (ctx, { id, embedding, embeddingText }) => {
    await ctx.db.patch(id, {
      embedding,
      embeddingText,
      embeddingUpdatedAt: new Date().toISOString()
    });
  }
});

// Obtener todas las propiedades (para generar embeddings)
export const getAllProperties = internalQuery({
  handler: async (ctx) => {
    return await ctx.db.query("properties")
      .filter((q) => q.neq(q.field("status"), "draft"))
      .collect();
  }
});

// Obtener propiedades con embeddings
export const getPropertiesWithEmbeddings = internalQuery({
  handler: async (ctx) => {
    return await ctx.db.query("properties")
      .filter((q) => q.neq(q.field("status"), "draft"))
      .filter((q) => q.neq(q.field("embedding"), undefined))
      .collect();
  }
});

// Obtener una propiedad por ID (función interna)
export const getPropertyInternal = internalQuery({
  args: { id: v.id("properties") },
  handler: async (ctx, { id }) => {
    return await ctx.db.get(id);
  }
});

// 🚨 NUEVA: Query mejorada para obtener propiedades destacadas con límites
export const getFeaturedProperties = query({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    // Obtener propiedades destacadas activas
    const featuredProperties = await ctx.db
      .query("properties")
      .filter((q) => 
        q.and(
          q.eq(q.field("featured"), true),
          q.gt(q.field("featuredUntil"), now)
        )
      )
      .order("desc")
      .take(9); // Máximo 9

    return {
      properties: featuredProperties,
      count: featuredProperties.length,
      limit: 9,
      available: 9 - featuredProperties.length
    };
  }
});

// 🚨 NUEVA: Query mejorada para obtener propiedades premium con límites
export const getPremiumHomeProperties = query({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    // Obtener propiedad premium activa (solo 1)
    const premiumProperty = await ctx.db
      .query("properties")
      .filter((q) => 
        q.gt(q.field("premiumHomeUntil"), now)
      )
      .first();

    return {
      property: premiumProperty,
      count: premiumProperty ? 1 : 0,
      limit: 1,
      available: premiumProperty ? 0 : 1
    };
  }
});

// 🚨 NUEVA: Query para dashboard con información completa de límites
export const getDashboardFeaturedStats = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const now = Date.now();
    
    // Estadísticas globales
    const globalFeatured = await ctx.db
      .query("properties")
      .filter((q) => 
        q.and(
          q.eq(q.field("featured"), true),
          q.gt(q.field("featuredUntil"), now)
        )
      )
      .collect();

    const globalPremium = await ctx.db
      .query("properties")
      .filter((q) => 
        q.gt(q.field("premiumHomeUntil"), now)
      )
      .collect();

    // Propiedades del usuario
    const userProperties = await ctx.db
      .query("properties")
      .withIndex("by_owner", (q) => q.eq("ownerId", identity.subject))
      .collect();

    const userFeatured = userProperties.filter(p => 
      p.featured && p.featuredUntil && p.featuredUntil > now
    );

    const userPremium = userProperties.filter(p => 
      p.premiumHomeUntil && p.premiumHomeUntil > now
    );

    return {
      global: {
        featured: {
          active: globalFeatured.length,
          limit: 9,
          available: 9 - globalFeatured.length,
          percentage: Math.round((globalFeatured.length / 9) * 100)
        },
        premium: {
          active: globalPremium.length,
          limit: 1,
          available: 1 - globalPremium.length,
          percentage: Math.round((globalPremium.length / 1) * 100)
        }
      },
      user: {
        featured: userFeatured.length,
        premium: userPremium.length,
        total: userFeatured.length + userPremium.length,
        properties: userProperties.map(p => ({
          id: p._id,
          title: p.title,
          featured: p.featured && p.featuredUntil && p.featuredUntil > now,
          premium: p.premiumHomeUntil && p.premiumHomeUntil > now,
          featuredUntil: p.featuredUntil,
          premiumUntil: p.premiumHomeUntil,
          daysLeft: {
            featured: p.featuredUntil ? Math.max(0, Math.ceil((p.featuredUntil - now) / (24 * 60 * 60 * 1000))) : 0,
            premium: p.premiumHomeUntil ? Math.max(0, Math.ceil((p.premiumHomeUntil - now) / (24 * 60 * 60 * 1000))) : 0
          }
        }))
      }
    };
  }
});