import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { api } from "./_generated/api";
import { Id } from "./_generated/dataModel";

// === FUNCIONES PARA INTEGRACIÓN CON N8N Y ASISTENTE IA ===

// Verificar disponibilidad para una propiedad específica
export const checkPropertyAvailability = query({
  args: {
    propertyId: v.id("properties"),
    preferredDates: v.array(v.string()),
    duration: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    const availability = await ctx.db
      .query("availability")
      .withIndex("by_user", (q) => q.eq("userId", property.ownerId))
      .filter((q) => q.eq(q.field("isEnabled"), true))
      .collect();

    if (availability.length === 0) {
      return {
        hasAvailability: false,
        message: "El propietario no ha configurado su disponibilidad.",
        availableSlots: []
      };
    }

    const allAvailableSlots = [];
    
    for (const dateStr of args.preferredDates) {
      const targetDate = new Date(dateStr);
      const dayOfWeek = targetDate.getDay() as 0 | 1 | 2 | 3 | 4 | 5 | 6;
      const duration = args.duration || 60;

      const dayAvailability = availability.find(a => a.dayOfWeek === dayOfWeek);
      if (!dayAvailability) continue;

      const startOfDay = new Date(targetDate);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(targetDate);
      endOfDay.setHours(23, 59, 59, 999);

      const existingAppointments = await ctx.db
        .query("appointments")
        .withIndex("by_host_date", (q) => q.eq("hostId", property.ownerId))
        .filter((q) => 
          q.and(
            q.gte(q.field("startTime"), startOfDay.toISOString()),
            q.lte(q.field("startTime"), endOfDay.toISOString()),
            q.neq(q.field("status"), "cancelled")
          )
        )
        .collect();

      const [startHour, startMinute] = dayAvailability.startTime.split(':').map(Number);
      const [endHour, endMinute] = dayAvailability.endTime.split(':').map(Number);
      
      const slotDuration = dayAvailability.slotDuration || duration;
      const breakTime = dayAvailability.breakTime || 0;

      let currentTime = new Date(targetDate);
      currentTime.setHours(startHour, startMinute, 0, 0);
      
      const endTime = new Date(targetDate);
      endTime.setHours(endHour, endMinute, 0, 0);

      while (currentTime < endTime) {
        const slotEnd = new Date(currentTime.getTime() + slotDuration * 60000);
        
        if (slotEnd <= endTime) {
          const hasConflict = existingAppointments.some(appointment => {
            const appointmentStart = new Date(appointment.startTime);
            const appointmentEnd = new Date(appointment.endTime);
            return (currentTime < appointmentEnd && slotEnd > appointmentStart);
          });

          if (!hasConflict) {
            allAvailableSlots.push({
              date: dateStr,
              startTime: currentTime.toISOString(),
              endTime: slotEnd.toISOString(),
              duration: slotDuration,
              dayName: targetDate.toLocaleDateString('es-ES', { weekday: 'long' }),
              timeSlot: `${currentTime.getHours().toString().padStart(2, '0')}:${currentTime.getMinutes().toString().padStart(2, '0')}`
            });
          }
        }

        currentTime = new Date(currentTime.getTime() + (slotDuration + breakTime) * 60000);
      }
    }

    return {
      hasAvailability: true,
      property: {
        title: property.title,
        address: property.address,
        city: property.city
      },
      owner: property.ownerId,
      availableSlots: allAvailableSlots,
      message: allAvailableSlots.length > 0 
        ? `Encontré ${allAvailableSlots.length} horarios disponibles para visitar esta propiedad.`
        : "No hay horarios disponibles en las fechas solicitadas. ¿Te gustaría revisar otras fechas?"
    };
  },
});

// Crear solicitud de cita automatizada por el asistente
export const createAIAppointmentRequest = mutation({
  args: {
    propertyId: v.id("properties"),
    guestName: v.string(),
    guestEmail: v.string(),
    guestPhone: v.optional(v.string()),
    requestedStartTime: v.string(),
    requestedEndTime: v.string(),
    message: v.optional(v.string()),
    type: v.union(
      v.literal("property_viewing"),
      v.literal("consultation"),
      v.literal("negotiation"),
      v.literal("document_signing"),
      v.literal("other")
    ),
    meetingType: v.union(
      v.literal("in_person"),
      v.literal("video_call"),
      v.literal("phone_call")
    ),
    source: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    // Obtener información del propietario
    const owner = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", property.ownerId))
      .first();

    if (!owner) {
      throw new Error("Propietario no encontrado");
    }

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    const requestId = await ctx.db.insert("appointmentRequests", {
      hostId: property.ownerId,
      guestId: `ai-guest-${Date.now()}`,
      guestName: args.guestName,
      guestEmail: args.guestEmail,
      guestPhone: args.guestPhone,
      propertyId: args.propertyId,
      requestedStartTime: args.requestedStartTime,
      requestedEndTime: args.requestedEndTime,
      message: args.message,
      type: args.type,
      meetingType: args.meetingType,
      status: "pending",
      createdAt: new Date().toISOString(),
      expiresAt: expiresAt.toISOString(),
    });

    // 📧 ENVIAR NOTIFICACIONES POR EMAIL
    // Ejecutamos las notificaciones de forma inmediata pero sin bloquear
    ctx.scheduler.runAfter(0, api.emails.notifyAppointmentRequest, {
      ownerEmail: owner.email,
      ownerName: owner.name || "Propietario",
      guestEmail: args.guestEmail,
      guestName: args.guestName,
      guestPhone: args.guestPhone,
      propertyTitle: property.title,
      propertyAddress: `${property.address}, ${property.city}`,
      propertyPrice: `${property.currency} ${property.price?.toLocaleString()}`,
      requestedStartTime: args.requestedStartTime,
      requestedEndTime: args.requestedEndTime,
      meetingType: args.meetingType,
      message: args.message,
      dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/appointments`,
    }).catch((error) => {
      console.error("Error programando notificaciones:", error);
      // No fallar la solicitud si falla el email
    });

    return {
      success: true,
      requestId,
      message: "Solicitud de cita creada exitosamente. El propietario será notificado por email.",
      expiresAt: expiresAt.toISOString()
    };
  },
});

// Obtener contexto completo de una propiedad para el asistente
export const getPropertyContextForAI = query({
  args: { propertyId: v.id("properties") },
  handler: async (ctx, args) => {
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    const owner = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", property.ownerId))
      .first();

    const availability = await ctx.db
      .query("availability")
      .withIndex("by_user", (q) => q.eq("userId", property.ownerId))
      .filter((q) => q.eq(q.field("isEnabled"), true))
      .collect();

    const recentRequests = await ctx.db
      .query("appointmentRequests")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .filter((q) => q.eq(q.field("status"), "pending"))
      .order("desc")
      .take(3);

    const upcomingAppointments = await ctx.db
      .query("appointments")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .filter((q) => 
        q.and(
          q.gte(q.field("startTime"), new Date().toISOString()),
          q.neq(q.field("status"), "cancelled")
        )
      )
      .order("asc")
      .take(5);

    return {
      property: {
        id: property._id,
        title: property.title,
        address: property.address,
        city: property.city,
        state: property.state,
        type: property.type,
        status: property.status,
        price: property.price,
        currency: property.currency,
        bedrooms: property.bedrooms,
        bathrooms: property.bathrooms,
        area: property.area,
        amenities: property.amenities
      },
      owner: owner ? {
        name: owner.name || "Propietario",
        role: owner.role || "seller",
        company: owner.company,
        email: owner.email,
        phone: owner.phone
      } : null,
      availability: {
        configured: availability.length > 0,
        schedule: availability.map((a: any) => ({
          dayOfWeek: a.dayOfWeek,
          startTime: a.startTime,
          endTime: a.endTime,
          slotDuration: a.slotDuration || 60
        }))
      },
      activity: {
        pendingRequests: recentRequests.length,
        upcomingAppointments: upcomingAppointments.length,
        lastRequestDate: recentRequests[0]?.createdAt,
        nextAppointmentDate: upcomingAppointments[0]?.startTime
      }
    };
  },
}); 