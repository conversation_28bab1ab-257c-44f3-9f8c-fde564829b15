"use client";

import React, { useState } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Bold, Italic, List, Eye, Edit3 } from 'lucide-react';
import { RichFormattedText } from './formatted-text';

interface RichTextareaProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  minHeight?: string;
  maxLength?: number;
}

export function RichTextarea({ 
  value, 
  onChange, 
  placeholder = "Escribe tu texto aquí...",
  className = "",
  minHeight = "120px",
  maxLength = 2000
}: RichTextareaProps) {
  const [activeTab, setActiveTab] = useState<"edit" | "preview">("edit");
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);

  // Funciones para insertar formato
  const insertFormat = (before: string, after: string = '') => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.slice(start, end);
    
    const newText = value.slice(0, start) + before + selectedText + after + value.slice(end);
    onChange(newText);
    
    // Restaurar foco y posición del cursor
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + before.length, end + before.length);
    }, 0);
  };

  const insertBulletPoint = () => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const lineStart = value.lastIndexOf('\n', start - 1) + 1;
    const beforeLine = value.slice(0, lineStart);
    const afterLine = value.slice(lineStart);
    
    const newText = beforeLine + '• ' + afterLine;
    onChange(newText);
    
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + 2, start + 2);
    }, 0);
  };

  const insertNumberedList = () => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const lineStart = value.lastIndexOf('\n', start - 1) + 1;
    const beforeLine = value.slice(0, lineStart);
    const afterLine = value.slice(lineStart);
    
    const newText = beforeLine + '1. ' + afterLine;
    onChange(newText);
    
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + 3, start + 3);
    }, 0);
  };

  return (
    <div className={`border rounded-xl overflow-hidden ${className}`}>
      {/* Barra de herramientas */}
      <div className="border-b bg-gray-50 p-2">
        <div className="flex items-center gap-2">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "edit" | "preview")} className="flex-1">
            <TabsList className="grid w-full grid-cols-2 h-8">
              <TabsTrigger value="edit" className="text-xs flex items-center gap-1">
                <Edit3 className="h-3 w-3" />
                Editar
              </TabsTrigger>
              <TabsTrigger value="preview" className="text-xs flex items-center gap-1">
                <Eye className="h-3 w-3" />
                Vista Previa
              </TabsTrigger>
            </TabsList>
          </Tabs>
          
          {activeTab === "edit" && (
            <div className="flex items-center gap-1">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-7 px-2"
                onClick={insertBulletPoint}
                title="Lista con viñetas"
              >
                <List className="h-3 w-3" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-7 px-2"
                onClick={insertNumberedList}
                title="Lista numerada"
              >
                1.
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Contenido */}
      <Tabs value={activeTab} className="border-0">
        <TabsContent value="edit" className="m-0 border-0">
          <div className="relative">
            <Textarea
              ref={textareaRef}
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder={placeholder}
              className="border-0 rounded-none resize-none focus:ring-0"
              style={{ minHeight }}
              maxLength={maxLength}
            />
            <div className="absolute bottom-2 right-2 text-xs text-gray-500 bg-white px-2 py-1 rounded">
              {value.length}/{maxLength}
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="preview" className="m-0 border-0">
          <div className="p-4" style={{ minHeight }}>
            {value.trim() ? (
              <RichFormattedText text={value} />
            ) : (
              <p className="text-gray-500 italic">La vista previa aparecerá aquí...</p>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Tips de formato */}
      {activeTab === "edit" && (
        <div className="border-t bg-gray-50 p-2">
          <div className="text-xs text-gray-600">
            <strong>💡 Tips de formato:</strong> Usa <code>• texto</code> para listas con viñetas, 
            <code>1. texto</code> para listas numeradas, y doble enter para párrafos separados.
          </div>
        </div>
      )}
    </div>
  );
} 