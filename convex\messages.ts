import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { api } from "./_generated/api";

// Crear un nuevo mensaje/lead
export const createMessage = mutation({
  args: {
    propertyId: v.id("properties"),
    subject: v.string(),
    message: v.string(),
    senderName: v.string(),
    senderEmail: v.string(),
    senderPhone: v.optional(v.string()),
    leadType: v.union(
      v.literal("inquiry"),
      v.literal("viewing"), 
      v.literal("offer"),
      v.literal("negotiation")
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado para enviar mensajes");
    }

    // Obtener la propiedad para saber quién es el destinatario
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    // Crear el mensaje
    const messageId = await ctx.db.insert("messages", {
      propertyId: args.propertyId,
      subject: args.subject,
      message: args.message,
      senderId: identity.subject,
      receiverId: property.ownerId,
      status: "unread",
      leadType: args.leadType,
      senderName: args.senderName,
      senderEmail: args.senderEmail,
      senderPhone: args.senderPhone,
      createdAt: new Date().toISOString(),
      // Para futuro: calcular créditos según el tipo de lead
      creditsCharged: getCreditsForLeadType(args.leadType),
      creditsPaid: false,
    });

    // Consumir créditos del propietario que recibe la consulta
    try {
      // Buscar al propietario para consumir sus créditos
      const ownerIdentity = { subject: property.ownerId }; // Simular identidad del propietario
      
      // Verificar límites primero
      const subscription = await ctx.db
        .query("subscriptions")
        .withIndex("userId", (q) => q.eq("userId", property.ownerId))
        .first();

      let creditsToConsume = getCreditsForLeadType(args.leadType);

      if (!subscription) {
        // Crear suscripción gratuita si no existe
        await ctx.db.insert("subscriptions", {
          userId: property.ownerId,
          plan: "free",
          status: "active",
          credits: 10,
          maxProperties: 5,
          creditsUsed: creditsToConsume,
          propertiesCount: 0,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      } else {
        // Actualizar créditos usados
        await ctx.db.patch(subscription._id, {
          creditsUsed: subscription.creditsUsed + creditsToConsume,
          updatedAt: Date.now(),
        });
      }

          // Marcar que los créditos fueron cobrados
    await ctx.db.patch(messageId, {
      creditsPaid: true,
    });

    // 📧 ENVIAR NOTIFICACIÓN AL PROPIETARIO
    try {
      // Obtener información del propietario
      const owner = await ctx.db
        .query("users")
        .withIndex("by_token", (q) => q.eq("tokenIdentifier", property.ownerId))
        .first();

      if (owner && owner.email) {
        ctx.scheduler.runAfter(0, api.emails.notifyWebMessageReceived, {
          ownerEmail: owner.email,
          ownerName: owner.name || "Propietario",
          senderName: args.senderName,
          senderEmail: args.senderEmail,
          senderPhone: args.senderPhone,
          propertyTitle: property.title,
          propertyAddress: `${property.address}, ${property.city}`,
          propertyPrice: `${property.currency} ${property.price?.toLocaleString()}`,
          subject: args.subject,
          message: args.message,
          leadType: args.leadType,
          createdAt: new Date().toISOString(),
          dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/messages`,
        }).catch((error) => {
          console.error("Error enviando notificación de mensaje:", error);
        });
      }
    } catch (error) {
      console.error("Error programando notificación de mensaje:", error);
    }

    // 🚨 VERIFICAR CRÉDITOS AGOTADOS Y NOTIFICAR
    if (subscription) {
      try {
        const updatedSubscription = await ctx.db.get(subscription._id);
        if (updatedSubscription) {
          const remainingCredits = updatedSubscription.credits - updatedSubscription.creditsUsed;
          
          // Notificar cuando los créditos se agotan completamente
          if (remainingCredits <= 0) {
            const owner = await ctx.db
              .query("users")
              .withIndex("by_token", (q) => q.eq("tokenIdentifier", property.ownerId))
              .first();

            if (owner && owner.email) {
              ctx.scheduler.runAfter(0, api.emails.notifyLowCredits, {
                userEmail: owner.email,
                userName: owner.name || "Usuario",
                currentPlan: updatedSubscription.plan,
                creditsUsed: updatedSubscription.creditsUsed,
                totalCredits: updatedSubscription.credits,
                dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/finance`,
                upgradeUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/finance`,
              }).catch((error) => {
                console.error("Error enviando notificación de créditos agotados:", error);
              });
            }
          }
        }
      } catch (error) {
        console.error("Error verificando créditos agotados:", error);
      }
    }

  } catch (error) {
    console.error("Error consumiendo créditos:", error);
    // No fallar la creación del mensaje por errores de créditos
  }

  return messageId;
  },
});

// Función helper para calcular créditos (sistema de moneda interna)
function getCreditsForLeadType(leadType: string): number {
  switch (leadType) {
    case "inquiry": return 1;      // Consulta básica
    case "viewing": return 3;      // Solicitud de visita (más valiosa)
    case "offer": return 5;        // Oferta de compra (muy valiosa)
    case "negotiation": return 2;  // Negociación
    default: return 1;
  }
}

// Obtener mensajes recibidos (bandeja de entrada)
export const getInboxMessages = query({
  args: {
    status: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    let query = ctx.db
      .query("messages")
      .withIndex("by_receiver", (q) => q.eq("receiverId", identity.subject));

    if (args.status) {
      query = query.filter((q) => q.eq(q.field("status"), args.status));
    }

    const messages = await query
      .order("desc")
      .take(args.limit || 50);

    // Enriquecer con datos de la propiedad
    const enrichedMessages = await Promise.all(
      messages.map(async (message) => {
        const property = await ctx.db.get(message.propertyId);
        return {
          ...message,
          property: property ? {
            title: property.title,
            images: property.images,
            price: property.price,
            currency: property.currency,
            type: property.type,
            city: property.city,
          } : null,
        };
      })
    );

    return enrichedMessages;
  },
});

// Obtener mensajes enviados
export const getSentMessages = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    const messages = await ctx.db
      .query("messages")
      .withIndex("by_sender", (q) => q.eq("senderId", identity.subject))
      .order("desc")
      .take(args.limit || 50);

    // Enriquecer con datos de la propiedad
    const enrichedMessages = await Promise.all(
      messages.map(async (message) => {
        const property = await ctx.db.get(message.propertyId);
        return {
          ...message,
          property: property ? {
            title: property.title,
            images: property.images,
            price: property.price,
            currency: property.currency,
            type: property.type,
            city: property.city,
          } : null,
        };
      })
    );

    return enrichedMessages;
  },
});

// Marcar mensaje como leído
export const markAsRead = mutation({
  args: {
    messageId: v.id("messages"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Mensaje no encontrado");
    }

    // Solo el destinatario puede marcar como leído
    if (message.receiverId !== identity.subject) {
      throw new Error("No autorizado");
    }

    await ctx.db.patch(args.messageId, {
      status: "read",
      readAt: new Date().toISOString(),
    });

    return args.messageId;
  },
});

// Obtener estadísticas de mensajes
export const getMessageStats = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const allMessages = await ctx.db
      .query("messages")
      .withIndex("by_receiver", (q) => q.eq("receiverId", identity.subject))
      .collect();

    const unreadCount = allMessages.filter(m => m.status === "unread").length;
    const totalCount = allMessages.length;
    const creditsEarned = allMessages.reduce((sum, m) => sum + (m.creditsCharged || 0), 0);

    const leadTypeStats = allMessages.reduce((acc, message) => {
      acc[message.leadType] = (acc[message.leadType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      unreadCount,
      totalCount,
      creditsEarned,
      leadTypeStats,
    };
  },
});

// Archivar mensaje
export const archiveMessage = mutation({
  args: {
    messageId: v.id("messages"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Mensaje no encontrado");
    }

    // Solo el destinatario puede archivar
    if (message.receiverId !== identity.subject) {
      throw new Error("No autorizado");
    }

    await ctx.db.patch(args.messageId, {
      status: "archived",
    });

    return args.messageId;
  },
}); 