# Guías de Notificaciones - InmoApp

## Sistema de Notificaciones Unificado

Este proyecto utiliza **Sonner** como base para todas las notificaciones, con dos niveles:

1. **TOAST** - Feedback automático no invasivo
2. **ALERT** - Mensajes críticos que requieren atención

## Configuración

El toaster está configurado globalmente en `app/layout.tsx`:

```tsx
import { Toaster } from "@/components/ui/sonner";

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <body>
        {children}
        <Toaster />
      </body>
    </html>
  );
}
```

## 🟢 TOAST - Feedback Automático

### Cuándo usar:
- ✅ Confirmaciones de acciones exitosas
- ❌ Errores no críticos
- ℹ️ Información general
- ⚠️ Advertencias menores
- 🔄 Estados de carga temporales

### Importar:
```tsx
import { toast } from 'sonner';
```

### Tipos de Toast:

#### ✅ Success (Verde)
```tsx
toast.success("¡Propiedad guardada como borrador!");
toast.success("¡Propiedad publicada exitosamente!");
toast.success("Estado de la propiedad actualizado exitosamente");
```

#### ❌ Error (Rojo) - Solo errores NO CRÍTICOS
```tsx
toast.error("Error al subir las imágenes. Por favor intenta de nuevo.");
toast.error("Máximo 10 imágenes permitidas");
```

#### ℹ️ Info (Azul)
```tsx
toast.info("Guardando cambios...");
toast.info("Subiendo imágenes...");
```

#### ⚠️ Warning (Amarillo)
```tsx
toast.warning("Los cambios no guardados se perderán");
```

## 🔴 ALERT - Mensajes Críticos

### Cuándo usar:
- 🚨 Confirmaciones de acciones destructivas
- 🛑 Errores críticos del sistema
- 📢 Mensajes importantes que requieren respuesta
- ⚠️ Confirmaciones de seguridad

### Importar:
```tsx
import { useAlert } from "@/hooks/use-alert";
```

### Tipos de Alert:

#### 🚨 Confirmaciones Importantes
```tsx
const { showConfirm } = useAlert();

// Eliminar algo importante
showConfirm(
  "¿Eliminar esta propiedad? Esta acción no se puede deshacer",
  () => deleteProperty()
);

// Cancelar proceso importante
showConfirm(
  "¿Salir sin guardar? Perderás todos los cambios",
  () => router.push("/dashboard"),
  {
    title: "Confirmar salida",
    confirmText: "Salir sin guardar",
    cancelText: "Continuar editando"
  }
);
```

#### 🛑 Errores Críticos del Sistema
```tsx
const { showCriticalError } = useAlert();

// Error de conexión crítico
showCriticalError(
  "No se pudo conectar al servidor. Revisa tu conexión a internet.",
  () => window.location.reload(),
  "Reintentar"
);

// Error de autenticación
showCriticalError(
  "Tu sesión ha expirado. Inicia sesión nuevamente.",
  () => signOut(),
  "Ir a Login"
);
```

#### 📢 Mensajes del Sistema
```tsx
const { showSystemMessage } = useAlert();

// Como en tu imagen de ejemplo
showSystemMessage(
  "Disponibilidad guardada correctamente",
  "localhost:3000 says"
);

// Mantenimiento programado
showSystemMessage(
  "El sistema estará en mantenimiento de 2:00 AM a 4:00 AM",
  "Mantenimiento Programado"
);
```

## 📋 Reglas de Consistencia

### ✅ Usar TOAST para:
```tsx
// Feedback de acciones normales
toast.success("Mensaje enviado");
toast.error("Error al subir archivo");
toast.info("Procesando...");

// Validaciones simples
toast.warning("Completa todos los campos");
```

### ✅ Usar ALERT para:
```tsx
const { showConfirm, showCriticalError, showSystemMessage } = useAlert();

// Acciones destructivas
showConfirm("¿Eliminar cuenta?", () => deleteAccount());

// Errores críticos
showCriticalError("Error de servidor", () => retry());

// Mensajes del sistema
showSystemMessage("Operación completada");
```

### ❌ NO MEZCLAR:
```tsx
// ❌ MAL - No usar toast para confirmaciones críticas
toast("¿Eliminar cuenta?", { action: { label: "Sí" } });

// ❌ MAL - No usar alert para feedback simple
showSystemMessage("Archivo guardado");

// ✅ BIEN - Usar el tipo correcto
showConfirm("¿Eliminar cuenta?", () => delete());
toast.success("Archivo guardado");
```

## Ejemplos por Contexto

### Formularios
```tsx
const { showConfirm } = useAlert();

const handleSubmit = async (e: React.FormEvent, saveAsDraft = false) => {
  try {
    const propertyId = await createProperty(propertyData);
    
    if (saveAsDraft) {
      toast.success("¡Propiedad guardada como borrador!");
    } else {
      toast.success("¡Propiedad publicada exitosamente!");
    }
    router.push("/dashboard/properties");
  } catch (error) {
    toast.error("Error al crear la propiedad. Por favor intenta de nuevo.");
  }
};

const handleCancel = () => {
  if (hasUnsavedChanges) {
    showConfirm(
      "¿Salir sin guardar? Perderás todos los cambios",
      () => router.push("/dashboard")
    );
  } else {
    router.push("/dashboard");
  }
};
```

### Eliminaciones
```tsx
const { showConfirm } = useAlert();

const handleDelete = (propertyId: string) => {
  showConfirm(
    "¿Eliminar esta propiedad? Esta acción no se puede deshacer.",
    async () => {
      try {
        await deleteProperty(propertyId);
        toast.success("Propiedad eliminada exitosamente");
      } catch (error) {
        toast.error("Error al eliminar la propiedad");
      }
    },
    {
      title: "Confirmar eliminación",
      confirmText: "Eliminar",
      cancelText: "Cancelar"
    }
  );
};
```

### Errores del Sistema
```tsx
const { showCriticalError } = useAlert();

const handleApiError = (error: any) => {
  if (error.status === 401) {
    showCriticalError(
      "Tu sesión ha expirado. Inicia sesión nuevamente.",
      () => signOut(),
      "Ir a Login"
    );
  } else if (error.status >= 500) {
    showCriticalError(
      "Error del servidor. Intenta de nuevo en unos momentos.",
      () => window.location.reload(),
      "Reintentar"
    );
  } else {
    // Error no crítico
    toast.error("Error al procesar la solicitud");
  }
};
```

## ⚠️ Nunca Usar

- `alert()` - Usar `showSystemMessage()` o `showConfirm()`
- `confirm()` - Usar `showConfirm()`
- `console.log()` para feedback al usuario
- Múltiples sistemas de notificaciones
- Toast para confirmaciones críticas
- Alert para feedback simple

## Migración

Si encuentras código usando el sistema anterior:

```tsx
// ❌ Antes
alert("Propiedad guardada!");

// ✅ Después  
import { toast } from 'sonner';
toast.success("¡Propiedad guardada exitosamente!");
``` 