import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

export const getUser = query({
    args: {},
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (identity === null) {
            return ("Not authenticated");
        }
        return identity
    }
})

// Obtener usuario actual completo desde la base de datos
export const getCurrentUser = query({
    args: {},
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            return null;
        }

        const user = await ctx.db
            .query("users")
            .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
            .first();

        return user;
    },
});

export const getUserByToken = query({
    args: { tokenIdentifier: v.string() },
    handler: async (ctx, args) => {
        const user = await ctx.db.query("users")
            .withIndex("by_token", (q) => q.eq("tokenIdentifier", args.tokenIdentifier))
            .first();
        
        if (!user) return null;
        
        return {
            name: user.name || "Usuario",
            email: user.email,
            phone: user.phone || "+56 9 1234 5678",
            company: user.company || "InmoApp",
            bio: user.bio || "Profesional certificado con años de experiencia en el mercado inmobiliario. Te ayudamos a encontrar la propiedad perfecta para ti.",
            avatar: user.avatar || user.image,
            role: user.role || "agent",
            license: user.license
        };
    },
});

export const store = mutation({
    args: {},
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Called storeUser without authentication present");
        }

        // Check if we've already stored this identity before
        const user = await ctx.db
            .query("users")
            .withIndex("by_token", (q) =>
                q.eq("tokenIdentifier", identity.subject)
            )
            .unique();

        if (user !== null) {
            // Actualizar solo campos básicos si han cambiado
            const updateData: any = {};
            
            if (user.name !== identity.name) updateData.name = identity.name;
            if (user.email !== identity.email) updateData.email = identity.email;
            if (user.image !== identity.pictureUrl) updateData.image = identity.pictureUrl;

            if (Object.keys(updateData).length > 0) {
                await ctx.db.patch(user._id, updateData);
            }
            
            return user._id;
        }

        // Crear nuevo usuario con campos básicos
        const userName = identity.name || identity.email?.split('@')[0] || "Usuario";
        
        return await ctx.db.insert("users", {
            name: userName,
            email: identity.email!,
            image: identity.pictureUrl,
            userId: identity.subject,
            tokenIdentifier: identity.subject,
            createdAt: new Date().toISOString(),
        });
    },
});

export const updateProfile = mutation({
    args: {
        role: v.optional(v.string()),
        phone: v.optional(v.string()),
        company: v.optional(v.string()),
        license: v.optional(v.string()),
        bio: v.optional(v.string()),
        avatar: v.optional(v.string()),
        name: v.optional(v.string()),
        currency: v.optional(v.string()),
        notifications: v.optional(v.boolean()),
        newsletter: v.optional(v.boolean()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }

        const user = await ctx.db
            .query("users")
            .withIndex("by_token", (q) =>
                q.eq("tokenIdentifier", identity.subject)
            )
            .unique();

        if (!user) {
            throw new Error("User not found");
        }

        // Actualizar perfil con los nuevos datos
        await ctx.db.patch(user._id, {
            ...args,
            // Convertir role a tipo correcto si se proporciona
            ...(args.role && { role: args.role as any }),
        });

        return user._id;
    },
});

export const deleteUser = mutation({
    args: {},
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }

        const user = await ctx.db
            .query("users")
            .withIndex("by_token", (q) =>
                q.eq("tokenIdentifier", identity.subject)
            )
            .unique();

        if (!user) {
            throw new Error("User not found");
        }

        // Eliminar el usuario
        await ctx.db.delete(user._id);
        
        return { success: true };
    },
});

// Función para actualizar usuarios sin nombre
export const updateUsersWithoutNames = mutation({
    args: {},
    handler: async (ctx) => {
        const users = await ctx.db.query("users").collect();
        let updated = 0;
        
        for (const user of users) {
            if (!user.name || user.name.trim() === '' || user.name === 'Usuario') {
                const emailPart = user.email.split('@')[0];
                const newName = emailPart.charAt(0).toUpperCase() + emailPart.slice(1);
                
                await ctx.db.patch(user._id, {
                    name: newName
                });
                updated++;
            }
        }
        
        return { updated };
    },
});

// Función para sincronizar datos del perfil público desde Clerk
export const syncPublicProfile = mutation({
    args: {
        displayName: v.optional(v.string()),
        bio: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }

        const user = await ctx.db
            .query("users")
            .withIndex("by_token", (q) =>
                q.eq("tokenIdentifier", identity.subject)
            )
            .unique();

        if (!user) {
            throw new Error("User not found");
        }

        // Actualizar solo los campos del perfil público
        const updateData: any = {};
        if (args.displayName !== undefined) updateData.name = args.displayName;
        if (args.bio !== undefined) updateData.bio = args.bio;

        if (Object.keys(updateData).length > 0) {
            await ctx.db.patch(user._id, updateData);
        }

        return user._id;
    },
});

// Función para asignar rol de administrador (solo para desarrollo)
export const makeAdmin = mutation({
    args: {
        email: v.string(),
    },
    handler: async (ctx, args) => {
        // Buscar usuario por email
        const user = await ctx.db
            .query("users")
            .filter((q) => q.eq(q.field("email"), args.email))
            .first();

        if (!user) {
            throw new Error(`Usuario con email ${args.email} no encontrado`);
        }

        // Asignar rol de admin
        await ctx.db.patch(user._id, {
            role: "admin",
        });

        return {
            success: true,
            message: `Usuario ${user.name || user.email} ahora es administrador`,
            userId: user._id,
        };
    },
});
