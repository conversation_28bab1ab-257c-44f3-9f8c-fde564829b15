# 🔌 Conectar n8n Directamente a Convex

## **Método 1: JavaScript Code Node con Convex Client**

En lugar de crear APIs REST, puedes usar el cliente de Convex directamente en n8n:

### **Configuración Inicial**

1. **En n8n, usa un "Code" node con JavaScript**
2. **Instala dependencias automáticamente:**

```javascript
// Nodo Code en n8n
import { ConvexHttpClient } from "convex/browser";

const client = new ConvexHttpClient("https://tu-deployment.convex.cloud");

// 🔍 OBTENER PROPIEDADES
const properties = await client.query("properties:getProperties", {
  limit: 10,
  city: "Santiago",
  status: "for_sale"
});

// Retornar para el siguiente nodo
return properties.map(property => ({ json: property }));
```

### **Ejemplos Prácticos**

#### **📋 Obtener todas las propiedades**
```javascript
// Nodo Code en n8n
import { ConvexHttpClient } from "convex/browser";

const client = new ConvexHttpClient(process.env.CONVEX_URL);

const properties = await client.query("properties:getProperties", {
  limit: 50
});

return [{ json: { properties, total: properties.length } }];
```

#### **🏠 Crear nueva propiedad**
```javascript
// Nodo Code en n8n - Recibe datos del webhook anterior
const propertyData = $input.all()[0].json;

import { ConvexHttpClient } from "convex/browser";
const client = new ConvexHttpClient(process.env.CONVEX_URL);

const newPropertyId = await client.mutation("properties:createProperty", {
  title: propertyData.title,
  price: propertyData.price,
  description: propertyData.description,
  type: propertyData.type,
  address: propertyData.address,
  city: propertyData.city,
  state: propertyData.state,
  country: "Chile",
  area: propertyData.area,
  bedrooms: propertyData.bedrooms,
  bathrooms: propertyData.bathrooms,
  images: propertyData.images || [],
  ownerId: propertyData.ownerId,
  currency: "CLP",
  status: "draft"
});

return [{ json: { success: true, propertyId: newPropertyId } }];
```

#### **🔍 Buscar propiedades por filtros**
```javascript
// Nodo Code en n8n
import { ConvexHttpClient } from "convex/browser";
const client = new ConvexHttpClient(process.env.CONVEX_URL);

// Buscar casas caras en Santiago
const expensiveHouses = await client.query("properties:getProperties", {
  city: "Santiago",
  type: "house",
  minPrice: 200000000,
  limit: 5
});

// Formatear para WhatsApp/Email
const message = expensiveHouses.map(house => 
  `🏠 ${house.title}
💰 $${house.price.toLocaleString()} ${house.currency}
📍 ${house.address}, ${house.city}
🛏️ ${house.bedrooms} dormitorios | 🚿 ${house.bathrooms} baños
📐 ${house.area}m²`
).join('\n\n---\n\n');

return [{ json: { message, properties: expensiveHouses } }];
```

## **Método 2: HTTP Requests a Endpoints Automáticos de Convex**

Convex expone automáticamente endpoints para todas las funciones:

### **🔗 URLs Automáticas:**
```
GET  https://tu-deployment.convex.cloud/api/query?name=properties:getProperties&args={"limit":10}
POST https://tu-deployment.convex.cloud/api/mutation
```

### **Ejemplo en n8n HTTP Request Node:**

#### **Obtener propiedades (Query)**
```json
{
  "method": "GET",
  "url": "https://tu-deployment.convex.cloud/api/query",
  "parameters": {
    "queryParameters": {
      "name": "properties:getProperties",
      "args": "{\"limit\": 10, \"city\": \"Santiago\"}"
    }
  },
  "headers": {
    "Content-Type": "application/json"
  }
}
```

#### **Crear propiedad (Mutation)**
```json
{
  "method": "POST",
  "url": "https://tu-deployment.convex.cloud/api/mutation",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "name": "properties:createProperty",
    "args": {
      "title": "{{$json.title}}",
      "price": "{{$json.price}}",
      "type": "house",
      "address": "{{$json.address}}",
      "city": "{{$json.city}}",
      "ownerId": "{{$json.userId}}"
    }
  }
}
```

## **Método 3: WebSockets (Tiempo Real)**

Para datos en tiempo real, n8n puede conectarse via WebSocket:

```javascript
// Nodo Code en n8n para suscripción en tiempo real
import { ConvexHttpClient } from "convex/browser";

const client = new ConvexHttpClient(process.env.CONVEX_URL);

// Suscribirse a cambios en propiedades
const unsubscribe = client.subscribe("properties:getProperties", 
  { status: "for_sale" },
  (newProperties) => {
    // Cada vez que hay nuevas propiedades, ejecutar workflow
    console.log("Nuevas propiedades:", newProperties);
    
    // Aquí puedes activar otros nodos de n8n
    // Por ejemplo, enviar notificaciones, actualizar CRM, etc.
  }
);

// Mantener la suscripción activa
return [{ json: { subscribed: true } }];
```

## **🎯 Workflows Específicos**

### **Workflow 1: Monitor de Propiedades Nuevas**

1. **Schedule Trigger** (cada 15 minutos)
2. **Code Node:**
```javascript
import { ConvexHttpClient } from "convex/browser";
const client = new ConvexHttpClient(process.env.CONVEX_URL);

// Obtener propiedades de los últimos 15 minutos
const recentProperties = await client.query("properties:getProperties", {
  status: "for_sale",
  limit: 100
});

const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000).toISOString();
const newProperties = recentProperties.filter(p => p.createdAt > fifteenMinutesAgo);

if (newProperties.length > 0) {
  return newProperties.map(p => ({ json: p }));
} else {
  // Detener workflow si no hay propiedades nuevas
  return [];
}
```
3. **WhatsApp/Email Node** para notificar

### **Workflow 2: Agente IA de Recomendaciones**

1. **Webhook Trigger** (recibe perfil del cliente)
2. **Code Node:**
```javascript
const clientProfile = $input.all()[0].json;

import { ConvexHttpClient } from "convex/browser";
const client = new ConvexHttpClient(process.env.CONVEX_URL);

// Buscar propiedades que coincidan con el perfil
const matchingProperties = await client.query("properties:getProperties", {
  city: clientProfile.preferredCity,
  type: clientProfile.propertyType,
  minPrice: clientProfile.budgetMin,
  maxPrice: clientProfile.budgetMax,
  bedrooms: clientProfile.bedrooms,
  limit: 5
});

return [{ json: { client: clientProfile, recommendations: matchingProperties } }];
```
3. **OpenAI Node** para generar recomendación personalizada
4. **Email Node** para enviar al cliente

## **🔑 Variables de Entorno en n8n**

Configura estas variables en n8n:

```
CONVEX_URL=https://tu-deployment.convex.cloud
CONVEX_DEPLOYMENT_NAME=tu-deployment-name
```

## **⚡ Ventajas del Método Directo**

✅ **No necesitas programar APIs REST**
✅ **Acceso directo a todas las funciones de Convex**
✅ **Tipado automático**
✅ **Tiempo real con suscripciones**
✅ **Menos código, más productividad**

## **🔧 Para Empezar:**

1. Obtén tu URL de Convex: `npx convex dev`
2. En n8n, crea un "Code" node
3. Usa el código de ejemplo arriba
4. ¡Listo! Ya tienes acceso directo a tu base de datos

¿Quieres que configure algún workflow específico usando este método directo? 