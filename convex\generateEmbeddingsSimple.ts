import { action } from "./_generated/server";
import { internal } from "./_generated/api";
import OpenAI from 'openai';

// Inicializar OpenAI
let openai: OpenAI | null = null;

try {
  if (process.env.OPENAI_API_KEY) {
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }
} catch (error) {
  console.warn("OpenAI no disponible:", error);
}

// Función pública para generar embeddings de todas las propiedades
export const generateAllEmbeddingsNow = action({
  handler: async (ctx): Promise<{ success: boolean; message: string; processedCount: number; errors: string[] }> => {
    if (!openai) {
      return { 
        success: false, 
        message: "OpenAI no disponible - revisa OPENAI_API_KEY", 
        processedCount: 0,
        errors: ["OpenAI no configurado"]
      };
    }

    console.log("🚀 Iniciando generación de embeddings...");

    // Obtener todas las propiedades usando runQuery
    const properties = await ctx.runQuery(internal.properties.getAllProperties);

    console.log(`📊 Encontradas ${properties.length} propiedades para procesar`);
    
    let processedCount = 0;
    const errors: string[] = [];
    
    for (const property of properties) {
      try {
        console.log(`🔄 [${processedCount + 1}/${properties.length}] Procesando: ${property.title}`);
        
        // Crear descripción completa para el embedding
        const propertyDescription = [
          property.title,
          property.description,
          `Tipo de propiedad: ${property.type}`,
          `Ubicación: ${property.city}, ${property.state}`,
          `Estado: ${property.status}`,
          `Precio: ${property.price} ${property.currency}`,
          property.bedrooms ? `${property.bedrooms} habitaciones` : '',
          property.bathrooms ? `${property.bathrooms} baños` : '',
          `Área: ${property.area} metros cuadrados`,
          property.amenities?.length ? `Amenidades: ${property.amenities.join(', ')}` : '',
          property.address ? `Dirección: ${property.address}` : ''
        ].filter(Boolean).join('. ');

        // Generar embedding con OpenAI
        const response = await openai.embeddings.create({
          model: "text-embedding-3-small",
          input: propertyDescription,
        });

        const embedding = response.data[0].embedding;

        // Guardar embedding usando runMutation
        await ctx.runMutation(internal.properties.updatePropertyEmbedding, {
          id: property._id,
          embedding: embedding,
          embeddingText: propertyDescription
        });

        processedCount++;
        console.log(`✅ Embedding generado para: ${property.title}`);
        
        // Pequeña pausa para no saturar la API
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        const errorMsg = `Error con ${property.title}: ${error instanceof Error ? error.message : 'Error desconocido'}`;
        console.error(`❌ ${errorMsg}`);
        errors.push(errorMsg);
      }
    }

    const message = `✅ Proceso completado: ${processedCount}/${properties.length} embeddings generados exitosamente`;
    console.log(message);
    
    if (errors.length > 0) {
      console.log(`⚠️ Errores encontrados: ${errors.length}`);
    }

    return {
      success: true,
      message,
      processedCount,
      errors
    };
  }
}); 