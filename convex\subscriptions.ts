import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Función para obtener la suscripción del usuario
export const getUserSubscription = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    // Buscar suscripción activa del usuario
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    // Contar propiedades reales del usuario (solo las publicadas, no drafts)
    const userProperties = await ctx.db
      .query("properties")
      .withIndex("by_owner", (q) => q.eq("ownerId", identity.subject))
      .filter((q) => q.neq(q.field("status"), "draft"))
      .collect();

    const realPropertiesCount = userProperties.length;

    if (!subscription) {
      // Devolver plan gratuito por defecto
      return {
        id: "free-plan",
        status: "active",
        plan: "free",
        credits: 10,
        creditsUsed: 0,
        maxProperties: 5,
        propertiesCount: realPropertiesCount,
        isPremium: false,
        currentPeriodEnd: null,
        amount: 0,
      };
    }

    return {
      id: subscription._id,
      status: subscription.status,
      plan: subscription.plan,
      credits: subscription.credits,
      creditsUsed: subscription.creditsUsed,
      maxProperties: subscription.maxProperties,
      propertiesCount: realPropertiesCount, // Usar el conteo real
      isPremium: subscription.plan !== "free",
      currentPeriodEnd: subscription.currentPeriodEnd,
      amount: subscription.amount,
    };
  },
});

// Función para obtener el estado de suscripción del usuario
export const getUserSubscriptionStatus = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return {
        hasActiveSubscription: false,
        plan: "free",
        isPremium: false
      };
    }

    // Buscar suscripción activa del usuario (duplicar lógica para evitar llamada a query)
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (!subscription || subscription.status !== "active") {
      return {
        hasActiveSubscription: false,
        plan: "free",
        isPremium: false
      };
    }

    let plan = "free";
    let isPremium = false;

    if (subscription.amount === 29) {
      plan = "premium";
      isPremium = true;
    } else if (subscription.amount === 99) {
      plan = "pro";
      isPremium = true;
    }
    
    return {
      hasActiveSubscription: subscription.status === "active",
      plan,
      isPremium
    };
  },
});

// Función para consumir créditos
export const consumeCredits = mutation({
  args: {
    amount: v.number(),
    description: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Obtener usuario
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .unique();

    if (!user) {
      throw new Error("Usuario no encontrado");
    }

    // Obtener créditos actuales
    const currentCredits = parseInt(user.credits || "0");
    
    if (currentCredits < args.amount) {
      throw new Error("Créditos insuficientes");
    }

    // Restar créditos
    const newCredits = currentCredits - args.amount;
    await ctx.db.patch(user._id, {
      credits: newCredits.toString()
    });

    // Registrar el consumo (opcional: crear tabla de historial)
    return {
      success: true,
      remainingCredits: newCredits,
      consumed: args.amount
    };
  },
});

// Función para verificar si el usuario puede publicar más propiedades
export const canPublishProperty = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return false;
    }

    // Obtener suscripción (duplicar lógica)
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    let maxProperties = 5; // Plan gratuito por defecto

    if (subscription && subscription.status === "active") {
      if (subscription.amount === 29) {
        maxProperties = 50;
      } else if (subscription.amount === 99) {
        maxProperties = 999999;
      }
    }

    // Contar propiedades activas del usuario
    const propertiesCount = await ctx.db
      .query("properties")
      .withIndex("by_owner", (q) => q.eq("ownerId", identity.subject))
      .filter((q) => q.neq(q.field("status"), "draft"))
      .collect();

    return propertiesCount.length < maxProperties;
  },
});

// Función para obtener estadísticas de uso
export const getUsageStats = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    // Contar propiedades del usuario
    const userProperties = await ctx.db
      .query("properties")
      .withIndex("by_owner", (q) => q.eq("ownerId", identity.subject))
      .collect();

    const propertiesCount = userProperties.length;

    if (!subscription) {
      return {
        plan: "free",
        propertiesUsed: propertiesCount,
        propertiesLimit: 5,
        creditsUsed: 0,
        creditsLimit: 10,
        currentCredits: 10,
        percentage: {
          properties: (propertiesCount / 5) * 100,
          credits: 0,
        },
      };
    }

    const currentCredits = subscription.credits - subscription.creditsUsed;

    return {
      plan: subscription.plan,
      propertiesUsed: propertiesCount,
      propertiesLimit: subscription.maxProperties,
      creditsUsed: subscription.creditsUsed,
      creditsLimit: subscription.credits,
      currentCredits,
      percentage: {
        properties: (propertiesCount / subscription.maxProperties) * 100,
        credits: (subscription.creditsUsed / subscription.credits) * 100,
      },
    };
  },
});

// Función para agregar créditos (solo para desarrollo/testing)
export const addCreditsToUser = mutation({
  args: {
    creditsToAdd: v.number(),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Buscar suscripción existente
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    const now = Date.now();

    if (!subscription) {
      // Crear suscripción si no existe
      const subscriptionId = await ctx.db.insert("subscriptions", {
        userId: identity.subject,
        plan: "free",
        status: "active",
        credits: 10 + args.creditsToAdd,
        creditsUsed: 0,
        maxProperties: 5,
        propertiesCount: 0,
        createdAt: now,
        updatedAt: now,
      });

      return {
        success: true,
        message: `Suscripción creada con ${10 + args.creditsToAdd} créditos`,
        totalCredits: 10 + args.creditsToAdd,
        creditsAdded: args.creditsToAdd,
      };
    } else {
      // Agregar créditos a la suscripción existente
      const newTotalCredits = subscription.credits + args.creditsToAdd;
      
      await ctx.db.patch(subscription._id, {
        credits: newTotalCredits,
        updatedAt: now,
      });

      return {
        success: true,
        message: `Se agregaron ${args.creditsToAdd} créditos. Total: ${newTotalCredits}`,
        totalCredits: newTotalCredits,
        creditsAdded: args.creditsToAdd,
        reason: args.reason || "Créditos adicionales",
      };
    }
  },
});

// Función para resetear créditos usados (solo para desarrollo/testing)
export const resetUsedCredits = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (!subscription) {
      throw new Error("No tienes suscripción");
    }

    await ctx.db.patch(subscription._id, {
      creditsUsed: 0,
      updatedAt: Date.now(),
    });

    return {
      success: true,
      message: `Créditos usados reseteados. Ahora tienes ${subscription.credits} créditos disponibles`,
      totalCredits: subscription.credits,
      creditsUsed: 0,
    };
  },
});

// Función para consumir créditos por acción específica
export const consumeCreditsForAction = mutation({
  args: {
    action: v.string(), // "read_message" o "view_appointment_request"
    resourceId: v.string(), // ID del mensaje o solicitud
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Obtener configuración de la acción
    const creditAction = await ctx.db
      .query("creditActions")
      .filter((q) => q.eq(q.field("action"), args.action))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (!creditAction) {
      throw new Error("Acción no válida");
    }

    // Verificar si ya se consumieron créditos para este recurso
    const existingConsumption = await ctx.db
      .query("creditConsumptions")
      .filter((q) => q.eq(q.field("userId"), identity.subject))
      .filter((q) => q.eq(q.field("action"), args.action))
      .filter((q) => q.eq(q.field("resourceId"), args.resourceId))
      .first();

    if (existingConsumption) {
      // Ya se pagó por este recurso
      return { success: true, alreadyPaid: true, creditsUsed: 0 };
    }

    // Obtener suscripción
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (!subscription) {
      throw new Error("Suscripción no encontrada");
    }

    // Verificar créditos disponibles
    const availableCredits = subscription.credits - subscription.creditsUsed;
    if (availableCredits < creditAction.cost) {
      throw new Error("Créditos insuficientes");
    }

    // Consumir créditos
    const newCreditsUsed = subscription.creditsUsed + creditAction.cost;
    await ctx.db.patch(subscription._id, {
      creditsUsed: newCreditsUsed,
      updatedAt: Date.now(),
    });

    // Registrar el consumo
    await ctx.db.insert("creditConsumptions", {
      userId: identity.subject,
      action: args.action,
      resourceId: args.resourceId,
      creditsUsed: creditAction.cost,
      description: creditAction.description,
      timestamp: Date.now(),
    });

    return {
      success: true,
      alreadyPaid: false,
      creditsUsed: creditAction.cost,
      remainingCredits: subscription.credits - newCreditsUsed,
    };
  },
});

// Verificar si se puede realizar una acción (si tiene créditos suficientes)
export const canPerformAction = query({
  args: {
    action: v.string(),
    resourceId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return { canPerform: false, reason: "No autenticado" };
    }

    // Si se proporciona resourceId, verificar si ya se pagó
    if (args.resourceId) {
      const existingConsumption = await ctx.db
        .query("creditConsumptions")
        .filter((q) => q.eq(q.field("userId"), identity.subject))
        .filter((q) => q.eq(q.field("action"), args.action))
        .filter((q) => q.eq(q.field("resourceId"), args.resourceId))
        .first();

      if (existingConsumption) {
        return { canPerform: true, reason: "Ya pagado", alreadyPaid: true };
      }
    }

    // Obtener configuración de la acción
    const creditAction = await ctx.db
      .query("creditActions")
      .filter((q) => q.eq(q.field("action"), args.action))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (!creditAction) {
      return { canPerform: false, reason: "Acción no válida" };
    }

    // Obtener suscripción
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (!subscription) {
      return { canPerform: false, reason: "Suscripción no encontrada" };
    }

    // Verificar créditos disponibles
    const availableCredits = subscription.credits - subscription.creditsUsed;
    if (availableCredits < creditAction.cost) {
      return { 
        canPerform: false, 
        reason: "Créditos insuficientes",
        requiredCredits: creditAction.cost,
        availableCredits 
      };
    }

    return { 
      canPerform: true, 
      reason: "Créditos suficientes",
      requiredCredits: creditAction.cost,
      availableCredits 
    };
  },
});
