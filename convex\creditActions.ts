import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Configuración inicial de precios de créditos
const DEFAULT_CREDIT_ACTIONS = [
  {
    action: "featured_property",
    cost: 10,
    duration: 30, // 30 días
    description: "Destacar propiedad por 30 días",
    isActive: true,
    metadata: {
      maxConcurrent: null, // sin límite de propiedades destacadas simultáneas
      autoRenew: false
    }
  },
  {
    action: "premium_home",
    cost: 25,
    duration: 7, // 7 días
    description: "Posición premium en home por 7 días",
    isActive: true,
    metadata: {
      maxConcurrent: 1, // solo 1 propiedad premium a la vez
      autoRenew: false
    }
  },
  {
    action: "message_inquiry",
    cost: 1,
    duration: undefined, // instantáneo
    description: "Recibir consulta general",
    isActive: true,
    metadata: {}
  },
  {
    action: "message_viewing",
    cost: 3,
    duration: undefined,
    description: "Recibir solicitud de visita",
    isActive: true,
    metadata: {}
  },
  {
    action: "message_offer",
    cost: 5,
    duration: undefined,
    description: "Recibir oferta de compra",
    isActive: true,
    metadata: {}
  },
  {
    action: "message_negotiation",
    cost: 2,
    duration: undefined,
    description: "Recibir consulta de negociación",
    isActive: true,
    metadata: {}
  },
  {
    action: "read_message",
    cost: 2,
    duration: undefined,
    description: "Leer mensaje recibido de web",
    isActive: true,
    metadata: {}
  },
  {
    action: "view_appointment_request",
    cost: 3,
    duration: undefined,
    description: "Ver solicitud de cita",
    isActive: true,
    metadata: {}
  }
];

// Seed inicial de configuraciones de créditos
export const seedCreditActions = mutation({
  args: {},
  handler: async (ctx) => {
    // Verificar si ya existen configuraciones
    const existingActions = await ctx.db.query("creditActions").collect();
    
    if (existingActions.length > 0) {
      console.log("Las configuraciones de créditos ya existen");
      return { message: "Configuraciones ya existen", count: existingActions.length };
    }

    // Insertar configuraciones por defecto
    const inserted = [];
    const now = Date.now();

    for (const actionConfig of DEFAULT_CREDIT_ACTIONS) {
      const actionId = await ctx.db.insert("creditActions", {
        ...actionConfig,
        createdAt: now,
        updatedAt: now,
      });
      inserted.push(actionId);
    }

    return { 
      message: "Configuraciones de créditos creadas exitosamente", 
      count: inserted.length,
      actions: inserted
    };
  },
});

// Obtener configuración de una acción específica
export const getCreditActionConfig = query({
  args: {
    action: v.string(),
  },
  handler: async (ctx, args) => {
    const config = await ctx.db
      .query("creditActions")
      .withIndex("by_action", (q) => q.eq("action", args.action))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    return config;
  },
});

// Obtener todas las configuraciones activas
export const getAllCreditActions = query({
  args: {},
  handler: async (ctx) => {
    const actions = await ctx.db
      .query("creditActions")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .collect();

    return actions;
  },
});

// Actualizar configuración de una acción
export const updateCreditActionConfig = mutation({
  args: {
    actionId: v.id("creditActions"),
    cost: v.optional(v.number()),
    duration: v.optional(v.number()),
    description: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const { actionId, ...updates } = args;
    
    // Agregar timestamp de actualización
    const updateData = {
      ...updates,
      updatedAt: Date.now(),
    };

    await ctx.db.patch(actionId, updateData);

    return { success: true, actionId };
  },
});

// Función helper para obtener el costo de una acción
export const getActionCost = query({
  args: {
    action: v.string(),
  },
  handler: async (ctx, args) => {
    const config = await ctx.db
      .query("creditActions")
      .withIndex("by_action", (q) => q.eq("action", args.action))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (!config) {
      throw new Error(`Configuración no encontrada para la acción: ${args.action}`);
    }

    return {
      action: args.action,
      cost: config.cost,
      duration: config.duration,
      description: config.description,
      metadata: config.metadata,
    };
  },
});

 