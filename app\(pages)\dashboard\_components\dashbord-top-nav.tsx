"use client"

import ModeToggle from '@/components/mode-toggle'
import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogClose } from '@/components/ui/dialog'
import { Separator } from '@/components/ui/separator'
import { <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from '@/components/ui/sheet'
import { Badge } from '@/components/ui/badge'
import { HamburgerMenuIcon } from '@radix-ui/react-icons'
import { Banknote, HomeIcon, Settings, Building2, Mail, Shield } from 'lucide-react'
import Link from 'next/link'
import { ReactNode } from 'react'
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { usePathname } from 'next/navigation'
import clsx from 'clsx'

export default function DashboardTopNav({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  const messageStats = useQuery(api.messages.getMessageStats, {});
  const currentUser = useQuery(api.users.getCurrentUser);

  const navItems = [
    {
      label: "Panel Principal",
      href: "/dashboard",
      icon: HomeIcon
    },
    {
      label: "Mis Propiedades", 
      href: "/dashboard/properties",
      icon: Building2
    },
    {
      label: "Mensajes",
      href: "/dashboard/messages", 
      icon: Mail,
      showBadge: true
    },
    {
      label: "Suscripción",
      href: "/dashboard/finance",
      icon: Banknote
    },
    ...(currentUser?.role === 'admin' ? [{
      label: "Administración",
      href: "/dashboard/admin",
      icon: Shield,
      adminOnly: true
    }] : []),
    {
      label: "Configuración",
      href: "/dashboard/settings",
      icon: Settings
    }
  ];

  return (
    <div className="flex flex-col h-screen">
      <header className="flex h-[3.45rem] items-center gap-4 border-b bg-background px-4 lg:h-[3.45rem] lg:px-6">
        {/* Mobile Menu Button */}
        <Dialog>
          <SheetTrigger className="min-[1024px]:hidden p-2 transition hover:bg-gray-100 rounded">
            <HamburgerMenuIcon className="h-5 w-5" />
            <span className="sr-only">Abrir menú de navegación</span>
          </SheetTrigger>
          <SheetContent side="left" className="w-80">
            <SheetHeader className="pb-4">
              <Link href="/">
                <SheetTitle className="flex items-center gap-2 text-left">
                  <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
                    <HomeIcon className="h-4 w-4 text-white" />
                  </div>
                  Inmova
                </SheetTitle>
              </Link>
            </SheetHeader>
            
            <nav className="flex flex-col space-y-2 mt-4">
              {navItems.map((item) => (
                <DialogClose key={item.href} asChild>
                  <Link href={item.href}>
                    <Button 
                      variant="ghost" 
                      className={clsx(
                        "w-full justify-start gap-3 h-12",
                        pathname === item.href
                          ? "bg-blue-50 text-blue-600 hover:bg-blue-100"
                          : "hover:bg-blue-50 hover:text-blue-600"
                      )}
                    >
                      <item.icon className="h-4 w-4" />
                      {item.label}
                      
                      {item.adminOnly && (
                        <Badge 
                          variant="secondary" 
                          className="ml-auto h-5 px-2 text-xs bg-orange-100 text-orange-800"
                        >
                          Admin
                        </Badge>
                      )}
                      
                      {item.showBadge && messageStats && messageStats.unreadCount > 0 && (
                        <Badge 
                          variant="destructive" 
                          className="ml-auto h-5 w-5 flex items-center justify-center p-0 text-xs bg-red-500"
                        >
                          {messageStats.unreadCount > 99 ? '99+' : messageStats.unreadCount}
                        </Badge>
                      )}
                    </Button>
                  </Link>
                </DialogClose>
              ))}
            </nav>
          </SheetContent>
        </Dialog>

        {/* Desktop Title - Hidden on mobile when we have hamburger */}
        <div className="min-[1024px]:hidden flex-1">
          <Link href="/" className="flex items-center gap-2 font-semibold">
            <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
              <HomeIcon className="h-4 w-4 text-white" />
            </div>
                            <span>Inmova</span>
          </Link>
        </div>
        
        {/* Right side actions */}
        <div className="flex justify-center items-center gap-2 ml-auto">
          <ModeToggle />
        </div>
      </header>
      {children}
    </div>
  )
}
