"use client";

import { useState } from "react";
import { useUser } from "@clerk/clerk-react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CreditsDisplay } from "@/components/ui/credits-display";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Building2, 
  Plus, 
  Edit, 
  Trash2, 
  MoreHorizontal, 
  Eye, 
  MapPin, 
  DollarSign,
  Star,
  Crown,
  Clock,
  AlertTriangle
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { toast } from "sonner";

export default function MyPropertiesPage() {
  const { user } = useUser();
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  
  // Obtener propiedades del usuario
  const userProperties = useQuery(api.properties.getUserProperties, {
    userId: user?.id || "",
  });

  const updateProperty = useMutation(api.properties.updateProperty);
  const deleteProperty = useMutation(api.properties.deleteProperty);

  // Mutations para destacadas
  const featureProperty = useMutation(api.featuredProperties.featureProperty);
  const unfeatureProperty = useMutation(api.featuredProperties.unfeatureProperty);
  const promoteToHomePremium = useMutation(api.featuredProperties.promoteToHomePremium);
  const removeFromHomePremium = useMutation(api.featuredProperties.removeFromHomePremium);

  // Query para estado de destacadas
  const featuredStatus = useQuery(api.featuredProperties.getUserFeaturedStatus);

  // Agregar query para obtener suscripción del usuario
  const subscription = useQuery(api.subscriptions.getUserSubscription);

  // Filtrar propiedades por estado
  const filteredProperties = userProperties?.filter(property => {
    if (selectedStatus === "all") return true;
    return property.status === selectedStatus;
  }) || [];

  const getStatusBadge = (status: string) => {
    const statusMap = {
      for_sale: { label: 'En Venta', variant: 'default' as const, color: 'bg-blue-100 text-blue-800' },
      for_rent: { label: 'En Alquiler', variant: 'secondary' as const, color: 'bg-blue-100 text-blue-800' },
      sold: { label: 'Vendido', variant: 'destructive' as const, color: 'bg-red-100 text-red-800' },
      rented: { label: 'Alquilado', variant: 'outline' as const, color: 'bg-purple-100 text-purple-800' },
      draft: { label: 'Borrador', variant: 'outline' as const, color: 'bg-gray-100 text-gray-800' },
    };
    
    return statusMap[status as keyof typeof statusMap] || { label: status, variant: 'default' as const, color: 'bg-gray-100 text-gray-800' };
  };

  const getTypeLabel = (type: string) => {
    const typeMap = {
      house: 'Casa',
      apartment: 'Apartamento',
      office: 'Oficina',
      land: 'Terreno',
      commercial: 'Comercial',
    };
    
    return typeMap[type as keyof typeof typeMap] || type;
  };

  const handleUpdateStatus = async (propertyId: Id<"properties">, newStatus: string) => {
    try {
      await updateProperty({
        id: propertyId,
        status: newStatus as any
      });
      toast.success("Estado de la propiedad actualizado exitosamente");
    } catch (error) {
      console.error("Error updating property status:", error);
      toast.error("Error al actualizar el estado de la propiedad");
    }
  };

  const handleDeleteProperty = async (propertyId: Id<"properties">) => {
    if (confirm("¿Estás seguro de que quieres eliminar esta propiedad? Esta acción no se puede deshacer.")) {
      try {
        await deleteProperty({ id: propertyId });
        toast.success("Propiedad eliminada exitosamente");
      } catch (error) {
        console.error("Error deleting property:", error);
        toast.error("Error al eliminar la propiedad");
      }
    }
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(price);
  };

  // Funciones para destacadas
  const handleFeatureProperty = async (propertyId: Id<"properties">) => {
    try {
      // Verificar créditos antes de ejecutar
      const availableCredits = subscription ? subscription.credits - subscription.creditsUsed : 0;
      const requiredCredits = 10; // Costo de Destacar Propiedad

      if (availableCredits < requiredCredits) {
        toast.error(
          `❌ Créditos insuficientes: Necesitas ${requiredCredits} créditos, tienes ${availableCredits}. ` +
          `Ve a Finanzas para obtener más créditos.`,
          { duration: 6000 }
        );
        return;
      }

      const result = await featureProperty({ propertyId });
      toast.success(`✅ Propiedad destacada exitosamente! Costo: ${result.creditsCost} créditos`);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    }
  };

  const handleUnfeatureProperty = async (propertyId: Id<"properties">) => {
    try {
      await unfeatureProperty({ propertyId });
      toast.success("✅ Destacado removido exitosamente");
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    }
  };

  const handlePromoteToPremium = async (propertyId: Id<"properties">) => {
    try {
      // Verificar créditos antes de ejecutar
      const availableCredits = subscription ? subscription.credits - subscription.creditsUsed : 0;
      const requiredCredits = 25; // Costo de Premium Home

      if (availableCredits < requiredCredits) {
        toast.error(
          `❌ Créditos insuficientes: Necesitas ${requiredCredits} créditos, tienes ${availableCredits}. ` +
          `Ve a Finanzas para obtener más créditos.`,
          { duration: 6000 }
        );
        return;
      }

      const result = await promoteToHomePremium({ propertyId });
      toast.success(`✅ Propiedad promovida a Premium Home! Costo: ${result.creditsCost} créditos`);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    }
  };

  const handleRemoveFromPremium = async (propertyId: Id<"properties">) => {
    try {
      await removeFromHomePremium({ propertyId });
      toast.success("✅ Removido de Premium Home exitosamente");
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    }
  };

  // Helper para obtener estado de una propiedad
  const getPropertyStatus = (propertyId: string) => {
    return featuredStatus?.properties.find(p => p.id === propertyId) || {
      isFeatured: false,
      isPremium: false,
      canFeature: true,
      canPremium: true,
    };
  };

  // Helper para verificar créditos disponibles
  const hasCreditsFor = (action: 'feature' | 'premium') => {
    if (!subscription) return false;
    const availableCredits = subscription.credits - subscription.creditsUsed;
    const requiredCredits = action === 'feature' ? 10 : 25;
    return availableCredits >= requiredCredits;
  };

  const statusCounts = {
    all: userProperties?.length || 0,
    for_sale: userProperties?.filter(p => p.status === "for_sale").length || 0,
    for_rent: userProperties?.filter(p => p.status === "for_rent").length || 0,
    sold: userProperties?.filter(p => p.status === "sold").length || 0,
    rented: userProperties?.filter(p => p.status === "rented").length || 0,
    draft: userProperties?.filter(p => p.status === "draft").length || 0,
  };

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-semibold tracking-tight">Mis Propiedades</h1>
          <p className="text-muted-foreground">
            Gestiona tus propiedades y promociones
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <CreditsDisplay variant="compact" />
          <Link href="/dashboard/properties/new">
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Nueva Propiedad
            </Button>
          </Link>
        </div>
      </div>

      {/* Alerta de créditos bajos */}
      {subscription && (subscription.credits - subscription.creditsUsed) < 10 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-3">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <div className="flex-1">
              <h3 className="font-medium text-red-800">Créditos Críticos</h3>
              <p className="text-sm text-red-700">
                Solo tienes {subscription.credits - subscription.creditsUsed} créditos. 
                La mayoría de funcionalidades premium están deshabilitadas.
              </p>
            </div>
            <Link href="/dashboard/finance">
              <Button size="sm" className="bg-red-600 hover:bg-red-700">
                Recargar Ahora
              </Button>
            </Link>
          </div>
        </div>
      )}

      {/* Filtros por Estado */}
      <div className="flex gap-2 flex-wrap">
        <Button
          variant={selectedStatus === "all" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedStatus("all")}
          className={selectedStatus === "all" ? "bg-blue-600 hover:bg-blue-700" : ""}
        >
          Todas ({statusCounts.all})
        </Button>
        <Button
          variant={selectedStatus === "for_sale" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedStatus("for_sale")}
          className={selectedStatus === "for_sale" ? "bg-blue-600 hover:bg-blue-700" : ""}
        >
          En Venta ({statusCounts.for_sale})
        </Button>
        <Button
          variant={selectedStatus === "for_rent" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedStatus("for_rent")}
          className={selectedStatus === "for_rent" ? "bg-blue-600 hover:bg-blue-700" : ""}
        >
          En Alquiler ({statusCounts.for_rent})
        </Button>
        <Button
          variant={selectedStatus === "draft" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedStatus("draft")}
          className={selectedStatus === "draft" ? "bg-blue-600 hover:bg-blue-700" : ""}
        >
          Borradores ({statusCounts.draft})
        </Button>
        <Button
          variant={selectedStatus === "sold" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedStatus("sold")}
          className={selectedStatus === "sold" ? "bg-blue-600 hover:bg-blue-700" : ""}
        >
          Vendidas ({statusCounts.sold})
        </Button>
        <Button
          variant={selectedStatus === "rented" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedStatus("rented")}
          className={selectedStatus === "rented" ? "bg-blue-600 hover:bg-blue-700" : ""}
        >
          Alquiladas ({statusCounts.rented})
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Propiedades ({filteredProperties.length})</CardTitle>
          <CardDescription>
            Administra el estado y la información de tus propiedades
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredProperties.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Propiedad</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Ubicación</TableHead>
                  <TableHead>Precio</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead className="text-right">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProperties.map((property) => {
                  const statusInfo = getStatusBadge(property.status);
                  return (
                    <TableRow key={property._id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
                            {property.images && property.images.length > 0 ? (
                              <Image
                                src={property.images[0]}
                                alt={property.title}
                                fill
                                className="object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <Building2 className="h-6 w-6 text-gray-400" />
                              </div>
                            )}
                          </div>
                          <div>
                            <div>
                              <p className="font-medium line-clamp-1">{property.title}</p>
                              <p className="text-sm text-muted-foreground">
                                {property.area} m² • {property.bedrooms || 0} hab • {property.bathrooms || 0} baños
                              </p>
                              {/* Badges de destacadas */}
                              <div className="flex gap-1 mt-1">
                                {getPropertyStatus(property._id).isFeatured && (
                                  <Badge className="bg-yellow-100 text-yellow-800 text-xs">
                                    <Star className="h-3 w-3 mr-1" />
                                    Destacada
                                  </Badge>
                                )}
                                {getPropertyStatus(property._id).isPremium && (
                                  <Badge className="bg-purple-100 text-purple-800 text-xs">
                                    <Crown className="h-3 w-3 mr-1" />
                                    Premium Home
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">{getTypeLabel(property.type)}</span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{property.city}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-3 w-3 text-muted-foreground" />
                          <span className="font-medium">
                            {formatPrice(property.price, property.currency)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={statusInfo.color}>
                          {statusInfo.label}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem asChild>
                              <Link href={`/properties/${property._id}`}>
                                <Eye className="h-4 w-4 mr-2" />
                                Ver Detalles
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/dashboard/properties/edit/${property._id}`}>
                                <Edit className="h-4 w-4 mr-2" />
                                Editar
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuLabel>Cambiar Estado</DropdownMenuLabel>
                            {property.status !== "for_sale" && (
                              <DropdownMenuItem 
                                onClick={() => handleUpdateStatus(property._id, "for_sale")}
                              >
                                Marcar como En Venta
                              </DropdownMenuItem>
                            )}
                            {property.status !== "for_rent" && (
                              <DropdownMenuItem 
                                onClick={() => handleUpdateStatus(property._id, "for_rent")}
                              >
                                Marcar como En Alquiler
                              </DropdownMenuItem>
                            )}
                            {property.status !== "sold" && (
                              <DropdownMenuItem 
                                onClick={() => handleUpdateStatus(property._id, "sold")}
                              >
                                Marcar como Vendida
                              </DropdownMenuItem>
                            )}
                            {property.status !== "rented" && (
                              <DropdownMenuItem 
                                onClick={() => handleUpdateStatus(property._id, "rented")}
                              >
                                Marcar como Alquilada
                              </DropdownMenuItem>
                            )}
                            {property.status !== "draft" && (
                              <DropdownMenuItem 
                                onClick={() => handleUpdateStatus(property._id, "draft")}
                              >
                                Marcar como Borrador
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuLabel>Promociones</DropdownMenuLabel>
                            {getPropertyStatus(property._id).canFeature ? (
                              <DropdownMenuItem 
                                onClick={() => handleFeatureProperty(property._id)}
                                disabled={!hasCreditsFor('feature')}
                              >
                                <Star className="h-4 w-4 mr-2 text-yellow-600" />
                                Destacar Propiedad (10 créditos)
                                {!hasCreditsFor('feature') && (
                                  <span className="ml-2 text-red-500 text-xs">Sin créditos</span>
                                )}
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem 
                                onClick={() => handleUnfeatureProperty(property._id)}
                              >
                                <Star className="h-4 w-4 mr-2 text-yellow-600" />
                                Quitar Destacado
                              </DropdownMenuItem>
                            )}
                            {getPropertyStatus(property._id).canPremium ? (
                              <DropdownMenuItem 
                                onClick={() => handlePromoteToPremium(property._id)}
                                disabled={!hasCreditsFor('premium')}
                              >
                                <Crown className="h-4 w-4 mr-2 text-purple-600" />
                                Premium Home (25 créditos)
                                {!hasCreditsFor('premium') && (
                                  <span className="ml-2 text-red-500 text-xs">Sin créditos</span>
                                )}
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem 
                                onClick={() => handleRemoveFromPremium(property._id)}
                              >
                                <Crown className="h-4 w-4 mr-2 text-purple-600" />
                                Quitar Premium
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleDeleteProperty(property._id)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Eliminar
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-12">
              <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {selectedStatus === "all" ? "No tienes propiedades aún" : `No hay propiedades ${getStatusBadge(selectedStatus).label.toLowerCase()}`}
              </h3>
              <p className="text-gray-500 mb-6">
                {selectedStatus === "all" 
                  ? "Comienza publicando tu primera propiedad en el marketplace."
                  : `Cambia los filtros para ver propiedades con otros estados.`
                }
              </p>
              {selectedStatus === "all" && (
                <Link href="/dashboard/properties/new">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="h-4 w-4 mr-2" />
                    Publicar Primera Propiedad
                  </Button>
                </Link>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 