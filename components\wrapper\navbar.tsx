"use client";
import { cn } from "@/lib/utils";
import { useAuth } from "@clerk/nextjs";
import { motion } from "framer-motion";
import { 
  Menu, 
  X, 
  Home,
  Building2, 
  Heart, 
  User,
  Plus,
  Settings,
  LogOut,
  Phone,
  Info
} from "lucide-react";
import Link from "next/link";
import * as React from "react";
import { Button } from "../ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>etHeader, <PERSON>et<PERSON><PERSON>le, She<PERSON>Trigger } from "../ui/sheet";
import { UserProfile } from "../user-profile";
import { SignInButton, SignUpButton } from "@clerk/nextjs";
import { useState } from "react";


const navigation = [
  {
    name: 'Propiedades',
    href: '/properties',
    icon: Building2,
    description: 'Explora todas las propiedades disponibles'
  },
  {
    name: 'Favoritos',
    href: '/favorites',
    icon: Heart,
    description: 'Tus propiedades guardadas'
  }
];



export default function NavBar() {
  const { userId } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const closeMobileMenu = () => setIsMobileMenuOpen(false);

  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="fixed top-0 left-0 right-0 z-50 border-b backdrop-blur-md bg-white/95 border-gray-200 shadow-sm"
    >
      <div className="container mx-auto">
        <div className="flex h-16 items-center justify-between px-4">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Home className="h-5 w-5 text-white" />
              </div>
              <span className="font-bold text-xl text-gray-900">Inmova</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center gap-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
              >
                <item.icon className="h-4 w-4" />
                {item.name}
              </Link>
            ))}


          </nav>

          {/* Desktop Auth & User Menu */}
          <div className="hidden lg:flex items-center gap-3">
            {userId ? (
              <div className="flex items-center gap-2">
                <Link href="/dashboard">
                  <Button variant="outline" size="sm" className="border-blue-200 text-blue-600 hover:bg-blue-50">
                    <Plus className="h-4 w-4 mr-2" />
                    Publicar
                  </Button>
                </Link>
                <UserProfile />
              </div>
            ) : (
              <div className="flex items-center gap-3">
                <SignInButton mode="modal">
                  <Button variant="ghost" size="sm" className="text-gray-600 hover:text-blue-600">
                    Iniciar Sesión
                  </Button>
                </SignInButton>
                <SignUpButton mode="modal">
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                    Registrarse
                  </Button>
                </SignUpButton>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden flex items-center gap-2">
            {userId && (
              <Link href="/dashboard">
                <Button variant="outline" size="sm" className="border-blue-200 text-blue-600">
                  <Plus className="h-4 w-4" />
                </Button>
              </Link>
            )}
            
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                <SheetHeader>
                  <SheetTitle className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
                      <Home className="h-4 w-4 text-white" />
                    </div>
                    Inmova
                  </SheetTitle>
                </SheetHeader>

                <div className="mt-8 space-y-6">
                  {/* Auth Section for Mobile */}
                  {!userId && (
                    <div className="space-y-3 pb-6 border-b border-gray-200">
                      <SignInButton mode="modal">
                        <Button variant="outline" className="w-full justify-start" onClick={closeMobileMenu}>
                          <User className="h-4 w-4 mr-2" />
                          Iniciar Sesión
                        </Button>
                      </SignInButton>
                      <SignUpButton mode="modal">
                        <Button className="w-full bg-blue-600 hover:bg-blue-700" onClick={closeMobileMenu}>
                          <User className="h-4 w-4 mr-2" />
                          Registrarse
                        </Button>
                      </SignUpButton>
                    </div>
                  )}

                  {/* Navigation Links */}
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-900 mb-3">Navegación</h3>
                    {navigation.map((item) => (
                      <Link
                        key={item.name}
                        href={item.href}
                        onClick={closeMobileMenu}
                        className="flex items-center gap-3 px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      >
                        <item.icon className="h-5 w-5" />
                        <div>
                          <div className="font-medium">{item.name}</div>
                          <div className="text-xs text-gray-500">{item.description}</div>
                        </div>
                      </Link>
                    ))}
                  </div>



                  {/* Additional Links */}
                  <div className="space-y-2 pt-4 border-t border-gray-200">
                    <h3 className="font-medium text-gray-900 mb-3">Más</h3>
                    <Link
                      href="/contact"
                      onClick={closeMobileMenu}
                      className="flex items-center gap-3 px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    >
                      <Phone className="h-5 w-5" />
                      <span className="font-medium">Contacto</span>
                    </Link>
                    <Link
                      href="/about"
                      onClick={closeMobileMenu}
                      className="flex items-center gap-3 px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    >
                      <Info className="h-5 w-5" />
                      <span className="font-medium">Acerca de</span>
                    </Link>
                  </div>

                  {/* User Profile for Mobile */}
                  {userId && (
                    <div className="pt-4 border-t border-gray-200">
                      <UserProfile />
                    </div>
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </motion.header>
  );
}
