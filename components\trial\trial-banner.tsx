"use client";

import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Crown, Zap, ArrowRight } from "lucide-react";
import Link from "next/link";
import { useUser } from "@clerk/nextjs";

export function TrialBanner() {
  const { user } = useUser();
  const trialStatus = useQuery(api.subscriptions.checkTrialStatus);
  const userSubscription = useQuery(api.subscriptions.getUserSubscription);

  // No mostrar si no hay usuario
  if (!user) return null;

  // No mostrar si está cargando
  if (trialStatus === undefined || userSubscription === undefined) return null;

  // No mostrar si no tiene acceso a trial (es buyer)
  if (!trialStatus.hasAccess && trialStatus.reason === "Sin suscripción") return null;

  // Trial activo
  if (trialStatus.isTrialActive && trialStatus.daysRemaining !== undefined) {
    const isLastDays = trialStatus.daysRemaining <= 3;
    
    return (
      <Card className={`border-2 ${isLastDays ? 'border-orange-200 bg-orange-50' : 'border-blue-200 bg-blue-50'} mb-6`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-full ${isLastDays ? 'bg-orange-100' : 'bg-blue-100'}`}>
                {isLastDays ? (
                  <Clock className={`h-5 w-5 ${isLastDays ? 'text-orange-600' : 'text-blue-600'}`} />
                ) : (
                  <Crown className="h-5 w-5 text-blue-600" />
                )}
              </div>
              
              <div>
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold text-gray-900">
                    {isLastDays ? '⚠️ Trial por expirar' : '🎉 Trial Premium Activo'}
                  </h3>
                  <Badge variant={isLastDays ? "destructive" : "default"} className="text-xs">
                    {trialStatus.daysRemaining} día{trialStatus.daysRemaining !== 1 ? 's' : ''} restante{trialStatus.daysRemaining !== 1 ? 's' : ''}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">
                  {isLastDays 
                    ? 'Tu trial expira pronto. ¡Actualiza ahora para mantener todas las funciones premium!'
                    : 'Tienes acceso completo a todas las funciones premium. ¡Aprovecha al máximo tu trial!'
                  }
                </p>
              </div>
            </div>

            <div className="flex gap-2">
              {isLastDays && (
                <Link href="/dashboard/finance">
                  <Button size="sm" className="bg-orange-600 hover:bg-orange-700">
                    <Zap className="h-4 w-4 mr-1" />
                    Actualizar Ahora
                  </Button>
                </Link>
              )}
              
              <Link href="/dashboard/finance">
                <Button variant="outline" size="sm">
                  Ver Planes
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Trial expirado
  if (trialStatus.trialExpired) {
    return (
      <Card className="border-2 border-gray-200 bg-gray-50 mb-6">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-gray-100">
                <Clock className="h-5 w-5 text-gray-600" />
              </div>
              
              <div>
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold text-gray-900">Trial Expirado</h3>
                  <Badge variant="secondary" className="text-xs">
                    Plan Gratuito
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">
                  Tu trial de 15 días ha expirado. Actualiza a Premium para recuperar todas las funciones.
                </p>
              </div>
            </div>

            <Link href="/dashboard/finance">
              <Button size="sm">
                <Crown className="h-4 w-4 mr-1" />
                Actualizar a Premium
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Usuario premium (sin trial)
  if (trialStatus.hasAccess && !trialStatus.isTrialActive && userSubscription?.isPremium) {
    return (
      <Card className="border-2 border-green-200 bg-green-50 mb-6">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-green-100">
                <Crown className="h-5 w-5 text-green-600" />
              </div>
              
              <div>
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold text-gray-900">✨ Usuario Premium</h3>
                  <Badge className="text-xs bg-green-600">
                    Activo
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">
                  Tienes acceso completo a todas las funciones premium. ¡Gracias por tu suscripción!
                </p>
              </div>
            </div>

            <Link href="/dashboard/finance">
              <Button variant="outline" size="sm">
                Gestionar Suscripción
                <ArrowRight className="h-4 w-4 ml-1" />
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    );
  }

  // No mostrar nada para otros casos
  return null;
}

// Componente más pequeño para sidebar
export function TrialStatusWidget() {
  const trialStatus = useQuery(api.subscriptions.checkTrialStatus);

  if (!trialStatus?.isTrialActive) return null;

  const isLastDays = (trialStatus.daysRemaining || 0) <= 3;

  return (
    <div className={`p-3 rounded-lg ${isLastDays ? 'bg-orange-50 border border-orange-200' : 'bg-blue-50 border border-blue-200'} mb-4`}>
      <div className="flex items-center gap-2 mb-2">
        <Crown className={`h-4 w-4 ${isLastDays ? 'text-orange-600' : 'text-blue-600'}`} />
        <span className="text-sm font-medium">Trial Premium</span>
      </div>
      
      <div className="text-xs text-gray-600 mb-2">
        {trialStatus.daysRemaining} día{trialStatus.daysRemaining !== 1 ? 's' : ''} restante{trialStatus.daysRemaining !== 1 ? 's' : ''}
      </div>
      
      <Link href="/dashboard/finance">
        <Button size="sm" variant={isLastDays ? "default" : "outline"} className="w-full text-xs">
          {isLastDays ? 'Actualizar' : 'Ver Planes'}
        </Button>
      </Link>
    </div>
  );
}
