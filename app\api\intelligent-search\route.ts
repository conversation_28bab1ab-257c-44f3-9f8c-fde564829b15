import { NextRequest, NextResponse } from "next/server";
import { api } from "@/convex/_generated/api";
import { fetchMutation, fetchQuery, fetchAction } from "convex/nextjs";

export async function POST(request: NextRequest) {
  try {
    const { query } = await request.json();
    
    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { success: false, error: "Query is required" },
        { status: 400 }
      );
    }

    // Usar la función pública de búsqueda inteligente
    const result = await fetchAction(api.properties.intelligentSearch, {
      searchText: query
    });

    // También extraer los criterios para mostrarlos en la UI
    const extractedCriteria = await extractCriteriaForUI(query);

    return NextResponse.json({
      success: true,
      properties: result.properties,
      total: result.total,
      criteria: extractedCriteria
    });

  } catch (error) {
    console.error("Error in intelligent search:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Función auxiliar para extraer criterios (versión simplificada para la UI)
async function extractCriteriaForUI(query: string) {
  const text = query.toLowerCase();
  const criteria: any = {};
  
  // Extracciones básicas para mostrar en la UI
  if (text.includes('apartamento') || text.includes('depto')) {
    criteria.type = 'apartment';
  } else if (text.includes('casa')) {
    criteria.type = 'house';
  } else if (text.includes('oficina')) {
    criteria.type = 'office';
  }
  
  // Precio
  const priceMatch = text.match(/(\d+)k|(\d+)\s*mil|hasta\s+(\d+)/);
  if (priceMatch) {
    const price = parseInt(priceMatch[1] || priceMatch[2] || priceMatch[3]);
    criteria.maxPrice = priceMatch[1] ? price * 1000 : price;
  }
  
  // Habitaciones
  const bedroomMatch = text.match(/(\d+)\s*(cuartos?|habitacion)/);
  if (bedroomMatch) {
    criteria.bedrooms = parseInt(bedroomMatch[1]);
  }
  
  // Baños
  const bathroomMatch = text.match(/(\d+)\s*(baños?|banos?)/);
  if (bathroomMatch) {
    criteria.bathrooms = parseInt(bathroomMatch[1]);
  }
  
  // Zonas
  const zoneMatches = text.match(/zona\s*(\d+)/g);
  if (zoneMatches) {
    criteria.zones = zoneMatches.map(z => z.replace('zona', '').trim());
  }
  
  // Amenidades comunes
  const amenities = [];
  if (text.includes('piscina') || text.includes('pisina')) amenities.push('piscina');
  if (text.includes('gimnasio')) amenities.push('gimnasio');
  if (text.includes('jardín') || text.includes('jardin')) amenities.push('jardín');
  if (text.includes('balcón') || text.includes('balcon')) amenities.push('balcón');
  if (text.includes('parking') || text.includes('garage')) amenities.push('parking');
  
  if (amenities.length > 0) {
    criteria.amenities = amenities;
  }
  
  // Keywords
  const keywords = [];
  if (text.includes('moderno')) keywords.push('moderno');
  if (text.includes('amueblado')) keywords.push('amueblado');
  if (text.includes('cerca del metro')) keywords.push('cerca del metro');
  if (text.includes('premium')) keywords.push('premium');
  
  if (keywords.length > 0) {
    criteria.keywords = keywords;
  }
  
  return criteria;
} 