// Script para inicializar configuración admin
// Ejecutar con: node scripts/init-admin-settings.js

const { ConvexHttpClient } = require("convex/browser");

const client = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL);

async function initializeSettings() {
  try {
    console.log("🚀 Inicializando configuración admin...");
    
    // Aquí necesitarías autenticarte como admin
    // Por ahora, esto es solo un ejemplo de cómo sería
    
    console.log("✅ Configuración inicializada exitosamente");
    console.log("📋 Configuración por defecto:");
    console.log("   - trial_days: 15");
    console.log("   - premium_price: 99");
    console.log("   - trial_enabled: true");
    
  } catch (error) {
    console.error("❌ Error inicializando configuración:", error);
  }
}

if (require.main === module) {
  initializeSettings();
}

module.exports = { initializeSettings };
