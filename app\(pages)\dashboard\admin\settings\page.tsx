"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { 
  Settings, 
  Clock, 
  DollarSign, 
  Users, 
  TrendingUp,
  Save,
  RefreshCw,
  Calendar,
  Crown
} from "lucide-react";
import { toast } from "sonner";
import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";

export default function AdminSettingsPage() {
  const { user } = useUser();
  const router = useRouter();
  
  // Queries
  const currentUser = useQuery(api.users.getCurrentUser);
  const adminSettings = useQuery(api.admin.getAdminSettings);
  const trialStats = useQuery(api.admin.getTrialStats);
  
  // Mutations
  const updateSetting = useMutation(api.admin.updateSetting);
  const initializeSettings = useMutation(api.admin.initializeDefaultSettings);
  const extendUserTrial = useMutation(api.admin.extendUserTrial);
  
  // State
  const [isLoading, setIsLoading] = useState(false);
  const [extendTrialForm, setExtendTrialForm] = useState({
    userId: "",
    additionalDays: 7,
    reason: ""
  });

  // Verificar permisos de admin
  if (currentUser === undefined) {
    return <div>Cargando...</div>;
  }

  if (!currentUser || currentUser.role !== 'admin') {
    router.push('/dashboard');
    return <div>Acceso denegado</div>;
  }

  // Inicializar configuración si no existe
  const handleInitializeSettings = async () => {
    setIsLoading(true);
    try {
      await initializeSettings();
      toast.success("Configuración inicializada correctamente");
    } catch (error) {
      toast.error("Error al inicializar configuración");
    } finally {
      setIsLoading(false);
    }
  };

  // Actualizar configuración
  const handleUpdateSetting = async (key: string, value: any, description?: string) => {
    setIsLoading(true);
    try {
      await updateSetting({ key, value, description });
      toast.success(`Configuración "${key}" actualizada`);
    } catch (error) {
      toast.error("Error al actualizar configuración");
    } finally {
      setIsLoading(false);
    }
  };

  // Extender trial de usuario
  const handleExtendTrial = async () => {
    if (!extendTrialForm.userId || !extendTrialForm.additionalDays) {
      toast.error("Por favor completa todos los campos");
      return;
    }

    setIsLoading(true);
    try {
      await extendUserTrial({
        userId: extendTrialForm.userId,
        additionalDays: extendTrialForm.additionalDays,
        reason: extendTrialForm.reason
      });
      toast.success(`Trial extendido por ${extendTrialForm.additionalDays} días`);
      setExtendTrialForm({ userId: "", additionalDays: 7, reason: "" });
    } catch (error: any) {
      toast.error(error.message || "Error al extender trial");
    } finally {
      setIsLoading(false);
    }
  };

  if (!adminSettings) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Configuración Admin
            </CardTitle>
            <CardDescription>
              Parece que la configuración no está inicializada
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleInitializeSettings} disabled={isLoading}>
              {isLoading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Settings className="h-4 w-4 mr-2" />
              )}
              Inicializar Configuración
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Configuración del Sistema</h1>
        <p className="text-muted-foreground">
          Gestiona trials, precios y configuraciones globales
        </p>
      </div>

      {/* Estadísticas de Trial */}
      {trialStats && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Usuarios</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{trialStats.totalUsers}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Trials Activos</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{trialStats.activeTrials}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Suscripciones Pagadas</CardTitle>
              <Crown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{trialStats.paidSubscriptions}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tasa de Conversión</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {trialStats.conversionRate.toFixed(1)}%
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        {/* Configuración de Trial */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Configuración de Trial
            </CardTitle>
            <CardDescription>
              Configura la duración y disponibilidad del trial
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="trial_days">Días de Trial</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="trial_days"
                  type="number"
                  defaultValue={adminSettings.trial_days?.value || 15}
                  min="1"
                  max="90"
                  onBlur={(e) => {
                    const value = parseInt(e.target.value);
                    if (value && value !== adminSettings.trial_days?.value) {
                      handleUpdateSetting("trial_days", value, "Días de trial para nuevos usuarios seller/agent");
                    }
                  }}
                />
                <Badge variant="secondary">días</Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                Duración del trial para nuevos usuarios seller/agent
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="trial_enabled">Trial Habilitado</Label>
                <p className="text-xs text-muted-foreground">
                  Activar/desactivar el sistema de trial
                </p>
              </div>
              <Switch
                id="trial_enabled"
                defaultChecked={adminSettings.trial_enabled?.value || true}
                onCheckedChange={(checked) => {
                  handleUpdateSetting("trial_enabled", checked, "Si el sistema de trial está habilitado");
                }}
              />
            </div>
          </CardContent>
        </Card>

        {/* Configuración de Precios */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Configuración de Precios
            </CardTitle>
            <CardDescription>
              Configura los precios de los planes premium
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="premium_price">Precio Premium (USD)</Label>
              <div className="flex items-center gap-2">
                <span className="text-sm">$</span>
                <Input
                  id="premium_price"
                  type="number"
                  defaultValue={adminSettings.premium_price?.value || 99}
                  min="1"
                  max="999"
                  onBlur={(e) => {
                    const value = parseInt(e.target.value);
                    if (value && value !== adminSettings.premium_price?.value) {
                      handleUpdateSetting("premium_price", value, "Precio mensual del plan premium en USD");
                    }
                  }}
                />
                <Badge variant="secondary">USD/mes</Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                Precio mensual del plan premium
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Extender Trial Manual */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Extender Trial Manualmente
          </CardTitle>
          <CardDescription>
            Extiende el trial de un usuario específico
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label htmlFor="userId">ID del Usuario</Label>
              <Input
                id="userId"
                placeholder="user_123456..."
                value={extendTrialForm.userId}
                onChange={(e) => setExtendTrialForm(prev => ({ ...prev, userId: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="additionalDays">Días Adicionales</Label>
              <Input
                id="additionalDays"
                type="number"
                min="1"
                max="90"
                value={extendTrialForm.additionalDays}
                onChange={(e) => setExtendTrialForm(prev => ({ ...prev, additionalDays: parseInt(e.target.value) }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="reason">Razón (Opcional)</Label>
              <Input
                id="reason"
                placeholder="Soporte al cliente..."
                value={extendTrialForm.reason}
                onChange={(e) => setExtendTrialForm(prev => ({ ...prev, reason: e.target.value }))}
              />
            </div>
          </div>

          <Button onClick={handleExtendTrial} disabled={isLoading}>
            {isLoading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Calendar className="h-4 w-4 mr-2" />
            )}
            Extender Trial
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
