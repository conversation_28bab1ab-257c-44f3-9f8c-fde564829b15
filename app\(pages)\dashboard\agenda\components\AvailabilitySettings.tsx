"use client";

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Clock, Save, Calendar } from 'lucide-react';
import { useAlert } from "@/hooks/use-alert";

interface AvailabilitySettingsProps {
  userId: string;
}

const DAYS_OF_WEEK = [
  { value: 1, label: 'Lu<PERSON>', short: 'L' },
  { value: 2, label: 'Martes', short: 'M' },
  { value: 3, label: 'Miércoles', short: 'X' },
  { value: 4, label: 'Jueves', short: 'J' },
  { value: 5, label: 'Viernes', short: 'V' },
  { value: 6, label: '<PERSON><PERSON>bad<PERSON>', short: 'S' },
  { value: 0, label: '<PERSON>', short: 'D' },
];

export function AvailabilitySettings({ userId }: AvailabilitySettingsProps) {
  const [availability, setAvailability] = useState<Record<number, any>>({});
  const [isSaving, setIsSaving] = useState(false);
  const { showSystemMessage, showCriticalError } = useAlert();

  // Query para obtener disponibilidad actual
  const currentAvailability = useQuery(api.appointments.getUserAvailability, { userId });

  // Mutation para actualizar disponibilidad
  const setAvailabilityMutation = useMutation(api.appointments.setAvailability);

  // Inicializar estado con datos actuales
  useEffect(() => {
    if (currentAvailability) {
      const availabilityMap: Record<number, any> = {};
      
      // Inicializar todos los días con valores por defecto
      DAYS_OF_WEEK.forEach(day => {
        availabilityMap[day.value] = {
          isEnabled: false,
          startTime: '09:00',
          endTime: '17:00',
          slotDuration: 60,
          breakTime: 15,
        };
      });

      // Sobrescribir con datos existentes
      currentAvailability.forEach(item => {
        availabilityMap[item.dayOfWeek] = {
          isEnabled: item.isEnabled,
          startTime: item.startTime,
          endTime: item.endTime,
          slotDuration: item.slotDuration || 60,
          breakTime: item.breakTime || 15,
        };
      });

      setAvailability(availabilityMap);
    }
  }, [currentAvailability]);

  const handleDayToggle = (dayValue: number, enabled: boolean) => {
    setAvailability(prev => ({
      ...prev,
      [dayValue]: {
        ...prev[dayValue],
        isEnabled: enabled,
      }
    }));
  };

  const handleTimeChange = (dayValue: number, field: string, value: string | number) => {
    setAvailability(prev => ({
      ...prev,
      [dayValue]: {
        ...prev[dayValue],
        [field]: value,
      }
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Guardar configuración para cada día
      const promises = DAYS_OF_WEEK.map(day => {
        const dayAvailability = availability[day.value];
        if (dayAvailability) {
          return setAvailabilityMutation({
            userId,
            dayOfWeek: day.value as any,
            startTime: dayAvailability.startTime,
            endTime: dayAvailability.endTime,
            isEnabled: dayAvailability.isEnabled,
            timezone: 'America/Guatemala', // Zona horaria para Guatemala
            slotDuration: dayAvailability.slotDuration,
            breakTime: dayAvailability.breakTime,
          });
        }
        return Promise.resolve();
      });

      await Promise.all(promises);
      showSystemMessage('Disponibilidad guardada correctamente');
    } catch (error) {
      console.error('Error saving availability:', error);
      showCriticalError('Error al guardar la disponibilidad');
    } finally {
      setIsSaving(false);
    }
  };

  const setQuickSchedule = (type: 'business' | 'extended' | 'weekend') => {
    const newAvailability = { ...availability };

    DAYS_OF_WEEK.forEach(day => {
      if (type === 'business') {
        // Horario de oficina: L-V 9:00-17:00
        newAvailability[day.value] = {
          ...newAvailability[day.value],
          isEnabled: day.value >= 1 && day.value <= 5,
          startTime: '09:00',
          endTime: '17:00',
        };
      } else if (type === 'extended') {
        // Horario extendido: L-S 8:00-20:00
        newAvailability[day.value] = {
          ...newAvailability[day.value],
          isEnabled: day.value >= 1 && day.value <= 6,
          startTime: '08:00',
          endTime: '20:00',
        };
      } else if (type === 'weekend') {
        // Solo fines de semana: S-D 10:00-16:00
        newAvailability[day.value] = {
          ...newAvailability[day.value],
          isEnabled: day.value === 0 || day.value === 6,
          startTime: '10:00',
          endTime: '16:00',
        };
      }
    });

    setAvailability(newAvailability);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Configuración de Disponibilidad
          </CardTitle>
          <p className="text-sm text-gray-600">
            Define cuándo estás disponible para recibir citas
          </p>
        </CardHeader>
        <CardContent>
          {/* Horarios rápidos */}
          <div className="mb-6">
            <Label className="text-sm font-medium mb-3 block">Horarios Predefinidos</Label>
            <div className="flex gap-2 flex-wrap">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setQuickSchedule('business')}
                className="text-xs"
              >
                Horario de Oficina (L-V 9-17h)
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setQuickSchedule('extended')}
                className="text-xs"
              >
                Horario Extendido (L-S 8-20h)
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setQuickSchedule('weekend')}
                className="text-xs"
              >
                Solo Fines de Semana (S-D 10-16h)
              </Button>
            </div>
          </div>

          {/* Configuración por día */}
          <div className="space-y-4">
            {DAYS_OF_WEEK.map(day => {
              const dayAvailability = availability[day.value] || {};
              
              return (
                <div key={day.value} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium text-blue-600">
                        {day.short}
                      </div>
                      <span className="font-medium">{day.label}</span>
                    </div>
                    <Switch
                      checked={dayAvailability.isEnabled || false}
                      onCheckedChange={(checked) => handleDayToggle(day.value, checked)}
                    />
                  </div>

                  {dayAvailability.isEnabled && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 ml-11">
                      <div>
                        <Label className="text-xs text-gray-600">Hora Inicio</Label>
                        <Input
                          type="time"
                          value={dayAvailability.startTime || '09:00'}
                          onChange={(e) => handleTimeChange(day.value, 'startTime', e.target.value)}
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label className="text-xs text-gray-600">Hora Fin</Label>
                        <Input
                          type="time"
                          value={dayAvailability.endTime || '17:00'}
                          onChange={(e) => handleTimeChange(day.value, 'endTime', e.target.value)}
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label className="text-xs text-gray-600">Duración (min)</Label>
                        <Input
                          type="number"
                          min="15"
                          max="240"
                          step="15"
                          value={dayAvailability.slotDuration || 60}
                          onChange={(e) => handleTimeChange(day.value, 'slotDuration', parseInt(e.target.value))}
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label className="text-xs text-gray-600">Descanso (min)</Label>
                        <Input
                          type="number"
                          min="0"
                          max="60"
                          step="5"
                          value={dayAvailability.breakTime || 15}
                          onChange={(e) => handleTimeChange(day.value, 'breakTime', parseInt(e.target.value))}
                          className="mt-1"
                        />
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Botón guardar */}
          <div className="flex justify-end mt-6">
            <Button 
              onClick={handleSave}
              disabled={isSaving}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="w-4 h-4 mr-2" />
              {isSaving ? 'Guardando...' : 'Guardar Disponibilidad'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Información adicional */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Configuración General
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium">Zona Horaria</Label>
              <p className="text-sm text-gray-600 mt-1">
                America/Guatemala (UTC-6)
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Tiempo de Reserva Mínimo</Label>
              <p className="text-sm text-gray-600 mt-1">
                Los clientes pueden reservar citas con al menos 2 horas de anticipación
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Cancelación</Label>
              <p className="text-sm text-gray-600 mt-1">
                Las citas pueden cancelarse hasta 1 hora antes del inicio
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 