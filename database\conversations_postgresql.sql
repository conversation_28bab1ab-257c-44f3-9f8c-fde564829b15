-- <PERSON><PERSON>a PostgreSQL para Conversaciones Inmobiliarias

-- Tabla principal de conversaciones
CREATE TABLE conversations (
    id SERIAL PRIMARY KEY,
    chat_id VARCHAR(100) NOT NULL, -- "<EMAIL>"
    session_id UUID DEFAULT gen_random_uuid(),
    user_name VARCHAR(255),
    user_phone VARCHAR(50),
    
    -- Contenido del mensaje
    message_id VARCHAR(255) UNIQUE NOT NULL,
    message_type VARCHAR(20) CHECK (message_type IN ('user', 'assistant')),
    content TEXT NOT NULL,
    message_timestamp TIMESTAMP WITH TIME ZONE,
    
    -- Contexto inmobiliario
    intent VARCHAR(100), -- 'buscar_propiedad', 'pedir_info', 'agendar_cita'
    topic VARCHAR(100), -- 'búsqueda_apartamento', 'seguimiento', 'cierre'
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de leads inmobiliarios
CREATE TABLE leads (
    id SERIAL PRIMARY KEY,
    chat_id VARCHAR(100) UNIQUE NOT NULL,
    user_name VARCHAR(255),
    user_phone VARCHAR(50),
    email VARCHAR(255),
    
    -- Estado del lead
    status VARCHAR(50) DEFAULT 'new', -- 'new', 'interested', 'qualified', 'converted', 'lost'
    source VARCHAR(50) DEFAULT 'whatsapp',
    assigned_agent_id INTEGER,
    
    -- Preferencias de búsqueda
    budget_min DECIMAL(12,2),
    budget_max DECIMAL(12,2),
    property_type VARCHAR(50), -- 'apartment', 'house', 'office'
    preferred_zones JSON, -- ['zona 10', 'zona 14']
    bedrooms_min INTEGER,
    required_amenities JSON, -- ['piscina', 'gimnasio']
    
    -- Seguimiento
    last_interaction TIMESTAMP WITH TIME ZONE,
    next_follow_up TIMESTAMP WITH TIME ZONE,
    follow_up_reason TEXT,
    priority INTEGER DEFAULT 3, -- 1=alta, 2=media, 3=baja
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de interés en propiedades
CREATE TABLE property_interests (
    id SERIAL PRIMARY KEY,
    lead_id INTEGER REFERENCES leads(id),
    property_id VARCHAR(100), -- ID de Convex
    interest_level INTEGER DEFAULT 3, -- 1=muy interesado, 5=no interesado
    interaction_type VARCHAR(50), -- 'viewed', 'asked_info', 'scheduled_visit'
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de tareas de seguimiento
CREATE TABLE follow_up_tasks (
    id SERIAL PRIMARY KEY,
    lead_id INTEGER REFERENCES leads(id),
    task_type VARCHAR(50), -- 'call', 'whatsapp', 'email', 'property_update'
    title VARCHAR(255),
    description TEXT,
    scheduled_for TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    assigned_to INTEGER, -- agent_id
    priority INTEGER DEFAULT 3,
    status VARCHAR(20) DEFAULT 'pending' -- 'pending', 'completed', 'cancelled'
);

-- Índices para performance
CREATE INDEX idx_conversations_chat_id ON conversations(chat_id);
CREATE INDEX idx_conversations_session_id ON conversations(session_id);
CREATE INDEX idx_conversations_created_at ON conversations(created_at);
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_leads_next_follow_up ON leads(next_follow_up);
CREATE INDEX idx_property_interests_lead_id ON property_interests(lead_id);
CREATE INDEX idx_follow_up_tasks_scheduled_for ON follow_up_tasks(scheduled_for);

-- Triggers para actualizar timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON conversations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_leads_updated_at BEFORE UPDATE ON leads 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 