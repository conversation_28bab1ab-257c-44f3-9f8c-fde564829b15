{"name": "Inmo Agent", "nodes": [{"parameters": {"content": "# Entrada WhatsApp", "height": 480, "width": 460, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-17740, 2600], "id": "85d96893-1eff-4fb8-81a2-fe6d7dac547e", "name": "Sticky Note2"}, {"parameters": {"httpMethod": "POST", "path": "inmo-agent", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-17680, 2780], "id": "e04c172a-8efa-4f1b-acb0-e7a93c3b8cc5", "name": "Webhook", "webhookId": "ad567ab3-81ea-45d6-a2e3-a6a9dd204a06"}, {"parameters": {"assignments": {"assignments": [{"id": "24c275ee-aef2-4b03-bf22-4ca69aa05e98", "name": "serverUrl", "value": "={{ $json.body.server_url }}", "type": "string"}, {"id": "a15eb467-42de-4299-a389-667647ccc613", "name": "instanceName", "value": "={{ $json.body.instance }}", "type": "string"}, {"id": "13f6d482-fb2d-42a2-958e-e82273d6679c", "name": "<PERSON><PERSON><PERSON><PERSON>", "value": "={{ $json.body.apikey }}", "type": "string"}, {"id": "557486dd-9057-4d07-8c8a-19583a79b9f9", "name": "message.messageId", "value": "={{ $json.body.data.key.id }}", "type": "string"}, {"id": "79493ec9-a117-496c-88bf-65254c376d20", "name": "message.chatId", "value": "={{ $json.body.data.key.remoteJid }}", "type": "string"}, {"id": "1208a065-1670-40c7-9b9d-5c8c87cedd3f", "name": "message.messageType", "value": "={{ $json.body.data.message.conversation ? 'text': '' }}{{ $json.body.data.message.extendedTextMessage ? 'text': '' }}{{ $json.body.data.message.audioMessage ? 'audio': '' }}{{ $json.body.data.message.imageMessage ? 'image': '' }}{{ $json.body.data.message.videoMessage.url }}", "type": "string"}, {"id": "2e0a8ca5-35b9-4d4d-9005-1e66ac828eeb", "name": "message.messageContent", "value": "={{ $json.body.data.message.extendedTextMessage?.text || '' }}{{ $json.body.data.message.imageMessage?.caption || '' }}{{ $json.body.data.message.conversation || '' }}", "type": "string"}, {"id": "2f72443f-eace-44fa-8a81-5f3bf1ab5e88", "name": "message.messageTimeStamp", "value": "={{ $json.body.date_time.toDateTime().toISO() }}", "type": "string"}, {"id": "a8c0f64c-2ad7-42c8-9af0-eb792f298cdd", "name": "userName", "value": "={{ $json.body.data.pushName }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-17440, 2780], "id": "5fc7538b-80a1-4e75-b9f5-1ab38e6a38d6", "name": "<PERSON>"}, {"parameters": {"content": "# Encolamiento de Mensajes", "height": 920, "width": 1760}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-17140, 2380], "id": "01d4e268-d129-46da-ad72-dd0937e44776", "name": "<PERSON><PERSON>"}, {"parameters": {"operation": "push", "list": "={{ $('Edit Fields').item.json.message.chatId }}", "messageData": "={{ JSON.stringify($('Edit Fields').item.json.message) }}", "tail": true}, "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [-17040, 2740], "id": "49a46468-1e26-468e-b1f4-a4a2363594fe", "name": "push mensaje", "credentials": {"redis": {"id": "JuKDdGWPV1F6WgHC", "name": "Redis account"}}}, {"parameters": {"fieldToSplitOut": "message", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-15760, 2700], "id": "0610da82-efba-43bb-b0e0-8d46e85634a9", "name": "Split Out"}, {"parameters": {"mode": "raw", "jsonOutput": "={{ JSON.parse($json.message) }}", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-15540, 2700], "id": "8df06219-de2a-4f2a-8aa6-fd9d9cb593fa", "name": "<PERSON><PERSON>"}, {"parameters": {"amount": 10}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-16840, 2740], "id": "f91c91e0-a170-4896-918b-438e73dbd5bc", "name": "Wait1", "webhookId": "39be8185-9563-41b4-84d8-3f7650c1b4bf"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "18445dcf-1967-40d1-aec2-6d2196b1364b", "leftValue": "={{ JSON.parse($json.message.last()).messageId }}", "rightValue": "={{ $('Edit Fields').item.json.message.messageId }}", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-16340, 2740], "id": "3653be68-075e-4875-a4a4-47709c9767cb", "name": "If"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-16060, 2980], "id": "********-e40c-4085-93c0-066dbb67caf2", "name": "No Operation, do nothing"}, {"parameters": {"operation": "get", "propertyName": "message", "key": "={{ $('Edit Fields').item.json.message.chatId }}", "options": {}}, "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [-16600, 2740], "id": "f373dc4c-9b89-47d1-9d25-96c04cdc77e7", "name": "obtener todos mensajes", "credentials": {"redis": {"id": "JuKDdGWPV1F6WgHC", "name": "Redis account"}}}, {"parameters": {"operation": "delete", "key": "={{ $('Edit Fields').item.json.message.chatId }}"}, "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [-16020, 2700], "id": "2d4819f6-c1cf-4c53-a3e1-2384c288d3da", "name": "borrar to<PERSON> mensajes", "credentials": {"redis": {"id": "JuKDdGWPV1F6WgHC", "name": "Redis account"}}}, {"parameters": {"content": "# Procesamiento del Mensaje (Texto, Audio, Imagen)", "height": 1240, "width": 2360, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-15260, 2240], "id": "57f1f022-6c5a-450d-b256-c3e4010fd019", "name": "Sticky Note1"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.messageType }}", "rightValue": "audio", "operator": {"type": "string", "operation": "equals"}, "id": "e397999f-f0d4-47db-8619-c188b5e3616b"}], "combinator": "and"}, "renameOutput": true, "outputKey": "audio"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "338e3688-336d-4e2a-a3a9-d81e6bbd4e11", "leftValue": "={{ $json.messageType }}", "rightValue": "image", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "image"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b8f82334-7775-4e5d-9d7c-f6402af55910", "leftValue": "={{ $json.messageType }}", "rightValue": "text", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "text"}]}, "options": {"fallbackOutput": "extra", "renameFallbackOutput": "other"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-15180, 2800], "id": "699483c4-ecba-49c0-b405-327a5aa9f57c", "name": "Switch"}, {"parameters": {"method": "POST", "url": "={{ $('Edit Fields').item.json.serverUrl }}/chat/getBase64FromMediaMessage/{{ $('Edit Fields').item.json.instanceName }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $('Edit Fields').item.json.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "message.key.id", "value": "={{ $json.messageId }}"}, {"name": "convertToMp4", "value": "={{ <PERSON><PERSON><PERSON>(false) }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-14860, 2520], "id": "b9d1c2d3-de70-403e-9de0-7033582c5062", "name": "descargar audio"}, {"parameters": {"operation": "toBinary", "sourceProperty": "base64", "options": {"mimeType": "={{ $json.mimetype }}"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [-14640, 2520], "id": "82b780bb-6aa7-48ae-9025-0ad89fa3e730", "name": "convertir audio"}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-14420, 2520], "id": "a26187cc-36e9-472d-9e4e-a2f47cfc5f34", "name": "OpenAI", "credentials": {"openAiApi": {"id": "KSAvlJBgncGdJAJg", "name": "OpenAi account"}}}, {"parameters": {"method": "POST", "url": "={{ $('Edit Fields').item.json.serverUrl }}/chat/getBase64FromMediaMessage/{{ $('Edit Fields').item.json.instanceName }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $('Edit Fields').item.json.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "message.key.id", "value": "={{ $json.messageId }}"}, {"name": "convertToMp4", "value": "={{ <PERSON><PERSON><PERSON>(false) }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-14820, 2740], "id": "0407f0b2-27d7-47e8-b64d-50ae7aa05aae", "name": "descargar imagen"}, {"parameters": {"operation": "toBinary", "sourceProperty": "base64", "options": {"mimeType": "={{ $json.mimetype }}"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [-14600, 2740], "id": "acf81f41-2b52-4477-8010-37c9331d9a7b", "name": "convertir imagen"}, {"parameters": {"assignments": {"assignments": [{"id": "a54b459d-e8df-47e1-be8c-a8a476ab93c3", "name": "content", "value": "={{ $json.text }}", "type": "string"}, {"id": "1b078c6c-6824-43e2-8bff-8a56fca7a1c9", "name": "timestamp", "value": "={{ $('Json Parse').item.json.messageTimeStamp }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-14200, 2520], "id": "b717d267-5a9e-4f98-b971-334f3f368ad5", "name": "audio content"}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "text": "Describe la imagen", "inputType": "base64", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-14380, 2740], "id": "3e0f8016-4a11-4448-b8fb-028232f2bdf7", "name": "describe imagen", "credentials": {"openAiApi": {"id": "KSAvlJBgncGdJAJg", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "a54b459d-e8df-47e1-be8c-a8a476ab93c3", "name": "content", "value": "=<image>\n{{ $json.content }}\n</image>", "type": "string"}, {"id": "29e2c943-8bb0-4199-84dd-16d098894ddd", "name": "timestamp", "value": "={{ $('Json Parse').item.json.messageTimeStamp }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-14200, 2740], "id": "688ce569-18b9-42f1-b870-0618521e2825", "name": "image content"}, {"parameters": {"assignments": {"assignments": [{"id": "bcea6c99-72fa-44d3-acc6-ecfdec887583", "name": "content", "value": "={{ $json.messageContent }}", "type": "string"}, {"id": "6abb6aff-1f4a-4ab5-aece-c2bf1d27a59d", "name": "timestamp", "value": "={{ $('Json Parse').item.json.messageTimeStamp }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-14200, 2960], "id": "92f80c71-a6ef-4e02-a187-2e4aa9ee8e07", "name": "text content"}, {"parameters": {"assignments": {"assignments": [{"id": "d2599831-cc84-4aa6-acf7-77ed624f72ab", "name": "content", "value": "={{ $json.messages.join(\"\\n\") }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-13060, 2680], "id": "4217eb80-3a15-43f6-a499-3e0d0f37143f", "name": "mensaje"}, {"parameters": {"numberInputs": 3}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-13660, 2680], "id": "6cad7cff-d932-4c0e-85e3-b2dec7d4693e", "name": "<PERSON><PERSON>"}, {"parameters": {"sortFieldsUi": {"sortField": [{"fieldName": "timestamp"}]}, "options": {}}, "type": "n8n-nodes-base.sort", "typeVersion": 1, "position": [-13480, 2680], "id": "c594b3ca-43a3-4649-8b54-4fde4777e032", "name": "Sort"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "content", "renameField": true, "outputFieldName": "messages"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-13300, 2680], "id": "88854596-69ba-4552-b429-bd927365bd3f", "name": "Aggregate"}, {"parameters": {"content": "# <PERSON><PERSON> Inmo", "height": 1220, "width": 780, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-12060, 2180], "id": "cccfc892-8f3a-4d8c-902c-f707a642cbe9", "name": "Sticky Note3"}, {"parameters": {"content": "# Formato de Respuesta por WhatsApp", "height": 1220, "width": 2440, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-10900, 2180], "id": "c17e775b-9db4-4f1d-b676-5d4d9b7ad22e", "name": "Sticky Note4"}, {"parameters": {"promptType": "define", "text": "=User input: {{ $('mensaje').item.json.content }}", "options": {"systemMessage": "=### 👤 HISTORIAL CONVERSACIONAL\n{{ $json.fullContext }}\n\n### 👤 IDENTIDAD\nEres **INMO**, un corredor inmobiliario profesional de **inmo.gt** con más de 20 años de experiencia, especializado en propiedades en Guatemala.  \n**Fecha actual:** {{ $now.format('YYYY-MM-DD') }}  \n\n**Tono:** Profesional, confiable, cercano y experto.  \n**Ejemplo:**  \n- ✅ \"Buenos días. Soy Roberto, su asesor inmobiliario. ¿En qué propiedad puedo ayudarle?\"  \n- ❌ \"¡Hola! ¿Qué tal? ¿Buscás casa o apartamento?\"\n\n---\n\n### 🎯 LÓGICA DE DECISIÓN PRINCIPAL\n\n**ANTES de responder, SIEMPRE analiza el historial conversacional para entender el contexto:**  \n1. ¿Se mostraron propiedades previamente? (Busca el emoji 🏠 en el historial).  \n2. ¿El cliente mostró interés? (Palabras clave: \"sí\", \"me interesa\", \"quiero ver\", \"visitar\", \"me gusta\", \"perfecto\", \"excelente\").  \n3. ¿Es una nueva conversación o una continuación?  \n\n**Acciones según contexto:**  \n- **Cliente mostró interés en una propiedad:** Ir directamente a **Gestión de Visitas**.  \n- **Primera conversación o cliente pide algo nuevo:** Usar **Búsqueda de Propiedades**.  \n- **Cliente pide más detalles de una propiedad:** Usar **Información Detallada**.  \n\n---\n\n### 🔧 FUNCIONES DISPONIBLES\n\n#### 1️⃣ BÚSQUEDA DE PROPIEDADES  \n**Cuándo usar:** Solo cuando el cliente inicia una nueva búsqueda o solicita algo específico.  \n**Función:** `buscar_propiedades`  \n- Usa los términos exactos del cliente (ej. \"apartamento zona 10\").  \n- Muestra **máximo 3 resultados** por respuesta.  \n- Formato obligatorio para mostrar propiedades:  \n\n🏠 **[Título de la Propiedad]** (ID: [id])\n📍 [Ubicación exacta]\n💰 $[precio] USD\n🛏️ [habitaciones] hab | [baños] baños | [metros²] m²\n✨ [Amenidad destacada]\n\n🏠 **[Segunda propiedad...]** (ID: [id])\n📍 [Ubicación...]\n💰 $[precio] USD\n🛏️ [hab/baños/m²]\n✨ [Amenidad...]\n\n**⚠️ IMPORTANTE:** Siempre incluye el ID real que devuelve la búsqueda (ej: j_1732029359_j2d49h59hfjd_j5d9u85_wnp2) para poder usarlo después.\n\n**Siempre termina con:**  \n\"¿Alguna de estas propiedades es de su interés? ¿Le gustaría agendar una visita?\"\n\n#### 2️⃣ INFORMACIÓN DETALLADA DE PROPIEDADES  \n**Cuándo usar:** Cuando el cliente pide más detalles específicos de una propiedad.  \n**Funciones disponibles:**  \n- `buscar_informacion`: Para obtener información general de una propiedad específica  \n- `obtener_contexto_propiedad`: Para obtener contexto detallado y características específicas de una propiedad  \n\n**Uso:**  \n- Siempre usa el `propertyId` exacto de la propiedad mostrada anteriormente  \n- Proporciona información completa y detallada  \n- **Respuesta ejemplo:**  \n\"Le proporciono información detallada sobre [Nombre de la Propiedad]: [detalles]. ¿Le interesaría coordinar una visita?\"\n\n#### 3️⃣ GESTIÓN DE VISITAS (PRIORIDAD MÁXIMA)  \n**Cuándo usar:** Cuando el cliente muestra interés (palabras clave o preguntas sobre disponibilidad).  \n**Proceso obligatorio:**  \n1. Identifica la **última propiedad mostrada** (busca 🏠 en el historial).  \n2. Extrae el `propertyId` exacto de esa propiedad.  \n3. Llama `verificar_disponibilidad_propiedad` con:  \n   - `propertyId`: [ID extraído]  \n   - `preferredDates`: [\"{{ $now.format('YYYY-MM-DD') }}\", \"{{ $now.plus(1, 'day').format('YYYY-MM-DD') }}\", \"{{ $now.plus(2, 'day').format('YYYY-MM-DD') }}\"]  \n4. Muestra los horarios disponibles al cliente.  \n5. **Si el cliente confirma un horario específico, usa `crear_solicitud_cita` con:**\n   - `propertyId`: [ID de la propiedad]\n   - `guestName`: [Nombre del cliente extraído del historial]\n   - `guestEmail`: [Email si se proporcionó]\n   - `guestPhone`: [Teléfono si se proporcionó]\n   - `requestedStartTime`: [Fecha y hora confirmada en formato ISO]\n   - `requestedEndTime`: [Hora de fin estimada en formato ISO]\n   - `type`: \"property_viewing\"\n   - `meetingType`: \"in_person\"\n   - `message`: \"Solicitud de cita generada por IA\"\n\n**Respuesta ejemplo:**  \n\"Excelente elección. Permíteme verificar la disponibilidad para [Nombre de la Propiedad]...\"\n\n---\n\n### 💬 ESTILO DE COMUNICACIÓN PROFESIONAL\n\n**Expresiones profesionales para variar:**  \n- **Saludos:** \"Buenos días\", \"Buenas tardes\", \"Es un placer atenderle\"  \n- **Confirmación:** \"Perfecto\", \"Excelente\", \"Muy bien\", \"Entendido\"  \n- **Preguntas:** \"¿En qué puedo asistirle?\", \"¿Qué tipo de propiedad busca?\", \"¿Cuáles son sus preferencias?\"  \n- **Profesionalismo:** \"Permíteme verificar\", \"Le confirmo que\", \"Con mucho gusto le ayudo\"  \n\n**Saludos iniciales profesionales:**  \n- \"Buenos días. Soy Roberto, su asesor inmobiliario de inmo.gt. ¿En qué propiedad puedo ayudarle hoy?\"  \n- \"Buenas tardes. ¿Qué tipo de propiedad está buscando?\"  \n- \"Es un placer atenderle. ¿Cuáles son sus necesidades inmobiliarias?\"  \n\n**Mantén siempre:**  \n- ✅ Tono respetuoso y profesional  \n- ✅ Lenguaje claro y directo  \n- ✅ Enfoque en el servicio al cliente  \n- ❌ Evita jerga muy casual o coloquialismos excesivos  \n\n---\n\n### 🚨 DETECCIÓN DE INTERÉS  \n**Señales de interés:**  \n- Palabras: \"sí\", \"me interesa\", \"quiero ver\", \"visitar\", \"me gusta\", \"perfecto\", \"excelente\", \"claro\".  \n- Preguntas sobre horarios, disponibilidad o detalles específicos.  \n- Solicitudes explícitas de visita.  \n\n**Respuestas profesionales al detectar interés:**  \n- \"Excelente elección. Verifico la disponibilidad de [Nombre de la Propiedad]...\"  \n- \"Perfecto. Permíteme revisar los horarios disponibles para [Nombre de la Propiedad]...\"  \n- \"Me da mucho gusto su interés. Consulto la agenda para [Nombre de la Propiedad]...\"  \n\n---\n\n### 🔑 MANEJO OBLIGATORIO DE PROPERTY IDs\n\n**CUANDO MUESTRES PROPIEDADES:**  \n- Incluye SIEMPRE el ID real en formato: (ID: [_id_exacto])  \n- Ejemplo: 🏠 **Apartamento Zona 14** (ID: j_1732029359_j2d49h59hfjd_j5d9u85_wnp2)  \n\n**CUANDO EL CLIENTE MUESTRE INTERÉS:**  \n1. **Revisa** tu respuesta anterior donde mostraste propiedades  \n2. **Identifica** cuál le interesa:  \n   - \"Me gusta el segundo\" → Toma el ID de la 2da propiedad  \n   - \"El de zona 14\" → Toma el ID de esa ubicación  \n   - \"Sí\" (después de mostrar UNA) → Usa ese ID  \n3. **Extrae** el ID EXACTO (ej: \"j_1732029359_j2d49h59hfjd_j5d9u85_wnp2\")  \n4. **Usa** ese ID en las funciones correspondientes  \n\n**🚨 REGLAS CRÍTICAS:**  \n- ❌ NUNCA inventes IDs como \"propiedad_zona14\"  \n- ✅ SIEMPRE usa los IDs que devuelve `buscar_propiedades`  \n- ✅ INCLUYE el ID en tu respuesta para referencia futura  \n\n---\n\n### 🚫 LÍMITES Y RESTRICCIONES  \n- **Solo temas inmobiliarios:** Si el cliente pregunta algo no relacionado, responde:  \n  \"Me especializo en asesoría inmobiliaria. ¿En qué propiedad puedo ayudarle?\"  \n- **No inventes información:** Si no tienes datos, di:  \n  \"Permíteme verificar esa información para brindarle datos precisos.\"  \n- **No repitas búsquedas innecesarias:** Usa el historial para evitar duplicar acciones.  \n\n---\n\n### ⚡ EJEMPLOS DE FLUJO CORRECTO  \n\n**Escenario A - Cliente muestra interés:**  \n*Cliente:* \"Me interesa esa casa, quiero verla.\"  \n*Acción:*  \n1. Busca la última propiedad con 🏠 en el historial.  \n2. Extrae el `propertyId`.  \n3. Llama `verificar_disponibilidad_propiedad`.  \n4. Responde: \"Excelente elección. Verifico la disponibilidad para [Casa en Zona 10]...\"  \n\n**Escenario B - Nueva búsqueda:**  \n*Cliente:* \"Busco apartamento en zona 15.\"  \n*Acción:*  \n1. Llama `buscar_propiedades` con \"apartamento zona 15\".  \n2. Muestra hasta 3 resultados en el formato 🏠 con IDs incluidos.  \n3. Termina con: \"¿Alguna de estas propiedades es de su interés? ¿Le gustaría visitarla?\"  \n\n**Escenario C - Cliente pide más detalles:**  \n*Cliente:* \"Dame más información de esa casa en Zona 10.\"  \n*Acción:*  \n1. Identifica el `propertyId` de la casa mencionada.  \n2. Llama `buscar_informacion` o `obtener_contexto_propiedad`.  \n3. Responde: \"Le proporciono los detalles de [Casa en Zona 10]: [info]. ¿Le interesaría coordinar una visita?\"  \n\n---\n\n### 🔄 RECORDATORIOS FINALES  \n- **Siempre revisa el historial conversacional** antes de actuar.  \n- **No repitas acciones innecesarias** (ej. buscar propiedades ya mostradas).  \n- **Prioriza la gestión de visitas** cuando detectes interés.  \n- **Usa la función correcta** según la situación (buscar_informacion vs obtener_contexto_propiedad).  \n- **Mantén consistencia en el tono profesional** durante toda la conversación.  \n- **Proyecta confianza y expertise** como corredor inmobiliario experimentado."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-11760, 2560], "id": "10ad58fe-ee40-462b-8747-f764a457bfde", "name": "AI Agent1"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-12000, 2740], "id": "dbffaa54-2415-45a7-86d0-7b5c90903ea3", "name": "OpenAI Chat Model2", "credentials": {"openAiApi": {"id": "KSAvlJBgncGdJAJg", "name": "OpenAi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "typeVersion": 1, "position": [-10680, 2800], "id": "b2acc604-3220-45c9-84e9-2a51ef8c2eee", "name": "Auto-fixing Output Parser"}, {"parameters": {"jsonSchemaExample": "{\n\t\"response\": {\n      \"part_1\": \"Parte uno de la respuesta\",\n      \"part_2\": \"Parte dos de la respuesta (opcional).\",\n      \"part_3\": \"Parte tres de la respuesta (opcional).\"\n    }\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-10480, 3020], "id": "092f14c6-7a64-4573-9bc4-6743f2242851", "name": "Structured Output Parser"}, {"parameters": {"promptType": "define", "text": "=Texto de entrada:\n{{ $json.output }}", "hasOutputParser": true, "messages": {"messageValues": [{"message": "=# Formatea el texto de entrada de acuerdo a las instrucciones.\n\n---------------\n\n## Instrucciones\n- Devuelve un mensaje de salida dividido en 1, 2, o 3 partes, dependiendo de la lingitud del texto de entrada.\n- El mensaje de salida debe sonar relajado y amigable.\n- Elimina estos caracteres:\n \"*\", \"¿\", \"¡\", \"#\"\n- Utiliza signos de interrogación \"?\" **solo en el final de las frases que sean preguntas.**\n- **No** es necesario que las 3 partes contengan un mensaje.\n- Si el texto de entrada contine una lista, déjala sola en una parte.\n- En la respuesta no se puede devolver mensajes como \"Utilizando la herramienta Conocimiento para buscar...\" o cosas similares.\n- **IMPORTANTE: No añadas ni elimines información esencial del texto de entrada. Respeta fielmente el contenido original. Solamente ajusta la forma (ej. eliminar caracteres, dividir en partes, ajustar estilo) para cumplir las instrucciones dadas.**\n\n## **IMPORTANTE**\n- Si haces bien tu trabajo te voy a pagar un sueldo de $5,000 USD al mes.\n- **SIEMPRE debes completar al menos una parte con texto**."}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [-10760, 2580], "id": "6513f9df-94c4-424e-9a09-77a94c570b95", "name": "Verificador de Respuesta", "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "={{ $('Edit Fields').item.json.serverUrl }}/message/sendText/{{ $('Edit Fields').item.json.instanceName }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $('Edit Fields').item.json.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $('Edit Fields').item.json.message.chatId }}"}, {"name": "text", "value": "={{ $('Verificador de Respuesta').item.json.output.response.part_1 }}"}, {"name": "delay", "value": "={{ 2000 }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-10380, 2580], "id": "ea0e3f7c-d746-44b6-ac6b-8ab48720bfd3", "name": "Enviar Parte1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4b92a1f4-d4bc-4689-87cf-1be40aa49c27", "leftValue": "={{ $('Verificador de Respuesta').item.json.output.response.part_2 }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-10160, 2580], "id": "38b6a5be-1e92-42c0-a15b-3a8ab4e4ef66", "name": "If Parte 2"}, {"parameters": {"amount": "={{ 2+$('Verificador de Respuesta').item.json.output.response.part_2.length*0.01 }}"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-9920, 2480], "id": "c3230ff4-5fcb-4cdb-a18b-d588a70214fe", "name": "Wait", "webhookId": "fccc56ac-adbb-4a82-9af6-1f3a00fa08ff"}, {"parameters": {"method": "POST", "url": "={{ $('Edit Fields').item.json.serverUrl }}/message/sendText/{{ $('Edit Fields').item.json.instanceName }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $('Edit Fields').item.json.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $('Edit Fields').item.json.message.chatId }}"}, {"name": "text", "value": "={{ $('Verificador de Respuesta').item.json.output.response.part_2 }}"}, {"name": "delay", "value": "={{ 2000 }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-9720, 2480], "id": "fe1a7f4f-0eb8-4afb-84d8-3a129b4a655d", "name": "Enviar Parte2"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4b92a1f4-d4bc-4689-87cf-1be40aa49c27", "leftValue": "={{ $('Verificador de Respuesta').item.json.output.response.part_3 }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-9440, 2600], "id": "de4bce08-b61a-4236-b93d-1d7a525d5f25", "name": "If Parte 3"}, {"parameters": {"amount": "={{ 2+$('Verificador de Respuesta').item.json.output.response.part_3.length*0.01 }}"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-9220, 2480], "id": "e86c6557-e478-45fc-a1dd-d82ee5286f88", "name": "Wait2", "webhookId": "610f359e-8fe3-480f-9042-e011a3c9fb43"}, {"parameters": {"method": "POST", "url": "={{ $('Edit Fields').item.json.serverUrl }}/message/sendText/{{ $('Edit Fields').item.json.instanceName }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $('Edit Fields').item.json.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $('Edit Fields').item.json.message.chatId }}"}, {"name": "text", "value": "={{ $('Verificador de Respuesta').item.json.output.response.part_3 }}"}, {"name": "delay", "value": "={{ 2000 }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-8980, 2480], "id": "ce28794c-4919-470e-9f9a-8d78052099e8", "name": "Enviar Parte3"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-8740, 2620], "id": "3eb12252-7d94-4a3d-9c84-4867de2c1b8f", "name": "No Operation, do nothing1"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-10820, 3040], "id": "cdfdafe2-81f0-4c96-9431-fa04f3f1f65d", "name": "4o mini Verificador", "credentials": {"openAiApi": {"id": "KSAvlJBgncGdJAJg", "name": "OpenAi account"}}}, {"parameters": {"toolDescription": "Obtiene información detallada de una propiedad específica", "method": "POST", "url": "https://capable-cod-213.convex.site/getPropertyDetailsForAgent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"propertyId\": \"{{ $fromAI('propertyId', 'Property ID', 'string') }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-11960, 2960], "id": "a3012281-1848-4ff2-bd63-ca3a96d5ee82", "name": "buscar_informacion"}, {"parameters": {"toolDescription": "Busca propiedades según lo que pida el usuario", "method": "POST", "url": "https://capable-cod-213.convex.site/searchPropertiesForAgent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"searchCriteria\": {\n    \"searchText\": \"{{ $fromAI('query', 'El mensaje del usuario', 'string') }}\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-11400, 2900], "id": "df4a7b58-3cc7-4616-b24d-4592b72ca031", "name": "buscar_propiedades"}, {"parameters": {"method": "POST", "url": "https://capable-cod-213.convex.site/saveConversation", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"chatId\": \"{{ $('Edit Fields').item.json.message.chatId }}\",\n  \"userName\": \"{{ $('Edit Fields').item.json.userName }}\",\n  \"messageId\": \"{{ $('Edit Fields').item.json.message.messageId }}\",\n  \"messageType\": \"user\",\n  \"content\": \"{{ $('Edit Fields').item.json.message.messageContent }}\",\n  \"timestamp\": \"{{ $('Edit Fields').item.json.message.messageTimeStamp }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-11140, 2780], "id": "f875523b-4ecd-4fc6-adf9-aa210bc912d0", "name": "Save Chat Convex"}, {"parameters": {"method": "POST", "url": "https://capable-cod-213.convex.site/getChatHistoryForAgent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": " Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"chatId\": \"{{ $('Edit Fields').item.json.message.chatId }}\",\n  \"limit\": 15\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-12660, 2640], "id": "496ab707-9589-4915-bf24-0b85b9c549d8", "name": "Get Chat History"}, {"parameters": {"assignments": {"assignments": [{"id": "5f210319-4ae0-42a6-a130-153b675fd836", "name": "fullContext", "value": "==\"CLIENTE: {{ $('Edit Fields').item.json.userName }}\nFECHA: {{ $now.format('YYYY-MM-DD HH:mm') }}\n\nHISTORIAL COMPLETO:\n{{ $('Get Chat History').item.json.conversations && $('Get Chat History').item.json.conversations.length > 0 ? $('Get Chat History').item.json.conversations.map(c => (c.messageType === 'user' ? 'CLIENTE' : 'TÚ (INMO)') + ': ' + c.content).join('\\n\\n') : 'Primera conversación con este cliente' }}\n\n\n\nMENSAJE ACTUAL: {{ $('Edit Fields').item.json.message.messageContent }}\n\nINSTRUCCIÓN: Revisa todo el historial antes de responder. Si ya mostraste propiedades y el cliente muestra interés, procede directamente a agendar visita.\"", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-12360, 2640], "id": "b7baf54f-839c-4f20-884b-7be217f032d4", "name": "Prepare Context"}, {"parameters": {"toolDescription": "Obtiene información completa de una propiedad incluyendo disponibilidad del propietario y actividad reciente. Úsala para dar contexto antes de gestionar citas.\n\nPARÁMETROS:\n- propertyId: ID exacto de la propiedad\n\nDevuelve: detalles de propiedad, info del propietario, horarios de disponibilidad configurados, citas pendientes.", "method": "POST", "url": "https://capable-cod-213.convex.cloud/api/query", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"path\": \"aiAppointments:getPropertyContextForAI\",\n  \"args\": {\n    \"propertyId\": \"={{ $fromAI('propertyId', 'Property ID', 'string') }}\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-11880, 3180], "id": "0a78d6cf-5132-4ae1-8934-5d4d7711fc4c", "name": "obtener_contexto_propiedad"}, {"parameters": {"toolDescription": "Verifica horarios disponibles para visitar una propiedad específica. Úsala cuando el cliente quiera agendar una cita o visita.\n\nPARÁMETROS:\n- propertyId: ID de la propiedad (debe ser exacto de búsquedas previas)\n- preferredDates: fechas como string separado por comas: \"2024-01-15,2024-01-16,2024-01-17\"\n- duration: duración en minutos (opcional, default 60)\n\nEJEMPLOS:\n\"Quiero ver la propiedad mañana\" → usar fecha de mañana\n\"¿Cuándo puedo visitar?\" → usar próximos 3 días como: \"2024-01-15,2024-01-16,2024-01-17\"", "method": "POST", "url": "https://capable-cod-213.convex.cloud/api/query", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"path\": \"aiAppointments:checkPropertyAvailability\",\n  \"args\": {\n    \"propertyId\": \"{{ $fromAI('propertyId', 'ID exacto de la propiedad del historial (ejemplo: j_1732029359_j2d49h59hfjd_j5d9u85_wnp2)', 'string') }}\",\n    \"preferredDates\": [\"{{ $fromAI('date1', 'Primera fecha YYYY-MM-DD', 'string') }}\", \"{{ $fromAI('date2', 'Segunda fecha YYYY-MM-DD', 'string') }}\", \"{{ $fromAI('date3', 'Tercera fecha YYYY-MM-DD', 'string') }}\"],\n    \"duration\": 60\n  }\n}", "options": {}, "optimizeResponse": true}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-11720, 3060], "id": "1adf5f71-ca7c-42f6-b9f7-07409f5dffed", "name": "verificar_disponibilidad_propiedad"}, {"parameters": {"toolDescription": "Crea una solicitud de cita para visitar una propiedad. Úsala DESPUÉS de verificar disponibilidad y cuando el cliente confirme horario.\n\nPARÁMETROS OBLIGATORIOS:\n- propertyId: ID exacto de la propiedad\n- guestName: nombre del cliente\n- guestEmail: email del cliente\n- requestedStartTime: hora inicio en formato ISO (de verificar_disponibilidad)\n- requestedEndTime: hora fin en formato ISO (de verificar_disponibilidad)\n- type: tipo de cita (property_viewing, consultation, etc.)\n- meetingType: modalidad (in_person, video_call, phone_call)\n\nOPCIONALES:\n- guestPhone: teléfono del cliente\n- message: mensaje adicional", "method": "POST", "url": "https://capable-cod-213.convex.cloud/api/mutation", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"path\": \"aiAppointments:createAIAppointmentRequest\",\n  \"args\": {\n    \"propertyId\": \"={{ $fromAI('propertyId', 'Property ID', 'string') }}\",\n    \"guestName\": \"={{ $fromAI('guestName', 'Guest name', 'string') }}\",\n    \"guestEmail\": \"={{ $fromAI('guestEmail', 'Guest email', 'string') }}\",\n    \"guestPhone\": \"={{ $fromAI('guestPhone', 'Guest phone', 'string') || '' }}\",\n    \"requestedStartTime\": \"={{ $fromAI('requestedStartTime', 'Start time ISO string', 'string') }}\",\n    \"requestedEndTime\": \"={{ $fromAI('requestedEndTime', 'End time ISO string', 'string') }}\",\n    \"message\": \"={{ $fromAI('message', 'Additional message', 'string') || 'Solicitud de cita generada por IA' }}\",\n    \"type\": \"={{ $fromAI('type', 'Appointment type', 'string') || 'property_viewing' }}\",\n    \"meetingType\": \"={{ ($fromAI('meetingType', 'Meeting type', 'string') || 'in_person').replace(/^[=\\s]+/, '').toLowerCase() }}\",\n    \n    \"source\": \"n8n-ai-assistant\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-11500, 3080], "id": "bc672789-4f99-43e5-ab21-454f6af870b4", "name": "crear_solicitud_cita"}, {"parameters": {"method": "POST", "url": "https://capable-cod-213.convex.site/saveConversation", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"chatId\": \"{{ $('Edit Fields').item.json.message.chatId }}\",\n  \"userName\": \"Inmo Agent\", \n  \"messageId\": \"agent_{{ $('Edit Fields').item.json.message.messageId }}_response\",\n  \"messageType\": \"assistant\",\n  \"content\": {{ JSON.stringify($json.output) }},\n  \"timestamp\": \"{{ $now.toISOString() }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-11140, 2400], "id": "f8d98ad9-463f-40b9-85a3-2841ead668f0", "name": "Save Agent Response"}], "pinData": {"Webhook": [{"json": {"headers": {"host": "apps-n8n.yo7wfj.easypanel.host", "user-agent": "axios/1.7.9", "content-length": "967", "accept-encoding": "gzip, compress, deflate, br", "content-type": "application/json", "x-forwarded-for": "**********", "x-forwarded-host": "apps-n8n.yo7wfj.easypanel.host", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "75b82128a382", "x-real-ip": "**********"}, "params": {}, "query": {}, "body": {"event": "messages.upsert", "instance": "deEntradaIAagent", "data": {"key": {"remoteJid": "<EMAIL>", "fromMe": false, "id": "3EB01C61FF2FBA81CAA13D"}, "pushName": "<PERSON>", "status": "DELIVERY_ACK", "message": {"conversation": "si", "messageContextInfo": {"deviceListMetadata": {"senderKeyHash": "3pjoXdIIx+IvyQ==", "senderTimestamp": "**********", "senderAccountType": "E2EE", "receiverAccountType": "E2EE", "recipientKeyHash": "rZulyrIke/ZmPw==", "recipientTimestamp": "**********"}, "deviceListMetadataVersion": 2, "messageSecret": "abIGgL0pjFWK2rGavemjWfZIOc44MKgAKRn0nQArMI8="}}, "messageType": "conversation", "messageTimestamp": **********, "instanceId": "5d904c2f-3051-40b8-8032-fe76fd1ebdee", "source": "web"}, "destination": "https://apps-n8n.yo7wfj.easypanel.host/webhook/inmo-agent", "date_time": "2025-06-08T20:27:50.295Z", "sender": "<EMAIL>", "server_url": "https://apps-evolution-api.yo7wfj.easypanel.host", "apikey": "078FA839BEAD-48A5-821F-F99312842059"}, "webhookUrl": "https://apps-n8n.yo7wfj.easypanel.host/webhook/inmo-agent", "executionMode": "production"}}]}, "connections": {"Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "push mensaje": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "obtener todos mensajes", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "borrar to<PERSON> mensajes", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "obtener todos mensajes": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "borrar todos mensajes": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "push mensaje", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "descargar audio", "type": "main", "index": 0}], [{"node": "descargar imagen", "type": "main", "index": 0}], [{"node": "text content", "type": "main", "index": 0}]]}, "descargar audio": {"main": [[{"node": "convertir audio", "type": "main", "index": 0}]]}, "convertir audio": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "audio content", "type": "main", "index": 0}]]}, "descargar imagen": {"main": [[{"node": "convertir imagen", "type": "main", "index": 0}]]}, "convertir imagen": {"main": [[{"node": "describe imagen", "type": "main", "index": 0}]]}, "audio content": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "describe imagen": {"main": [[{"node": "image content", "type": "main", "index": 0}]]}, "image content": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "text content": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Merge": {"main": [[{"node": "Sort", "type": "main", "index": 0}]]}, "Sort": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "mensaje", "type": "main", "index": 0}]]}, "Json Parse": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "mensaje": {"main": [[{"node": "Get Chat History", "type": "main", "index": 0}]]}, "Auto-fixing Output Parser": {"ai_outputParser": [[{"node": "Verificador de Respuesta", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser", "type": "ai_outputParser", "index": 0}]]}, "Verificador de Respuesta": {"main": [[{"node": "Enviar Parte1", "type": "main", "index": 0}]]}, "Enviar Parte1": {"main": [[{"node": "If Parte 2", "type": "main", "index": 0}]]}, "If Parte 2": {"main": [[{"node": "Wait", "type": "main", "index": 0}], [{"node": "If Parte 3", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Enviar Parte2", "type": "main", "index": 0}]]}, "Enviar Parte2": {"main": [[{"node": "If Parte 3", "type": "main", "index": 0}]]}, "If Parte 3": {"main": [[{"node": "Wait2", "type": "main", "index": 0}], [{"node": "No Operation, do nothing1", "type": "main", "index": 0}]]}, "Wait2": {"main": [[{"node": "Enviar Parte3", "type": "main", "index": 0}]]}, "Enviar Parte3": {"main": [[{"node": "No Operation, do nothing1", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "Save Chat Convex", "type": "main", "index": 0}, {"node": "Verificador de Respuesta", "type": "main", "index": 0}, {"node": "Save Agent Response", "type": "main", "index": 0}]]}, "4o mini Verificador": {"ai_languageModel": [[{"node": "Auto-fixing Output Parser", "type": "ai_languageModel", "index": 0}, {"node": "Verificador de Respuesta", "type": "ai_languageModel", "index": 0}]]}, "buscar_informacion": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "buscar_propiedades": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "Save Chat Convex": {"main": [[]]}, "Get Chat History": {"main": [[{"node": "Prepare Context", "type": "main", "index": 0}]]}, "Prepare Context": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "obtener_contexto_propiedad": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "verificar_disponibilidad_propiedad": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "crear_solicitud_cita": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "b3540c19-a815-4c64-bac2-632617c19fdc", "meta": {"instanceId": "07b1f54ce63233c892bab2194482dcfcc49ccf3275504c189d498853ae39ba61"}, "id": "WPICWGh9T6aaDOm2", "tags": []}