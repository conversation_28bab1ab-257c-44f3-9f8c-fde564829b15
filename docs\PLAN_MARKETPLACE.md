# Plan de Desarrollo - Marketplace Inmobiliario

## Stack Técnico (aprovechando la base actual)
- ✅ Next.js 15 + TypeScript 
- ✅ Tailwind CSS + Shadcn/ui
- ✅ Clerk (auth)
- 🔄 Convex DB (modificar esquemas)
- ➕ Cloudinary (imágenes)
- ➕ Google Maps API
- ➕ React Hook Form + Zod
- ➕ Prisma (considerar migrar de Convex)

## Entidades Principales

### 1. Properties (Propiedades)
```typescript
{
  id: string
  title: string
  description: string
  price: number
  currency: 'USD' | 'EUR' | 'MXN'
  type: 'house' | 'apartment' | 'office' | 'land'
  status: 'for_sale' | 'for_rent' | 'sold' | 'rented'
  
  // Ubicación
  address: string
  city: string
  state: string
  country: string
  zipCode: string
  coordinates: { lat: number, lng: number }
  
  // Características
  bedrooms: number
  bathrooms: number
  area: number (m²)
  builtYear: number
  parking: number
  
  // Media
  images: string[] (URLs Cloudinary)
  virtualTour?: string
  floorPlan?: string
  
  // Relaciones
  ownerId: string (Usuario vendedor)
  agentId?: string (Agente inmobiliario)
  
  // Timestamps
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
}
```

### 2. Users (extender el actual)
```typescript
{
  // Campos existentes...
  role: 'buyer' | 'seller' | 'agent' | 'admin'
  profile: {
    phone?: string
    company?: string
    license?: string (para agentes)
    bio?: string
    avatar?: string
  }
  preferences: {
    currency: string
    notifications: boolean
    newsletter: boolean
  }
}
```

### 3. Favorites
```typescript
{
  userId: string
  propertyId: string
  createdAt: Date
}
```

### 4. Inquiries (Consultas)
```typescript
{
  id: string
  propertyId: string
  buyerId: string
  sellerId: string
  message: string
  phone?: string
  email: string
  status: 'pending' | 'responded' | 'closed'
  createdAt: Date
}
```

## Funcionalidades Prioritarias (Fase 1)

### MVP - 4 semanas
1. **Listado de propiedades** con filtros básicos
2. **Detalle de propiedad** con galería
3. **Sistema de favoritos**
4. **Formulario de contacto** vendedor
5. **Dashboard vendedor** (subir propiedades)
6. **Búsqueda por ubicación**

### Fase 2 - 4 semanas  
1. **Integración mapas** (Google Maps)
2. **Filtros avanzados** (precio, área, tipo)
3. **Upload múltiple imágenes**
4. **Sistema de mensajería**
5. **Perfil público agentes**

### Fase 3 - 4 semanas
1. **Tour virtual** integración
2. **Sistema de citas**
3. **Analytics dashboard**
4. **Notificaciones push**
5. **SEO avanzado**

## Estructura de Carpetas Propuesta

```
/app
  /(marketplace)
    /properties
      /[id]
        /page.tsx (detalle)
      /page.tsx (listado)
    /search
      /page.tsx
    /favorites
      /page.tsx
  /(dashboard)
    /seller
      /properties
        /new/page.tsx
        /[id]/edit/page.tsx
        /page.tsx (mis propiedades)
      /inquiries/page.tsx
    /agent
      /properties/page.tsx
      /clients/page.tsx
  /api
    /properties
    /upload
    /inquiries

/components
  /marketplace
    /property-card.tsx
    /property-gallery.tsx
    /property-filters.tsx
    /property-map.tsx
    /search-bar.tsx
  /forms
    /property-form.tsx
    /inquiry-form.tsx
  /dashboard
    /seller-sidebar.tsx
    /property-stats.tsx
```

## APIs Externas Necesarias

1. **Google Maps Platform**
   - Maps JavaScript API
   - Places API  
   - Geocoding API

2. **Cloudinary**
   - Upload de imágenes
   - Transformaciones automáticas
   - CDN global

3. **Opcional Fase 2**
   - SendGrid (emails)
   - Pusher (real-time)
   - Analytics 