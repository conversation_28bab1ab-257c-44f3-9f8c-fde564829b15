"use client";

import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { PropertyCard } from "@/components/marketplace/property-card";
import { AdvancedSearch } from "@/components/marketplace/advanced-search";
import { Button } from "@/components/ui/button";
import { Search, MapPin, Home, Building2, T<PERSON>dingUp, Users, Award, Clock, Heart } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";

export default function HomePage() {
  const router = useRouter();

  // Obtener propiedad PREMIUM para hero
  const premiumProperty = useQuery(api.featuredProperties.getCurrentPremiumProperty);

  // Obtener propiedades destacadas
  const featuredProperties = useQuery(api.featuredProperties.getFeaturedProperties, {
    limit: 6
  });

  // Obtener estadísticas
  const allProperties = useQuery(api.properties.getProperties, { limit: 100 });
  const forSaleCount = allProperties?.filter(p => p.status === "for_sale").length || 0;
  const forRentCount = allProperties?.filter(p => p.status === "for_rent").length || 0;

  const heroProperty = premiumProperty;

  return (
    <div className="w-full">
      {/* Hero Section Mejorado */}
      <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white">
        <div className="absolute inset-0 bg-white/5"></div>
        <div className="relative container mx-auto px-4 py-16 md:py-24">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
                Encuentra Tu Casa
                <span className="block text-blue-300">De Ensueño</span>
              </h1>
              <p className="text-lg md:text-xl lg:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed">
                Miles de propiedades esperándote. Compra, vende o arrienda de forma fácil y segura con los mejores profesionales del mercado.
              </p>
            </div>

            {/* Buscador Avanzado */}
            <div className="max-w-5xl mx-auto mb-12">
              <AdvancedSearch variant="home" />
            </div>

            {/* Estadísticas Prominentes */}
            <div className="grid grid-cols-3 gap-4 md:gap-8 max-w-4xl mx-auto">
              <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-4 md:p-6 border border-white/20">
                <div className="text-2xl md:text-4xl font-bold text-blue-300 mb-1 md:mb-2">5+</div>
                <div className="text-xs md:text-sm text-blue-100 font-medium">Años de Experiencia</div>
              </div>
              <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-4 md:p-6 border border-white/20">
                <div className="text-2xl md:text-4xl font-bold text-blue-300 mb-1 md:mb-2">{allProperties?.length || 0}</div>
                <div className="text-xs md:text-sm text-blue-100 font-medium">Propiedades Publicadas</div>
              </div>
              <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-4 md:p-6 border border-white/20">
                <div className="text-2xl md:text-4xl font-bold text-blue-300 mb-1 md:mb-2">500+</div>
                <div className="text-xs md:text-sm text-blue-100 font-medium">Clientes Felices</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Sección con Propiedad Premium VIP */}
      <section className="py-16 md:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                  Encuentra Tu Propio
                  <span className="block text-blue-600">Paraíso Inmobiliario</span>
                </h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  En Inmova creemos que cada persona merece encontrar su hogar perfecto. 
                  Con años de experiencia y miles de propiedades, te ayudamos a hacer realidad tus sueños inmobiliarios.
                </p>
                
                <div className="space-y-4 mb-8">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <Award className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Propiedades Verificadas</h3>
                      <p className="text-gray-600">Todas nuestras propiedades están verificadas y actualizadas</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <Users className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Agentes Expertos</h3>
                      <p className="text-gray-600">Profesionales capacitados para asesorarte</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                      <Clock className="h-6 w-6 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Atención 24/7</h3>
                      <p className="text-gray-600">Soporte completo durante todo el proceso</p>
                    </div>
                  </div>
                </div>

                <Link href="/properties">
                  <Button 
                    size="lg" 
                    className="px-8 py-4 text-lg bg-blue-600 hover:bg-blue-700 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    Explorar Ahora
                  </Button>
                </Link>
              </div>

              {/* PROPIEDAD PREMIUM VIP - Posición pagada */}
              <div className="relative">
                <div className="aspect-[4/5] bg-gradient-to-br from-blue-100 to-blue-200 rounded-3xl overflow-hidden relative shadow-2xl cursor-pointer group"
                     onClick={() => heroProperty ? router.push(`/properties/${heroProperty._id}`) : router.push('/properties')}>
                  
                  {/* Imagen de la propiedad real o imagen institucional */}
                  <div 
                    className="absolute inset-0 bg-cover bg-center group-hover:scale-105 transition-transform duration-700"
                    style={{
                      backgroundImage: heroProperty?.images?.[0] 
                        ? `linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%), url('${heroProperty.images[0]}')`
                        : `linear-gradient(135deg, rgba(59, 130, 246, 0.85) 0%, rgba(147, 51, 234, 0.85) 100%), url('https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`
                    }}
                  />
                  
                  {/* Badge VIP Premium o Institucional */}
                  <div className="absolute top-3 md:top-4 left-3 md:left-4">
                    {heroProperty ? (
                      <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1.5 md:px-4 md:py-2 rounded-full text-xs md:text-sm font-bold shadow-lg">
                        ⭐ PREMIUM VIP
                      </div>
                    ) : (
                      <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-3 py-1.5 md:px-4 md:py-2 rounded-full text-xs md:text-sm font-bold shadow-lg">
                        🏢 INMO
                      </div>
                    )}
                  </div>
                  
                  {/* Overlay con información */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent" />
                  
                  {/* Contenido superpuesto - Mejorado para móvil */}
                  <div className="absolute bottom-4 md:bottom-6 left-4 md:left-6 right-4 md:right-6">
                    <div className="space-y-3 md:space-y-4">
                      <div>
                        <h3 className="text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-white mb-2 md:mb-3 leading-tight">
                          {heroProperty ? heroProperty.title : "¡Destaca Tu Propiedad Aquí!"}
                        </h3>
                        <p className="text-white/90 text-xs md:text-sm lg:text-base leading-relaxed max-w-xs md:max-w-md mb-3 md:mb-4">
                          {heroProperty 
                            ? "Obtén máxima visibilidad • Posición premium • Miles de compradores esperando"
                            : "Obtén máxima visibilidad • Posición premium • Miles de compradores esperando"
                          }
                        </p>
                      </div>
                      
                      <Button 
                        onClick={() => heroProperty ? router.push(`/properties/${heroProperty._id}`) : router.push('/dashboard/properties/new')}
                        className="bg-white/90 hover:bg-white text-gray-900 border-0 rounded-xl font-bold px-4 py-2.5 md:px-6 md:py-3 text-xs md:text-sm lg:text-base shadow-2xl hover:shadow-3xl transition-all duration-300 backdrop-blur-sm w-full md:w-auto"
                      >
                        <span className="text-gray-900 font-semibold">
                          {heroProperty ? "Ver Propiedad" : "¡ESPACIO DISPONIBLE!"}
                        </span>
                      </Button>
                    </div>
                  </div>
                  
                  {/* Elementos decorativos flotantes - Reposicionados */}
                  <div className="absolute top-3 md:top-4 right-3 md:right-4">
                    <div className="w-10 h-10 md:w-12 md:h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:bg-red-500/80 transition-colors duration-300">
                      <Heart className="h-5 w-5 md:h-6 md:w-6 text-white" />
                    </div>
                  </div>

                  {/* Icon adicional cuando no hay propiedades premium - Reposicionado */}
                  {!heroProperty && (
                    <div className="absolute top-16 md:top-20 right-3 md:right-4">
                      <div className="w-8 h-8 md:w-10 md:h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center animate-pulse">
                        <TrendingUp className="h-4 w-4 md:h-5 md:w-5 text-white" />
                      </div>
                    </div>
                  )}
                  
                  {/* Tags informativos - Reposicionados para móvil */}
                  <div className="absolute top-16 md:top-20 left-3 md:left-4 space-y-1 md:space-y-2">
                    {/* Precio */}
                    <div className="bg-white/95 backdrop-blur-sm rounded-xl p-2 md:p-3 shadow-lg">
                      {heroProperty ? (
                        <>
                          <div className="text-lg md:text-2xl font-bold text-blue-600">
                            GTQ {heroProperty.price?.toLocaleString()}
                          </div>
                          <div className="text-xs text-gray-600">
                            {heroProperty.status === "for_sale" ? "Precio total" : "Por mes"}
                          </div>
                        </>
                      ) : (
                        <>
                          <div className="text-sm md:text-lg font-bold text-blue-600">
                            ¡PUBLICA AQUÍ!
                          </div>
                          <div className="text-xs text-gray-600">
                            Solo 75 créditos
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Propiedades Destacadas */}
      <section className="py-16 md:py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Propiedades Destacadas
            </h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Descubre nuestras mejores propiedades seleccionadas especialmente para ti
            </p>
          </div>

          {featuredProperties && featuredProperties.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 mb-12 md:mb-16">
              {featuredProperties.map((property) => (
                <PropertyCard
                  key={property._id}
                  property={property as any}
                  onFavorite={(id) => {
                    // Función para manejar favoritos (implementar después)
                  }}
                  isFavorite={false}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Home className="w-8 h-8 text-blue-600" />
              </div>
              <p className="text-gray-500 text-lg">Cargando propiedades destacadas...</p>
            </div>
          )}

          <div className="text-center">
            <Link href="/properties">
              <Button 
                size="lg" 
                variant="outline" 
                className="px-8 py-4 text-lg border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white rounded-xl font-semibold transition-all duration-300"
              >
                Ver Todas las Propiedades
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Categorías Mejoradas */}
      <section className="py-16 md:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Explora por Categorías
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Encuentra exactamente lo que buscas en nuestras categorías especializadas
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
            <Link href="/properties?type=house" className="group">
              <div className="bg-white rounded-2xl p-6 md:p-8 text-center shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-blue-200">
                <div className="w-12 h-12 md:w-16 md:h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4 md:mb-6 group-hover:bg-blue-600 transition-all duration-300">
                  <Home className="h-6 w-6 md:h-8 md:w-8 text-blue-600 group-hover:text-white transition-colors duration-300" />
                </div>
                <h3 className="font-bold text-lg md:text-xl text-gray-900 mb-2">Casas</h3>
                <p className="text-sm md:text-base text-gray-600">Encuentra tu hogar ideal</p>
              </div>
            </Link>

            <Link href="/properties?type=apartment" className="group">
              <div className="bg-white rounded-2xl p-6 md:p-8 text-center shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-blue-200">
                <div className="w-12 h-12 md:w-16 md:h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4 md:mb-6 group-hover:bg-blue-600 transition-all duration-300">
                  <Building2 className="h-6 w-6 md:h-8 md:w-8 text-blue-600 group-hover:text-white transition-colors duration-300" />
                </div>
                <h3 className="font-bold text-lg md:text-xl text-gray-900 mb-2">Apartamentos</h3>
                <p className="text-sm md:text-base text-gray-600">Vida urbana moderna</p>
              </div>
            </Link>

            <Link href="/properties?type=office" className="group">
              <div className="bg-white rounded-2xl p-6 md:p-8 text-center shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-blue-200">
                <div className="w-12 h-12 md:w-16 md:h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4 md:mb-6 group-hover:bg-purple-600 transition-all duration-300">
                  <TrendingUp className="h-6 w-6 md:h-8 md:w-8 text-purple-600 group-hover:text-white transition-colors duration-300" />
                </div>
                <h3 className="font-bold text-lg md:text-xl text-gray-900 mb-2">Oficinas</h3>
                <p className="text-sm md:text-base text-gray-600">Espacios de trabajo</p>
              </div>
            </Link>

            <Link href="/properties?type=land" className="group">
              <div className="bg-white rounded-2xl p-6 md:p-8 text-center shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-blue-200">
                <div className="w-12 h-12 md:w-16 md:h-16 bg-orange-100 rounded-2xl flex items-center justify-center mx-auto mb-4 md:mb-6 group-hover:bg-orange-600 transition-all duration-300">
                  <MapPin className="h-6 w-6 md:h-8 md:w-8 text-orange-600 group-hover:text-white transition-colors duration-300" />
                </div>
                <h3 className="font-bold text-lg md:text-xl text-gray-900 mb-2">Terrenos</h3>
                <p className="text-sm md:text-base text-gray-600">Inversión y desarrollo</p>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Final Mejorado */}
      <section className="py-16 md:py-20 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            ¿Listo para encontrar tu próxima propiedad?
          </h2>
          <p className="text-lg md:text-xl mb-8 md:mb-12 text-blue-100 max-w-2xl mx-auto leading-relaxed">
            Únete a miles de personas que ya encontraron su hogar ideal con nosotros
          </p>
          <div className="flex flex-col sm:flex-row gap-4 md:gap-6 justify-center">
            <Link href="/properties">
              <Button 
                size="lg" 
                variant="secondary" 
                className="w-full sm:w-auto px-8 md:px-10 py-3 md:py-4 text-base md:text-lg bg-white text-blue-600 hover:bg-gray-100 rounded-xl font-semibold transition-all duration-300"
              >
                Explorar Propiedades
              </Button>
            </Link>
            <Link href="/dashboard">
              <Button 
                size="lg" 
                variant="outline" 
                className="w-full sm:w-auto px-8 md:px-10 py-3 md:py-4 text-base md:text-lg text-blue-600 border-2 border-white hover:bg-white hover:text-blue-600 rounded-xl font-semibold transition-all duration-300"
              >
                Publicar Propiedad
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
} 