import { NextRequest, NextResponse } from "next/server";
import { stripe } from "@/lib/stripe";
import { api } from "@/convex/_generated/api";
import { ConvexHttpClient } from "convex/browser";

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get("stripe-signature");
    
    console.log("🔔 Webhook recibido de Stripe");
    
    if (!stripe) {
      console.error("❌ Stripe no está configurado");
      return NextResponse.json({ error: "Stripe not configured" }, { status: 500 });
    }

    if (!signature) {
      console.error("❌ No se encontró firma del webhook");
      return NextResponse.json({ error: "No signature" }, { status: 400 });
    }

    // Verificar firma del webhook
    let event;
    try {
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      );
    } catch (err) {
      console.error("❌ Error verificando firma del webhook:", err);
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
    }

    console.log("🔔 Stripe Webhook Event:", {
      type: event.type,
      id: event.id,
      created: event.created
    });

    // Solo procesar eventos que nos interesan
    const relevantEvents = [
      'checkout.session.completed',
      'customer.subscription.created',
      'customer.subscription.updated', 
      'customer.subscription.deleted',
      'invoice.payment_succeeded',
      'invoice.payment_failed'
    ];

    if (!relevantEvents.includes(event.type)) {
      console.log("⏭️ Evento no relevante, ignorando:", event.type);
      return NextResponse.json({ success: true, ignored: true });
    }

    // Procesar el evento en Convex
    await convex.mutation(api.stripe.processWebhook, {
      eventType: event.type,
      data: event.data.object,
    });

    console.log("✅ Webhook de Stripe procesado exitosamente");
    return NextResponse.json({ success: true });
    
  } catch (error) {
    console.error("❌ Error procesando webhook de Stripe:", error);
    return NextResponse.json(
      { error: "Error interno del servidor" }, 
      { status: 500 }
    );
  }
} 