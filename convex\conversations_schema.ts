import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export const conversationsSchema = defineTable({
  // Identificación
  chatId: v.string(), // "<EMAIL>"
  userId: v.optional(v.string()), // Si tienes users registrados
  userName: v.string(), // "<PERSON>"
  
  // Contenido del mensaje
  messageId: v.string(),
  messageType: v.union(v.literal("user"), v.literal("assistant")),
  content: v.string(),
  timestamp: v.string(),
  
  // Contexto
  sessionId: v.optional(v.string()), // Para agrupar conversaciones
  topic: v.optional(v.string()), // "búsqueda_apartamento", "seguimiento"
  intent: v.optional(v.string()), // "buscar_propiedad", "pedir_info"
  
  // Metadata para seguimiento
  propertyInterest: v.optional(v.array(v.string())), // IDs de propiedades de interés
  leadStatus: v.optional(v.string()), // "new", "interested", "qualified", "converted"
  followUpDate: v.optional(v.string()),
  tags: v.optional(v.array(v.string())),
  
  // Timestamps
  createdAt: v.string(),
  updatedAt: v.string(),
})
.index("by_chatId", ["chatId"])
.index("by_session", ["sessionId"])
.index("by_topic", ["topic"])
.index("by_leadStatus", ["leadStatus"])
.index("by_followUpDate", ["followUpDate"]); 