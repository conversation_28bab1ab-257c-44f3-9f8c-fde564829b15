"use client";

import React, { useState } from 'react';
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Phone, 
  Video, 
  User, 
  Home,
  MoreHorizontal,
  Check,
  X,
  Edit
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { formatTimeFor12h, formatDateLocal, formatTimeRange } from '@/lib/time-utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface AppointmentCardProps {
  appointment: any; // Tipo de cita desde Convex
  showProperty?: boolean;
  onUpdate: () => void;
}

export function AppointmentCard({ appointment, showProperty = false, onUpdate }: AppointmentCardProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  
  const updateAppointment = useMutation(api.appointments.updateAppointment);
  const cancelAppointment = useMutation(api.appointments.cancelAppointment);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800 border-green-200';
      case 'scheduled': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200';
      case 'no_show': return 'bg-orange-100 text-orange-800 border-orange-200';
      default: return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed': return 'Confirmada';
      case 'scheduled': return 'Programada';
      case 'completed': return 'Completada';
      case 'cancelled': return 'Cancelada';
      case 'no_show': return 'No se presentó';
      default: return status;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'property_viewing': return 'Visita de Propiedad';
      case 'consultation': return 'Consulta';
      case 'negotiation': return 'Negociación';
      case 'document_signing': return 'Firma de Documentos';
      case 'other': return 'Otro';
      default: return type;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'property_viewing': return <Home className="w-4 h-4" />;
      case 'consultation': return <User className="w-4 h-4" />;
      case 'negotiation': return <User className="w-4 h-4" />;
      case 'document_signing': return <User className="w-4 h-4" />;
      default: return <Calendar className="w-4 h-4" />;
    }
  };

  const getMeetingTypeIcon = (meetingType: string) => {
    switch (meetingType) {
      case 'video_call': return <Video className="w-4 h-4 text-blue-600" />;
      case 'phone_call': return <Phone className="w-4 h-4 text-green-600" />;
      case 'in_person': return <MapPin className="w-4 h-4 text-purple-600" />;
      default: return <Calendar className="w-4 h-4" />;
    }
  };

  const handleStatusChange = async (newStatus: string) => {
    setIsUpdating(true);
    try {
      await updateAppointment({
        appointmentId: appointment._id,
        status: newStatus as any,
      });
      onUpdate();
    } catch (error) {
      console.error('Error updating appointment:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancel = async () => {
    setIsUpdating(true);
    try {
      await cancelAppointment({
        appointmentId: appointment._id,
        cancelledBy: appointment.hostId,
        cancellationReason: "Cancelada por el anfitrión",
      });
      onUpdate();
    } catch (error) {
      console.error('Error cancelling appointment:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const isPastAppointment = new Date(appointment.startTime) < new Date();
  const canMarkCompleted = isPastAppointment && appointment.status !== 'completed' && appointment.status !== 'cancelled';
  const canConfirm = appointment.status === 'scheduled';
  const canCancel = appointment.status !== 'cancelled' && appointment.status !== 'completed';

  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            {/* Header */}
            <div className="flex items-start gap-3 mb-3">
              <div className="flex items-center gap-2 text-gray-600">
                {getTypeIcon(appointment.type)}
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 mb-1">
                  {appointment.title}
                </h3>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <span className="flex items-center gap-1">
                    <User className="w-3 h-3" />
                    {appointment.guestName}
                  </span>
                  <span className="flex items-center gap-1">
                    {getMeetingTypeIcon(appointment.meetingType)}
                    {appointment.meetingType === 'video_call' && 'Videollamada'}
                    {appointment.meetingType === 'phone_call' && 'Llamada'}
                    {appointment.meetingType === 'in_person' && 'Presencial'}
                  </span>
                </div>
              </div>
              <Badge className={getStatusColor(appointment.status)}>
                {getStatusText(appointment.status)}
              </Badge>
            </div>

            {/* Fecha y hora */}
            <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
              <span className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                {formatDateLocal(appointment.startTime)}
              </span>
              <span className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                {formatTimeRange(appointment.startTime, appointment.endTime)}
              </span>
            </div>

            {/* Ubicación */}
            {appointment.location && (
              <div className="flex items-center gap-1 text-sm text-gray-600 mb-3">
                <MapPin className="w-3 h-3" />
                {appointment.location}
              </div>
            )}

            {/* Propiedad relacionada */}
            {showProperty && appointment.property && (
              <div className="flex items-center gap-1 text-sm text-blue-600 mb-3">
                <Home className="w-3 h-3" />
                {appointment.property.title}
              </div>
            )}

            {/* Descripción */}
            {appointment.description && (
              <p className="text-sm text-gray-600 mb-3">
                {appointment.description}
              </p>
            )}

            {/* Notas */}
            {appointment.notes && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2 mb-3">
                <p className="text-sm text-yellow-800">
                  <strong>Notas:</strong> {appointment.notes}
                </p>
              </div>
            )}

            {/* Contacto */}
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <span>{appointment.guestEmail}</span>
              {appointment.guestPhone && (
                <span>{appointment.guestPhone}</span>
              )}
            </div>
          </div>

          {/* Acciones */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm"
                disabled={isUpdating}
                className="h-8 w-8 p-0"
              >
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {canConfirm && (
                <DropdownMenuItem 
                  onClick={() => handleStatusChange('confirmed')}
                  className="text-green-600"
                >
                  <Check className="w-4 h-4 mr-2" />
                  Confirmar
                </DropdownMenuItem>
              )}
              
              {canMarkCompleted && (
                <DropdownMenuItem 
                  onClick={() => handleStatusChange('completed')}
                  className="text-blue-600"
                >
                  <Check className="w-4 h-4 mr-2" />
                  Marcar como Completada
                </DropdownMenuItem>
              )}

              <DropdownMenuItem className="text-gray-600">
                <Edit className="w-4 h-4 mr-2" />
                Editar
              </DropdownMenuItem>

              {canCancel && (
                <DropdownMenuItem 
                  onClick={handleCancel}
                  className="text-red-600"
                >
                  <X className="w-4 h-4 mr-2" />
                  Cancelar
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* URL de videollamada */}
        {appointment.meetingUrl && appointment.meetingType === 'video_call' && (
          <div className="mt-3 pt-3 border-t border-gray-100">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => window.open(appointment.meetingUrl, '_blank')}
              className="text-blue-600 border-blue-200 hover:bg-blue-50"
            >
              <Video className="w-4 h-4 mr-2" />
              Unirse a la Videollamada
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 