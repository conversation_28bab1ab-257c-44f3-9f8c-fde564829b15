import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { internal, api } from "./_generated/api";

const http = httpRouter();

// Endpoint para obtener todas las propiedades (para n8n)
http.route({
  path: "/api/properties",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get("limit") || "50");
    const offset = parseInt(url.searchParams.get("offset") || "0");
    
    const properties = await ctx.runQuery(api.properties.getProperties, {
      limit,
      offset
    });
    
    return new Response(JSON.stringify({
      success: true,
      data: properties,
      pagination: {
        limit,
        offset,
        total: properties.length
      }
    }), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE",
        "Access-Control-Allow-Headers": "Content-Type, Authorization"
      }
    });
  })
});

// Endpoint para obtener una propiedad específica
http.route({
  path: "/api/properties/{id}",
  method: "GET", 
  handler: httpAction(async (ctx, request) => {
    const url = new URL(request.url);
    const id = url.pathname.split("/").pop();
    
    if (!id) {
      return new Response(JSON.stringify({
        success: false,
        error: "ID de propiedad requerido"
      }), { status: 400 });
    }
    
    try {
      // Validar que el ID no sea "undefined" o inválido
      if (!id || id === "undefined") {
        return new Response(JSON.stringify({
          success: false,
          error: "ID de propiedad no válido"
        }), { status: 400 });
      }

      const property = await ctx.runQuery(api.properties.getPropertyById, {
        id: id as any
      });
      
      if (!property) {
        return new Response(JSON.stringify({
          success: false,
          error: "Propiedad no encontrada"
        }), { status: 404 });
      }
      
      return new Response(JSON.stringify({
        success: true,
        data: property
      }), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*"
        }
      });
    } catch (error) {
      return new Response(JSON.stringify({
        success: false,
        error: "Error interno del servidor"
      }), { status: 500 });
    }
  })
});

// Endpoint para crear propiedades (para n8n)
http.route({
  path: "/api/properties",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    try {
      const body = await request.json();
      
      // Validación básica
      if (!body.title || !body.price || !body.address) {
        return new Response(JSON.stringify({
          success: false,
          error: "Campos requeridos: title, price, address"
        }), { status: 400 });
      }
      
      const newProperty = await ctx.runMutation(api.properties.createProperty, {
        ...body,
        status: "draft" // Por defecto como borrador
      });
      
      return new Response(JSON.stringify({
        success: true,
        data: newProperty,
        message: "Propiedad creada exitosamente"
      }), {
        status: 201,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*"
        }
      });
    } catch (error) {
      return new Response(JSON.stringify({
        success: false,
        error: "Error procesando la solicitud"
      }), { status: 500 });
    }
  })
});

// TEMPORALMENTE DESHABILITADO - No necesario para MVP
// import { paymentWebhook } from "./subscriptions";
// http.route({
//     path: "/payments/webhook", 
//     method: "POST",
//     handler: paymentWebhook,
// });

// Endpoint para el agente de WhatsApp - Búsqueda de propiedades
export const searchPropertiesForAgent = httpAction(async (ctx, request) => {
  try {
    const requestBody = await request.json();
    
    // Aceptar tanto searchQuery como searchCriteria para compatibilidad
    const rawSearchCriteria = requestBody.searchQuery ? 
      { searchText: requestBody.searchQuery } : 
      (requestBody.searchCriteria || {});
    
    // Validar que hay criterios de búsqueda
    if (!rawSearchCriteria && !requestBody.searchQuery) {
      return new Response(JSON.stringify({
        success: false,
        error: "Criterios de búsqueda requeridos"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    console.log("Criterios RAW desde N8N:", rawSearchCriteria);

    // Búsqueda simple por texto - deja que la base de datos haga el trabajo
    let actualSearchCriteria = rawSearchCriteria;
    
    if (rawSearchCriteria.searchQuery) {
      // Búsqueda directa por texto en todos los campos
      actualSearchCriteria = {
        searchText: rawSearchCriteria.searchQuery
      };
      console.log("Búsqueda por texto:", actualSearchCriteria);
    } else if (rawSearchCriteria.searchCriteria) {
      // Fallback si viene el formato anterior
      try {
        if (typeof rawSearchCriteria.searchCriteria === 'string') {
          actualSearchCriteria = JSON.parse(rawSearchCriteria.searchCriteria);
        } else {
          actualSearchCriteria = rawSearchCriteria.searchCriteria;
        }
      } catch (error) {
        actualSearchCriteria = { searchText: rawSearchCriteria.searchCriteria };
      }
    }

    // Función para limpiar valores que pueden venir de N8N
    const cleanSearchValue = (value: any): any => {
      // Si es null o undefined, retorna undefined
      if (value === null || value === undefined) {
        return undefined;
      }
      
      // Si es string
      if (typeof value === 'string') {
        const trimmed = value.trim();
        // Si está vacío, es "null", "undefined", o contiene solo espacios
        if (trimmed === '' || trimmed === 'null' || trimmed === 'undefined') {
          return undefined;
        }
        return trimmed;
      }
      
      // Si es número, verificar que sea válido
      if (typeof value === 'number') {
        return isNaN(value) ? undefined : value;
      }
      
      return value;
    };

    // Limpiar y construir criterios válidos
    const searchCriteria: any = {};
    
    Object.entries(actualSearchCriteria).forEach(([key, value]) => {
      const cleanedValue = cleanSearchValue(value);
      if (cleanedValue !== undefined) {
        searchCriteria[key] = cleanedValue;
      }
    });

    console.log("Criterios después de limpieza:", searchCriteria);

    // Convertir campos numéricos a número
    const numericFields = ['minPrice', 'maxPrice', 'bedrooms', 'bathrooms', 'minArea', 'maxArea', 'parking', 'builtYear'];
    numericFields.forEach(field => {
      if (field in searchCriteria && typeof searchCriteria[field] === 'string') {
        const numValue = parseFloat(searchCriteria[field] as string);
        // Si no es un número válido, eliminar el campo
        if (isNaN(numValue)) {
          delete searchCriteria[field];
        } else {
          searchCriteria[field] = numValue;
        }
      }
    });
    
    // Procesar amenidades si existen
    if ('amenities' in searchCriteria && typeof searchCriteria.amenities === 'string') {
      searchCriteria.amenities = searchCriteria.amenities.split(',').map((a: string) => a.trim());
    }
    
    console.log("Criterios de búsqueda procesados:", searchCriteria);

    // Usar AI para extraer criterios si hay texto de búsqueda
    if (searchCriteria.searchText) {
      try {
        console.log("🤖 Usando AI para extraer criterios de búsqueda");
        const aiResult = await ctx.runAction(internal.openaiExtraction.extractSearchCriteriaWithAI, {
          searchText: searchCriteria.searchText
        });
        
        if (aiResult.success) {
          console.log("✅ Criterios extraídos con AI:", aiResult.criteria);
          // Combinar criterios de AI con los existentes
          Object.assign(searchCriteria, aiResult.criteria);
        } else {
          console.log("❌ AI falló, usando búsqueda básica:", aiResult.error);
        }
      } catch (error) {
        console.log("⚠️ Error con AI, usando búsqueda básica:", error);
      }
    }

    // Llamar a la función de búsqueda con AI
    const results = await ctx.runAction(internal.properties.searchForAgentWithAI, { searchCriteria });

    return new Response(JSON.stringify({
      success: true,
      properties: results.properties,
      total: results.total,
      searchCriteria: searchCriteria
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error en búsqueda de propiedades:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Endpoint para obtener detalles completos de una propiedad
export const getPropertyDetailsForAgent = httpAction(async (ctx, request) => {
  try {
    const { propertyId } = await request.json();
    
    if (!propertyId) {
      return new Response(JSON.stringify({
        success: false,
        error: "ID de propiedad requerido"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Llamar a la función interna para obtener detalles
    const result = await ctx.runQuery(internal.properties.getDetailsForAgent, { propertyId });

    if (!result.property) {
      return new Response(JSON.stringify({
        success: false,
        error: "Propiedad no encontrada"
      }), {
        status: 404,
        headers: { "Content-Type": "application/json" }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      property: result.property
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error obteniendo detalles de propiedad:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Función para validar API Key
function validateApiKey(request: Request): boolean {
  const apiKey = request.headers.get("x-api-key");
  const validApiKey = process.env.N8N_API_KEY;
  
  if (!validApiKey) {
    console.error("N8N_API_KEY no está configurada en las variables de entorno");
    return false;
  }
  
  return apiKey === validApiKey;
}

// Endpoint para guardar conversaciones de WhatsApp
export const saveConversation = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const { chatId, userName, messageId, messageType, content, timestamp } = await request.json();
    
    if (!chatId || !messageId || !messageType || !content) {
      return new Response(JSON.stringify({
        success: false,
        error: "Campos requeridos: chatId, messageId, messageType, content"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Guardar conversación en Convex
    const conversationId = await ctx.runMutation(api.conversations.saveMessage, {
      chatId,
      userName: userName || "Usuario",
      messageId,
      messageType,
      content,
      timestamp: timestamp || new Date().toISOString()
    });

    return new Response(JSON.stringify({
      success: true,
      conversationId,
      message: "Conversación guardada exitosamente"
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error guardando conversación:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Endpoint para generar reportes semanales
export const generateWeeklyReport = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const { startDate, endDate } = await request.json();
    
    if (!startDate || !endDate) {
      return new Response(JSON.stringify({
        success: false,
        error: "Campos requeridos: startDate, endDate"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Generar reporte
    const report = await ctx.runQuery(api.conversations.generateWeeklyReport, {
      startDate,
      endDate
    });

    return new Response(JSON.stringify({
      success: true,
      report
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error generando reporte:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Endpoint para obtener historial de conversaciones para el agente
export const getChatHistoryForAgent = httpAction(async (ctx, request) => {
  try {
    const { chatId, limit } = await request.json();
    
    if (!chatId) {
      return new Response(JSON.stringify({
        success: false,
        error: "ChatId requerido"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Obtener conversaciones del usuario
    const conversations = await ctx.runQuery(api.conversations.getChatHistory, {
      chatId,
      limit: limit || 10
    });

    // Obtener perfil del lead si existe
    const leadProfile = await ctx.runQuery(api.conversations.getLeadProfile, {
      chatId
    });

    return new Response(JSON.stringify({
      success: true,
      conversations,
      leadProfile,
      chatId
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error obteniendo historial:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Registrar endpoints para el agente de WhatsApp en el router
http.route({
  path: "/searchPropertiesForAgent",
  method: "POST",
  handler: searchPropertiesForAgent
});

http.route({
  path: "/getPropertyDetailsForAgent", 
  method: "POST",
  handler: getPropertyDetailsForAgent
});

// Endpoint para asignar propiedad a agente (admin)
export const assignPropertyToAgentAdmin = httpAction(async (ctx, request) => {
  try {
    const { propertyId, agentId } = await request.json();
    
    if (!propertyId || !agentId) {
      return new Response(JSON.stringify({
        success: false,
        error: "Property ID y Agent ID son requeridos"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Llamar a la función interna de asignación
    const result = await ctx.runAction(internal.properties.adminAssignPropertyToAgent, {
      propertyId,
      agentId
    });

    return new Response(JSON.stringify({
      success: true,
      message: result.message,
      propertyId: result.propertyId,
      agentId: result.agentId
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error asignando propiedad:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

http.route({
  path: "/assignPropertyToAgent", 
  method: "POST",
  handler: assignPropertyToAgentAdmin
});

// Registrar nuevos endpoints
http.route({
  path: "/saveConversation",
  method: "POST",
  handler: saveConversation
});

http.route({
  path: "/generateWeeklyReport",
  method: "POST", 
  handler: generateWeeklyReport
});

// Registrar nuevo endpoint
http.route({
  path: "/getChatHistoryForAgent",
  method: "POST",
  handler: getChatHistoryForAgent
});



// Convex expects the router to be the default export of `convex/http.js`.
export default http;