// Script temporal para hacer administrador a un usuario
// Usar desde la consola del dashboard de Convex

// Cambiar por tu email:
const ADMIN_EMAIL = "<EMAIL>";

// Ejecutar esta función en la consola de Convex:
/*
await api.users.makeAdmin({ email: "<EMAIL>" })
*/

console.log(`
🔧 Para hacer administrador a un usuario:

1. Ve a: https://dashboard.convex.dev
2. Abre tu proyecto InmoApp
3. Ve a la pestaña "Functions"
4. En la consola, ejecuta:

   await api.users.makeAdmin({ email: "${ADMIN_EMAIL}" })

5. Cam<PERSON> "${ADMIN_EMAIL}" por tu email real

✅ Después podrás acceder al panel admin en /dashboard/admin
`); 