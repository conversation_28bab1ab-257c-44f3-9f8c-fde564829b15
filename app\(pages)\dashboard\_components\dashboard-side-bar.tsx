"use client"

import clsx from 'clsx'
import {
  Banknote,
  Folder,
  HomeIcon,
  Settings,
  LucideIcon,
  Building2,
  Mail,
  Shield,
  Calendar
} from "lucide-react"
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Badge } from "@/components/ui/badge"

interface NavItem {
  label: string;
  href: string;
  icon: LucideIcon;
  showBadge?: boolean;
  adminOnly?: boolean;
}

const navItems: NavItem[] = [
  {
    label: "Panel Principal",
    href: "/dashboard",
    icon: HomeIcon
  },
  {
    label: "Mis Propiedades",
    href: "/dashboard/properties",
    icon: Building2
  },
  {
    label: "Mensajes",
    href: "/dashboard/messages",
    icon: Mail,
    showBadge: true
  },
  {
    label: "Agenda",
    href: "/dashboard/agenda",
    icon: Calendar
  },
  {
    label: "Suscripción",
    href: "/dashboard/finance",
    icon: Banknote
  },
  {
    label: "Administración",
    href: "/dashboard/admin",
    icon: Shield,
    adminOnly: true
  },
  {
    label: "Configuración",
    href: "/dashboard/settings",
    icon: Settings
  }
]

export default function DashboardSideBar() {
  const pathname = usePathname();
  const messageStats = useQuery(api.messages.getMessageStats, {});
  
  // Obtener datos del usuario actual para verificar rol
  const currentUser = useQuery(api.users.getCurrentUser);
  
  // Filtrar items del menú basado en permisos
  const filteredNavItems = navItems.filter(item => {
    if (item.adminOnly) {
      return currentUser?.role === 'admin';
    }
    return true;
  });
  
  return (
    <div className="min-[1024px]:block hidden w-64 border-r h-full bg-background">
      <div className="flex h-full flex-col">
        <div className="flex h-[3.45rem] items-center border-b px-4">
          <Link prefetch={true} className="flex items-center gap-2 font-semibold hover:cursor-pointer" href="/">
            <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center mr-2">
              <HomeIcon className="h-4 w-4 text-white" />
            </div>
                          <span>Inmova</span>
          </Link>
        </div>

        <nav className="flex-1 space-y-1 p-4">
          {filteredNavItems.map((item) => (
            <Link
              key={item.href}
              prefetch={true}
              href={item.href}
              className={clsx(
                "flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-colors relative",
                pathname === item.href
                  ? "bg-blue-50 text-blue-600 hover:bg-blue-100"
                  : "text-muted-foreground hover:bg-blue-50 hover:text-blue-600"
              )}
            >
              <item.icon className="h-4 w-4" />
              {item.label}
              {item.adminOnly && currentUser?.role === 'admin' && (
                <Badge 
                  variant="secondary" 
                  className="ml-auto h-5 px-2 text-xs bg-orange-100 text-orange-800"
                >
                  Admin
                </Badge>
              )}
              {item.showBadge && messageStats && messageStats.unreadCount > 0 && (
                <Badge 
                  variant="destructive" 
                  className="ml-auto h-5 w-5 flex items-center justify-center p-0 text-xs bg-red-500 hover:bg-red-600"
                >
                  {messageStats.unreadCount > 99 ? '99+' : messageStats.unreadCount}
                </Badge>
              )}
            </Link>
          ))}
        </nav>
      </div>
    </div>
  )
}
