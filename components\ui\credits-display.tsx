"use client";

import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { CreditCard, AlertTriangle } from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface CreditsDisplayProps {
  variant?: "header" | "inline" | "card" | "compact";
  showManageButton?: boolean;
  className?: string;
}

export function CreditsDisplay({ 
  variant = "header", 
  showManageButton = true,
  className 
}: CreditsDisplayProps) {
  const subscription = useQuery(api.subscriptions.getUserSubscription);

  if (!subscription) return null;

  const availableCredits = subscription.credits - subscription.creditsUsed;
  const isLow = availableCredits < 25;
  const isCritical = availableCredits < 10;

  // Variante compacta horizontal (nueva)
  if (variant === "compact") {
    return (
      <div className={cn("flex items-center gap-3", className)}>
        <div className={cn(
          "flex items-center gap-2 px-3 py-2 rounded-lg border",
          isCritical 
            ? "bg-red-50 border-red-200" 
            : isLow 
            ? "bg-yellow-50 border-yellow-200" 
            : "bg-blue-50 border-blue-200"
        )}>
          <CreditCard className={cn(
            "h-4 w-4",
            isCritical ? "text-red-600" : isLow ? "text-yellow-600" : "text-blue-600"
          )} />
          <div className="flex items-center gap-1">
            <span className={cn(
              "font-bold text-lg",
              isCritical ? "text-red-600" : isLow ? "text-yellow-600" : "text-blue-600"
            )}>
              {availableCredits}
            </span>
            <span className="text-sm text-muted-foreground">
              créditos de {subscription.credits}
            </span>
          </div>
        </div>
        {showManageButton && (
          <Link href="/dashboard/finance">
            <Button variant="outline" size="sm">
              {isLow ? "Obtener Más" : "Gestionar"}
            </Button>
          </Link>
        )}
      </div>
    );
  }

  // Variante para header (mejorada - más compacta)
  if (variant === "header") {
    return (
      <div className={cn("flex items-center gap-3", className)}>
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Créditos:</span>
          <span className={cn(
            "font-bold text-xl",
            isCritical ? "text-red-600" : isLow ? "text-yellow-600" : "text-blue-600"
          )}>
            {availableCredits}
          </span>
          <span className="text-sm text-muted-foreground">
            de {subscription.credits}
          </span>
        </div>
        {showManageButton && (
          <Link href="/dashboard/finance">
            <Button variant="outline" size="sm">
              <CreditCard className="h-4 w-4 mr-2" />
              {isLow ? "Obtener Más" : "Gestionar"}
            </Button>
          </Link>
        )}
      </div>
    );
  }

  // Variante inline (para usar en líneas de texto)
  if (variant === "inline") {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className={cn(
          "flex items-center gap-1 px-2 py-1 rounded-md text-sm font-medium",
          isCritical 
            ? "bg-red-100 text-red-800" 
            : isLow 
            ? "bg-yellow-100 text-yellow-800" 
            : "bg-blue-100 text-blue-800"
        )}>
          <CreditCard className="h-3 w-3" />
          {availableCredits} créditos
        </div>
        {isLow && showManageButton && (
          <Link href="/dashboard/finance">
            <Button size="sm" variant="outline">
              Obtener Más
            </Button>
          </Link>
        )}
      </div>
    );
  }

  // Variante card (para dashboards y resúmenes)
  if (variant === "card") {
    return (
      <div className={cn(
        "p-4 rounded-lg border",
        isCritical 
          ? "bg-red-50 border-red-200" 
          : isLow 
          ? "bg-yellow-50 border-yellow-200" 
          : "bg-blue-50 border-blue-200",
        className
      )}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={cn(
              "w-10 h-10 rounded-full flex items-center justify-center",
              isCritical 
                ? "bg-red-100" 
                : isLow 
                ? "bg-yellow-100" 
                : "bg-blue-100"
            )}>
              <CreditCard className={cn(
                "h-5 w-5",
                isCritical 
                  ? "text-red-600" 
                  : isLow 
                  ? "text-yellow-600" 
                  : "text-blue-600"
              )} />
            </div>
            <div>
              <p className="font-medium">Créditos Disponibles</p>
              <p className="text-sm text-muted-foreground">
                {availableCredits} de {subscription.credits} disponibles
              </p>
            </div>
          </div>
          
          <div className="text-right">
            <p className={cn(
              "text-3xl font-bold",
              isCritical 
                ? "text-red-600" 
                : isLow 
                ? "text-yellow-600" 
                : "text-blue-600"
            )}>
              {availableCredits}
            </p>
            {showManageButton && (
              <Link href="/dashboard/finance">
                <Button size="sm" className="mt-2">
                  {isLow ? "Obtener Más" : "Gestionar"}
                </Button>
              </Link>
            )}
          </div>
        </div>

        {/* Barra de progreso */}
        <div className="mt-3">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={cn(
                "h-2 rounded-full transition-all duration-300",
                isCritical 
                  ? "bg-red-500" 
                  : isLow 
                  ? "bg-yellow-500" 
                  : "bg-blue-500"
              )}
              style={{ 
                width: `${Math.max((availableCredits / subscription.credits) * 100, 5)}%` 
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  return null;
} 