import { internalAction } from "./_generated/server";
import { internal } from "./_generated/api";
import OpenAI from 'openai';

// Inicializar OpenAI
let openai: OpenAI | null = null;

try {
  if (process.env.OPENAI_API_KEY) {
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }
} catch (error) {
  console.warn("OpenAI no pudo inicializarse:", error);
}

// Función simple para generar todos los embeddings
export const generateAllEmbeddings = internalAction({
  handler: async (ctx): Promise<{ success: boolean; message: string; processedCount: number }> => {
    if (!openai) {
      return { success: false, message: "OpenAI no disponible", processedCount: 0 };
    }

    console.log("🚀 Iniciando generación masiva de embeddings...");

    // Obtener todas las propiedades activas
    const properties = await ctx.runQuery(internal.properties.getAllProperties);
    
    let processedCount = 0;
    
    for (const property of properties) {
      try {
        console.log(`🔄 Procesando: ${property.title}`);
        
        // Crear texto descriptivo completo
        const propertyText = [
          property.title,
          property.description,
          `Tipo: ${property.type}`,
          `Ciudad: ${property.city}`, 
          `Estado: ${property.status}`,
          `Precio: ${property.price} ${property.currency}`,
          `${property.bedrooms || 0} habitaciones`,
          `${property.bathrooms || 0} baños`,
          `${property.area} m²`,
          property.amenities?.join(', ') || '',
          property.address || ''
        ].filter(Boolean).join('. ');

        // Generar embedding
        const response = await openai.embeddings.create({
          model: "text-embedding-3-small",
          input: propertyText,
        });

        const embedding = response.data[0].embedding;

        // Guardar embedding en la propiedad
        await ctx.runMutation(internal.properties.updatePropertyEmbedding, {
          id: property._id,
          embedding: embedding,
          embeddingText: propertyText
        });

        processedCount++;
        console.log(`✅ Embedding generado para: ${property.title}`);
        
        // Pausa para evitar rate limits
        await new Promise(resolve => setTimeout(resolve, 200));
        
      } catch (error) {
        console.error(`❌ Error procesando ${property.title}:`, error);
      }
    }

    return {
      success: true,
      message: `Embeddings generados exitosamente para ${processedCount}/${properties.length} propiedades`,
      processedCount
    };
  }
}); 