# Marketplace Inmobiliario

Un marketplace moderno para compra, venta y alquiler de propiedades inmobiliarias construido con Next.js 15, Convex DB y TypeScript.

## 🚀 Características Implementadas

### ✅ Base Técnica
- **Next.js 15** con App Router y TypeScript
- **Convex DB** para base de datos en tiempo real
- **Clerk** para autenticación de usuarios
- **Tailwind CSS + Shadcn/ui** para interfaz moderna
- **Cloudinary** para gestión de imágenes
- **Google Maps API** para mapas y geolocalización

### ✅ Funcionalidades Core
- **Listado de propiedades** con filtros avanzados
- **Búsqueda en tiempo real** por ubicación, título, descripción
- **Tarjetas de propiedades** con información detallada
- **Sistema de favoritos** (preparado)
- **Filtros por tipo, precio, ubicación**
- **Vista grid/lista** intercambiable
- **Responsive design** para móviles

### 🔄 En Desarrollo
- Página de detalle de propiedad
- Dashboard para vendedores
- Sistema de consultas/mensajería
- Upload de imágenes múltiples
- Integración completa con mapas
- Sistema de favoritos funcional

## 📁 Estructura del Proyecto

```
/app
  /(pages)
    /properties          # Marketplace público
      /page.tsx         # Listado de propiedades
    /dashboard          # Panel de vendedores
/components
  /marketplace
    /property-card.tsx  # Tarjeta de propiedad
/convex
  /schema.ts           # Esquemas de BD
  /properties.ts       # Funciones de propiedades
/types
  /marketplace.ts      # Tipos TypeScript
```

## 🛠️ Configuración Rápida

### 1. Instalar dependencias
```bash
bun install
```

### 2. Configurar variables de entorno
```bash
# Copiar archivo de ejemplo
cp .env.example .env.local

# Configurar variables necesarias:
NEXT_PUBLIC_CONVEX_URL=
CONVEX_DEPLOYMENT=
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=
```

### 3. Configurar Convex DB
```bash
# Instalar Convex CLI
npm install -g convex

# Inicializar proyecto
npx convex dev
```

### 4. Ejecutar en desarrollo
```bash
bun run dev
```

## 🗄️ Esquema de Base de Datos

### Properties (Propiedades)
```typescript
{
  title: string
  description: string
  price: number
  currency: string
  type: 'house' | 'apartment' | 'office' | 'land' | 'commercial'
  status: 'for_sale' | 'for_rent' | 'sold' | 'rented' | 'draft'
  
  // Ubicación
  address: string
  city: string
  state: string
  country: string
  coordinates?: { lat: number, lng: number }
  
  // Características
  bedrooms?: number
  bathrooms?: number
  area: number
  builtYear?: number
  parking?: number
  
  // Media
  images: string[]
  virtualTour?: string
  
  // Relaciones
  ownerId: string
  agentId?: string
}
```

### Users (Usuarios extendidos)
```typescript
{
  // Campos base de Clerk...
  role?: 'buyer' | 'seller' | 'agent' | 'admin'
  phone?: string
  company?: string
  license?: string
  bio?: string
}
```

## 🎯 Próximos Pasos

### Fase 1 (Semana 1-2)
1. **Página de detalle** de propiedad con galería
2. **Dashboard vendedor** básico
3. **Formulario crear propiedad**
4. **Sistema de favoritos** funcional

### Fase 2 (Semana 3-4)
1. **Integración Google Maps** completa
2. **Upload múltiple imágenes** con Cloudinary
3. **Sistema de consultas** vendedor-comprador
4. **Filtros avanzados** (área, habitaciones, etc.)

### Fase 3 (Semana 5-6)
1. **Perfiles públicos** de agentes
2. **Sistema de citas** para visitas
3. **Notificaciones** en tiempo real
4. **Analytics dashboard**

## 🔧 APIs Externas Necesarias

### Google Maps Platform
- **Maps JavaScript API** - Mapas interactivos
- **Places API** - Autocompletado de direcciones
- **Geocoding API** - Conversión dirección ↔ coordenadas

### Cloudinary
- **Upload API** - Subida de imágenes
- **Transformation API** - Redimensionado automático
- **CDN** - Entrega optimizada

## 📱 Funcionalidades Móviles

- ✅ **Responsive design** completo
- ✅ **Touch gestures** para galerías
- ✅ **Filtros móviles** optimizados
- 🔄 **Geolocalización** del usuario
- 🔄 **Llamadas directas** desde la app

## 🚀 Despliegue

### Vercel (Recomendado)
```bash
# Conectar con Vercel
vercel

# Configurar variables de entorno en dashboard
# Desplegar
vercel --prod
```

### Variables de Entorno Producción
- Todas las de desarrollo
- `NEXT_PUBLIC_BASE_URL` con dominio real
- Claves de producción de APIs externas

## 📊 Métricas y Analytics

- **Propiedades más vistas**
- **Búsquedas populares**
- **Conversión consultas → visitas**
- **Tiempo en página de detalle**
- **Filtros más utilizados**

## 🤝 Contribuir

1. Fork del repositorio
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -m 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📄 Licencia

MIT License - ver archivo [LICENSE](LICENSE) para detalles. 