import Stripe from 'stripe';

// Obtener clave secreta de Stripe
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

if (!stripeSecretKey) {
  console.warn("⚠️  STRIPE_SECRET_KEY no está configurado en .env.local");
}

// Crear cliente de Stripe
export const stripe = stripeSecretKey ? new Stripe(stripeSecretKey, {
  apiVersion: '2023-10-16',
  typescript: true,
}) : null;

// Exportar configuración adicional
export const stripeConfig = {
  publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
  isProduction: process.env.NODE_ENV === "production",
  hasKey: !!stripeSecretKey,
};

// Solo logear warnings en desarrollo
if (process.env.NODE_ENV === "development") {
  if (!stripeSecretKey) {
    console.warn("⚠️  STRIPE_SECRET_KEY no está configurado en .env.local");
  }

  if (!stripeConfig.publishableKey) {
    console.warn("⚠️  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY no está configurado");
  }

  if (!stripeConfig.webhookSecret) {
    console.warn("⚠️  STRIPE_WEBHOOK_SECRET no está configurado - webhooks no funcionarán");
  }
} 