{"name": "nextjs-starter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/anthropic": "^1.1.2", "@ai-sdk/deepseek": "^0.1.3", "@ai-sdk/groq": "^1.1.4", "@ai-sdk/openai": "^1.1.2", "@clerk/localizations": "^3.16.3", "@clerk/nextjs": "^6.0.2", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@googlemaps/js-api-loader": "^1.16.8", "@heroicons/react": "^2.1.3", "@hookform/resolvers": "^3.3.4", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@neondatabase/serverless": "^0.10.4", "@next/mdx": "^15.1.3", "@next/third-parties": "^15.1.3", "@opentelemetry/api": "^1.9.0", "@phosphor-icons/react": "^2.1.10", "@polar-sh/nextjs": "^0.4.0", "@polar-sh/sdk": "^0.22.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.2.3", "@radix-ui/react-tooltip": "^1.1.5", "@stripe/stripe-js": "^3.4.0", "@supabase/auth-helpers-nextjs": "^0.8.1", "@supabase/ssr": "^0.3.0", "@supabase/supabase-js": "^2.43.3", "@tabler/icons-react": "^2.46.0", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query": "^5.0.1", "@tanstack/react-query-devtools": "^5.0.1", "@tremor/react": "^3.16.2", "@types/mdx": "^2.0.13", "@types/react-syntax-highlighter": "^15.5.13", "@upstash/ratelimit": "^1.2.1", "@upstash/redis": "^1.31.6", "@vercel/analytics": "^1.2.2", "ai": "^4.1.9", "axios": "^1.6.8", "babel-plugin-react-compiler": "^19.0.0-beta-714736e-20250131", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "convex": "^1.18.2", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.38.0", "embla-carousel-react": "^8.6.0", "encoding": "^0.1.13", "fs-extra": "^11.2.0", "geist": "^1.3.0", "html-react-parser": "^5.1.12", "lucide-react": "^0.436.0", "mini-svg-data-uri": "^1.4.4", "motion": "^12.0.5", "next": "latest", "next-cloudinary": "^6.16.0", "next-themes": "^0.3.0", "next-view-transitions": "^0.3.4", "openai": "^5.1.1", "react": "latest", "react-day-picker": "^8.10.1", "react-dom": "latest", "react-dropzone": "^14.3.8", "react-hook-form": "^7.51.3", "react-icons": "^4.11.0", "react-image-gallery": "^1.4.0", "react-markdown": "^9.0.3", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.12.7", "resend": "^4.5.2", "sharp": "^0.33.4", "slugify": "^1.6.6", "sonner": "^1.4.41", "stripe": "^14.25.0", "sugar-high": "^0.8.4", "svix": "^1.13.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "vercel": "^39.3.0", "zod": "^3.25.42"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "latest", "drizzle-kit": "^0.30.0", "eslint": "latest", "eslint-config-next": "latest", "postcss": "latest", "tailwindcss": "^3.3.5", "typescript": "latest"}}