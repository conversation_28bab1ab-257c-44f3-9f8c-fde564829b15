"use client";

import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  ArrowLeft, 
  Upload, 
  MapPin, 
  Home, 
  DollarSign,
  Save,
  Eye,
  AlertCircle,
  AlertTriangle,
  Crown
} from "lucide-react";
import Link from "next/link";
import AmenitiesSection from "./_components/AmenitiesSection";
import { ImageUpload } from "@/components/ui/image-upload";
import { validatePropertyForm, getFieldError, type ValidationError, type PropertyFormData as ValidatedPropertyFormData } from "@/lib/validations";
import { FormError } from "@/components/ui/form-error";
import { toast } from "sonner";
import { RichTextarea } from "@/components/ui/rich-textarea";

interface PropertyFormData {
  title: string;
  description: string;
  price: number;
  currency: string;
  type: string;
  status: string;
  address: string;
  city: string;
  state: string;
  country: string;
  zipCode: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  builtYear: number;
  parking: number;
  amenities: string[];
  images: string[];
  featured: boolean;
}

export default function NewPropertyPage() {
  const { user } = useUser();
  const router = useRouter();
  const createProperty = useMutation(api.properties.createProperty);
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [formData, setFormData] = useState<PropertyFormData>({
    title: "",
    description: "",
    price: 0,
    currency: "GTQ",
    type: "",
    status: "for_sale",
    address: "",
    city: "",
    state: "",
    country: "Guatemala",
    zipCode: "",
    bedrooms: 1,
    bathrooms: 1,
    area: 0,
    builtYear: new Date().getFullYear(),
    parking: 0,
    amenities: [],
    images: [],
    featured: false,
  });

  // Verificar límites antes de mostrar el formulario
  const limitsCheck = useQuery(api.stripe.checkUsageLimits, {
    action: "create_property"
  });

  // Si no está permitido crear propiedades, mostrar mensaje de upgrade
  if (limitsCheck && !limitsCheck.allowed) {
    return (
      <div className="max-w-2xl mx-auto p-4 md:p-6">
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="h-5 w-5" />
              Límite Alcanzado
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700 mb-4">
              {limitsCheck.reason}
            </p>
            
            {limitsCheck.upgrade && (
              <div className="flex flex-col sm:flex-row gap-3">
                <Button 
                  onClick={() => router.push("/dashboard/finance")}
                  className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto"
                >
                  <Crown className="h-4 w-4 mr-2" />
                  Ver Planes de Upgrade
                </Button>
                
                <Button 
                  variant="outline"
                  onClick={() => router.push("/dashboard/properties")}
                  className="w-full sm:w-auto"
                >
                  Volver a Mis Propiedades
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  // Si está cargando, mostrar loading
  if (!limitsCheck) {
    return (
      <div className="max-w-2xl mx-auto p-4 md:p-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Verificando límites de tu plan...</p>
        </div>
      </div>
    );
  }

  const handleInputChange = (field: keyof PropertyFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Limpiar errores del campo cuando el usuario empiece a escribir
    if (validationErrors.length > 0) {
      setValidationErrors(prev => prev.filter(error => error.field !== field));
    }
  };

  const handleSubmit = async (e: React.FormEvent, saveAsDraft = false) => {
    e.preventDefault();
    
    if (!user?.id) {
      toast.error("Debes estar autenticado para crear una propiedad");
      return;
    }

    // Validar formulario
    const errors = validatePropertyForm(formData as ValidatedPropertyFormData, saveAsDraft);
    if (errors.length > 0) {
      setValidationErrors(errors);
      // Scroll al primer error
      const firstErrorField = document.querySelector(`[data-error="${errors[0].field}"]`);
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return;
    }

    setValidationErrors([]);
    setIsSubmitting(true);

    try {
      // Las imágenes ya están procesadas en formData.images

      const propertyData = {
        title: formData.title,
        description: formData.description,
        price: Number(formData.price),
        currency: formData.currency,
        type: formData.type as "house" | "apartment" | "office" | "land" | "commercial",
        status: (saveAsDraft ? "draft" : formData.status) as "for_sale" | "for_rent" | "sold" | "rented" | "draft",
        address: formData.address,
        city: formData.city,
        state: formData.state,
        country: formData.country,
        zipCode: formData.zipCode || undefined,
        bedrooms: formData.bedrooms ? Number(formData.bedrooms) : undefined,
        bathrooms: formData.bathrooms ? Number(formData.bathrooms) : undefined,
        area: Number(formData.area),
        builtYear: formData.builtYear ? Number(formData.builtYear) : undefined,
        parking: formData.parking ? Number(formData.parking) : undefined,
        amenities: formData.amenities,
        images: formData.images,
        ownerId: user.id,
      };

      const propertyId = await createProperty(propertyData);

      if (saveAsDraft) {
        toast.success("¡Propiedad guardada como borrador!");
        router.push("/dashboard/properties");
      } else {
        toast.success("¡Propiedad publicada exitosamente!");
        router.push(`/properties/${propertyId}`);
      }
    } catch (error) {
      console.error("Error creating property:", error);
      toast.error("Error al crear la propiedad. Por favor intenta de nuevo.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col gap-6 p-6 max-w-4xl mx-auto">
      <div className="flex items-center gap-4">
        <Link href="/dashboard/properties" className="flex items-center text-blue-600 hover:text-blue-800">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Volver a Mis Propiedades
        </Link>
      </div>

      <div>
        <h1 className="text-3xl font-semibold tracking-tight">Nueva Propiedad</h1>
        <p className="text-muted-foreground mt-2">
          Completa la información de tu propiedad para publicarla en el marketplace.
        </p>
      </div>

      {/* Resumen de errores */}
      {validationErrors.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-red-800 mb-2">
                  Hay {validationErrors.length} error{validationErrors.length > 1 ? 'es' : ''} que corregir:
                </h3>
                <ul className="text-sm text-red-700 space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={`error-${error.field}-${index}`} className="flex items-center gap-2">
                      <span className="w-1 h-1 bg-red-600 rounded-full"></span>
                      {error.message}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <form onSubmit={(e) => handleSubmit(e, false)} className="space-y-6">
        {/* Información Básica */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Home className="h-5 w-5" />
              Información Básica
            </CardTitle>
            <CardDescription>
              Detalles principales de tu propiedad
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2" data-error="title">
                <Label htmlFor="title">Título de la Propiedad *</Label>
                <Input
                  id="title"
                  placeholder="Ej: Hermosa casa con jardín en Las Condes"
                  value={formData.title}
                  onChange={(e) => handleInputChange("title", e.target.value)}
                  className={`mt-1 ${getFieldError(validationErrors, 'title') ? 'border-red-500' : ''}`}
                  required
                />
                <FormError message={getFieldError(validationErrors, 'title')} />
              </div>
              
              <div data-error="type">
                <Label htmlFor="type">Tipo de Propiedad *</Label>
                <Select onValueChange={(value) => handleInputChange("type", value)}>
                  <SelectTrigger className={`mt-1 ${getFieldError(validationErrors, 'type') ? 'border-red-500' : ''}`}>
                    <SelectValue placeholder="Selecciona el tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="house">Casa</SelectItem>
                    <SelectItem value="apartment">Apartamento</SelectItem>
                    <SelectItem value="office">Oficina</SelectItem>
                    <SelectItem value="land">Terreno</SelectItem>
                    <SelectItem value="commercial">Comercial</SelectItem>
                  </SelectContent>
                </Select>
                <FormError message={getFieldError(validationErrors, 'type')} />
              </div>

              <div>
                <Label htmlFor="status">Estado de Publicación</Label>
                <Select 
                  defaultValue={formData.status}
                  onValueChange={(value) => handleInputChange("status", value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Borrador</SelectItem>
                    <SelectItem value="for_sale">En Venta</SelectItem>
                    <SelectItem value="for_rent">En Alquiler</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="md:col-span-2" data-error="description">
                <Label htmlFor="description">Descripción *</Label>
                <RichTextarea
                  placeholder="Describe tu propiedad en detalle...

Ejemplo de formato:
• Amplia sala de estar
• Cocina moderna equipada
• 3 dormitorios principales

1. Primer piso: sala, cocina, baño
2. Segundo piso: 3 dormitorios
3. Jardín posterior con piscina"
                  value={formData.description}
                  onChange={(value) => handleInputChange("description", value)}
                  className={`mt-1 ${getFieldError(validationErrors, 'description') ? 'border-red-500' : ''}`}
                  minHeight="160px"
                  maxLength={2000}
                />
                <FormError message={getFieldError(validationErrors, 'description')} />
                <div className="text-xs text-gray-500 mt-1">
                  {formData.description.length}/2000 caracteres
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Precio */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Precio
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-2" data-error="price">
                <Label htmlFor="price">Precio *</Label>
                <Input
                  id="price"
                  type="number"
                  placeholder="0"
                  value={formData.price || ""}
                  onChange={(e) => handleInputChange("price", Number(e.target.value))}
                  className={`mt-1 ${getFieldError(validationErrors, 'price') ? 'border-red-500' : ''}`}
                  required
                />
                <FormError message={getFieldError(validationErrors, 'price')} />
              </div>
              
              <div>
                <Label htmlFor="currency">Moneda</Label>
                <Select 
                  defaultValue={formData.currency}
                  onValueChange={(value) => handleInputChange("currency", value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GTQ">GTQ (Quetzal)</SelectItem>
                    <SelectItem value="USD">USD (Dólar)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Ubicación */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Ubicación
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="address">Dirección</Label>
                <Input
                  id="address"
                  placeholder="Ej: Av. Providencia 1234"
                  value={formData.address}
                  onChange={(e) => handleInputChange("address", e.target.value)}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="city">Ciudad</Label>
                <Input
                  id="city"
                  placeholder="Ej: Santiago"
                  value={formData.city}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="state">Región</Label>
                <Input
                  id="state"
                  placeholder="Ej: Región Metropolitana"
                  value={formData.state}
                  onChange={(e) => handleInputChange("state", e.target.value)}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="country">País</Label>
                <Input
                  id="country"
                  value={formData.country}
                  onChange={(e) => handleInputChange("country", e.target.value)}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="zipCode">Código Postal</Label>
                <Input
                  id="zipCode"
                  placeholder="Ej: 7510000"
                  value={formData.zipCode}
                  onChange={(e) => handleInputChange("zipCode", e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Características */}
        <Card>
          <CardHeader>
            <CardTitle>Características</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div data-error="area">
                <Label htmlFor="area">Área Total (m²) *</Label>
                <Input
                  id="area"
                  type="number"
                  placeholder="0"
                  value={formData.area || ""}
                  onChange={(e) => handleInputChange("area", Number(e.target.value))}
                  className={`mt-1 ${getFieldError(validationErrors, 'area') ? 'border-red-500' : ''}`}
                  required
                />
                <FormError message={getFieldError(validationErrors, 'area')} />
              </div>
              
              <div>
                <Label htmlFor="bedrooms">Habitaciones</Label>
                <Input
                  id="bedrooms"
                  type="number"
                  placeholder="0"
                  value={formData.bedrooms || ""}
                  onChange={(e) => handleInputChange("bedrooms", Number(e.target.value))}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="bathrooms">Baños</Label>
                <Input
                  id="bathrooms"
                  type="number"
                  placeholder="0"
                  value={formData.bathrooms || ""}
                  onChange={(e) => handleInputChange("bathrooms", Number(e.target.value))}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="parking">Estacionamientos</Label>
                <Input
                  id="parking"
                  type="number"
                  placeholder="0"
                  value={formData.parking || ""}
                  onChange={(e) => handleInputChange("parking", Number(e.target.value))}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="builtYear">Año de Construcción</Label>
                <Input
                  id="builtYear"
                  type="number"
                  placeholder="2024"
                  value={formData.builtYear || ""}
                  onChange={(e) => handleInputChange("builtYear", Number(e.target.value))}
                  className="mt-1"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Amenidades */}
        <AmenitiesSection 
          selectedAmenities={formData.amenities}
          onAmenitiesChange={(amenities) => handleInputChange("amenities", amenities)}
        />

        {/* Imágenes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Imágenes
            </CardTitle>
            <CardDescription>
              Sube imágenes de tu propiedad (máximo 10)
            </CardDescription>
          </CardHeader>
          <CardContent data-error="images">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h4 className="font-medium text-blue-900 mb-2">💡 Consejos para mejores fotos:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Usa buena iluminación natural</li>
                <li>• Incluye fotos del exterior, interior y detalles</li>
                <li>• Evita objetos personales en las fotos</li>
                <li>• La primera imagen será la principal</li>
              </ul>
            </div>
            <ImageUpload
              images={formData.images}
              onImagesChange={(images) => handleInputChange("images", images)}
              maxImages={10}
            />
            <FormError message={getFieldError(validationErrors, 'images')} />
          </CardContent>
        </Card>

        {/* Botones de Acción */}
        <div className="flex gap-4 justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={(e) => handleSubmit(e as any, true)}
            disabled={isSubmitting}
          >
            <Save className="h-4 w-4 mr-2" />
            Guardar Borrador
          </Button>
          
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Eye className="h-4 w-4 mr-2" />
            {isSubmitting ? "Publicando..." : "Publicar Propiedad"}
          </Button>
        </div>
      </form>
    </div>
  );
} 