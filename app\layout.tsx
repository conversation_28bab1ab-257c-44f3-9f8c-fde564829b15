import Provider from "@/app/provider";
import { Toaster } from "@/components/ui/sonner";
import { esES } from "@clerk/localizations";
import { Analytics } from "@vercel/analytics/react";
import { GeistSans } from "geist/font/sans";
import type { Metadata } from "next";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";

const defaultUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : "http://localhost:3000";

export const metadata: Metadata = {
  metadataBase: new URL("https://inmo-nine.vercel.app/"),
  title: {
    default: 'Inmova - Tu Marketplace Inmobiliario',
    template: `%s | Inmova`
  },
  description:
    "Encuentra, compra, vende y arrienda propiedades de forma fácil y segura. El marketplace inmobiliario más completo de Guatemala.",
  keywords: "inmobiliaria, propiedades, casas, apartamentos, terrenos, Guatemala",
  openGraph: {
    url: "https://inmo-nine.vercel.app/",
    siteName: "Inmova",
    locale: "es_GT",
    type: "website",
    title: "Inmova - Marketplace Inmobiliario",
    description:
      "Encuentra, compra, vende y arrienda propiedades de forma fácil y segura. El marketplace inmobiliario más completo de Guatemala.",
  },
  twitter: {
    card: "summary_large_image",
    creator: "@inmova",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="es" suppressHydrationWarning>
      <body className={GeistSans.className}>
        <Provider>
          <ThemeProvider
            attribute="class"
            defaultTheme="light"
            enableSystem={false}
            disableTransitionOnChange
          >
            {children}
            <Toaster />
          </ThemeProvider>
        </Provider>
        <Analytics />
      </body>
    </html>
  );
}
