"use client";

import React, { useState } from 'react';
import { useMutation, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { 
  Calendar,
  Clock,
  User,
  Phone,
  Video,
  MapPin,
  Check,
  Send,
  X
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { formatTimeFor12h, formatDateLocal, formatTimeRange, formatSlotTime } from '@/lib/time-utils';

interface AppointmentWidgetProps {
  propertyId: Id<"properties">;
  ownerId: string;
  ownerName: string;
  propertyTitle: string;
}

export function AppointmentWidget({ propertyId, ownerId, ownerName, propertyTitle }: AppointmentWidgetProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedSlot, setSelectedSlot] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  
  const [formData, setFormData] = useState({
    guestName: '',
    guestEmail: '',
    guestPhone: '',
    message: '',
    type: 'property_viewing',
    meetingType: 'in_person',
  });



  // Obtener fechas disponibles del usuario (solo fechas con slots reales)
  const availableDates = useQuery(
    api.appointments.getAvailableDates,
    {
      userId: ownerId,
      days: 14,
      duration: 60
    }
  );

  // Obtener slots disponibles para la fecha seleccionada
  const availableSlots = useQuery(
    api.appointments.getAvailableSlots,
    selectedDate ? {
      userId: ownerId,
      date: selectedDate,
      duration: 60
    } : "skip"
  );

  // Mutation para crear solicitud
  const requestAppointment = useMutation(api.appointments.requestAppointment);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedSlot || !formData.guestName || !formData.guestEmail) return;

    setIsSubmitting(true);
    try {
      await requestAppointment({
        hostId: ownerId,
        guestId: `guest-${Date.now()}`, // ID temporal para invitados
        guestName: formData.guestName,
        guestEmail: formData.guestEmail,
        guestPhone: formData.guestPhone || undefined,
        propertyId,
        requestedStartTime: selectedSlot.startTime,
        requestedEndTime: selectedSlot.endTime,
        message: formData.message || undefined,
        type: formData.type as any,
        meetingType: formData.meetingType as any,
      });

      setIsSuccess(true);
      setTimeout(() => {
        setIsSuccess(false);
        setIsOpen(false);
        resetForm();
      }, 3000);
    } catch (error) {
      console.error('Error requesting appointment:', error);
      alert('Error al enviar la solicitud. Intenta nuevamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData({
      guestName: '',
      guestEmail: '',
      guestPhone: '',
      message: '',
      type: 'property_viewing',
      meetingType: 'in_person',
    });
    setSelectedDate('');
    setSelectedSlot(null);
  };

  const getMeetingTypeIcon = (type: string) => {
    switch (type) {
      case 'video_call': return <Video className="w-4 h-4" />;
      case 'phone_call': return <Phone className="w-4 h-4" />;
      case 'in_person': return <MapPin className="w-4 h-4" />;
      default: return <Calendar className="w-4 h-4" />;
    }
  };



  if (isSuccess) {
    return (
      <Card className="border-green-200 bg-green-50">
        <CardContent className="p-6 text-center">
          <Check className="w-12 h-12 text-green-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-green-900 mb-2">
            ¡Solicitud Enviada!
          </h3>
          <p className="text-green-700">
            Tu solicitud de cita ha sido enviada a {ownerName}. 
            Te contactaremos pronto para confirmar los detalles.
          </p>
        </CardContent>
      </Card>
    );
  }

  if (!isOpen) {
    return (
      <Card className="border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-gray-900">¿Interesado en esta propiedad?</h3>
              <p className="text-sm text-gray-600">Agenda una cita con {ownerName}</p>
            </div>
            <Button 
              onClick={() => setIsOpen(true)}
              className="bg-blue-600 hover:bg-blue-700 rounded-xl"
            >
              <Calendar className="w-4 h-4 mr-2" />
              Agendar Cita
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-md mx-0 p-0 gap-0 max-h-[100vh] md:mx-4 md:p-6 md:gap-6 md:max-h-[90vh] md:rounded-lg
                               max-w-full h-[100vh] md:h-auto w-full md:w-auto
                               overflow-hidden md:overflow-visible flex flex-col">
        {/* Header fijo en móvil */}
        <div className="flex-shrink-0 bg-white border-b md:border-0 p-4 md:p-0 flex items-center justify-between md:block z-10">
          <div className="flex-1 md:mb-4">
            <DialogTitle className="text-lg md:text-xl font-bold text-left">Solicitar Cita</DialogTitle>
            <DialogDescription className="hidden md:block mt-2">
              Agenda una cita con {ownerName} para visitar esta propiedad
            </DialogDescription>
          </div>
          {/* Botón cerrar visible solo en móvil */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsOpen(false)}
            className="md:hidden"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
        
        {/* Contenido con scroll en móvil */}
        <div className="flex-1 overflow-y-auto md:overflow-visible p-4 md:p-0 min-h-0">
          {/* Info de la propiedad en móvil */}
          <div className="bg-blue-50 p-3 rounded-lg mb-6 md:hidden">
            <div className="text-sm font-medium text-blue-900 mb-1">{propertyTitle}</div>
            <div className="text-xs text-blue-700">Cita con {ownerName}</div>
          </div>

          <form id="appointment-form" onSubmit={handleSubmit} className="space-y-6 pb-4">
                        {/* Selección de fecha y hora específica */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ¿Cuándo te gustaría la cita con {ownerName}?
                </label>
                
                {/* Selección de fecha */}
                <div className="mb-4">
                  <Select value={selectedDate} onValueChange={setSelectedDate}>
                    <SelectTrigger className="rounded-xl h-12 text-base">
                      <SelectValue placeholder={
                        availableDates === undefined 
                          ? "Cargando fechas disponibles..." 
                          : availableDates.length === 0 
                            ? "Sin fechas disponibles" 
                            : "Primero selecciona una fecha"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {availableDates?.map((date) => (
                        <SelectItem key={date.value} value={date.value}>
                          <div className="flex items-center justify-between w-full">
                            <span>{date.label}</span>
                            <span className="text-xs text-gray-500 ml-2">
                              {date.availableFrom} - {date.availableTo}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                      {availableDates && availableDates.length === 0 && (
                        <SelectItem value="" disabled>
                          {ownerName} no tiene fechas disponibles en las próximas 2 semanas
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                {/* Selección de hora específica */}
                {selectedDate && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Selecciona la hora exacta
                    </label>
                    {availableSlots === undefined ? (
                      <div className="flex items-center justify-center py-8 bg-gray-50 rounded-xl">
                        <p className="text-sm text-gray-500">Cargando horarios disponibles...</p>
                      </div>
                    ) : availableSlots.length === 0 ? (
                      <div className="flex items-center justify-center py-8 bg-red-50 rounded-xl border border-red-200">
                        <p className="text-sm text-red-600">
                          😞 No hay horarios disponibles para esta fecha. Intenta con otra fecha.
                        </p>
                      </div>
                    ) : (
                      <div className="bg-gray-50 rounded-xl p-4">
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          {availableSlots.map((slot, index) => (
                            <Button
                              key={`slot-${slot.startTime}-${index}`}
                              type="button"
                              variant={selectedSlot?.startTime === slot.startTime ? "default" : "outline"}
                              size="default"
                              onClick={() => setSelectedSlot(slot)}
                              className={`rounded-xl h-12 flex flex-col items-center justify-center p-2 ${
                                selectedSlot?.startTime === slot.startTime 
                                  ? 'bg-blue-600 text-white shadow-md' 
                                  : 'bg-white hover:bg-blue-50 border-gray-300'
                              }`}
                            >
                                                             <span className="font-semibold text-sm">
                                 {formatSlotTime(slot.startTime)}
                               </span>
                              <span className="text-xs opacity-75">
                                60 min
                              </span>
                            </Button>
                          ))}
                        </div>
                        <p className="text-xs text-gray-600 mt-3 text-center">
                          💡 Selecciona el horario que más te convenga
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Tipo de cita y modalidad */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo de Cita
                </label>
                <Select value={formData.type} onValueChange={(value) => setFormData({ ...formData, type: value })}>
                  <SelectTrigger className="rounded-xl h-12 text-base">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="property_viewing">Visita a la Propiedad</SelectItem>
                    <SelectItem value="consultation">Consulta</SelectItem>
                    <SelectItem value="negotiation">Negociación</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Modalidad
                </label>
                <Select value={formData.meetingType} onValueChange={(value) => setFormData({ ...formData, meetingType: value })}>
                  <SelectTrigger className="rounded-xl h-12 text-base">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="in_person">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-3 h-3" />
                        Presencial
                      </div>
                    </SelectItem>
                    <SelectItem value="video_call">
                      <div className="flex items-center gap-2">
                        <Video className="w-3 h-3" />
                        Videollamada
                      </div>
                    </SelectItem>
                    <SelectItem value="phone_call">
                      <div className="flex items-center gap-2">
                        <Phone className="w-3 h-3" />
                        Llamada
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Información personal */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nombre Completo *
                </label>
                <Input
                  placeholder="Tu nombre completo"
                  value={formData.guestName}
                  onChange={(e) => setFormData({ ...formData, guestName: e.target.value })}
                  className="rounded-xl h-12 text-base"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email *
                </label>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.guestEmail}
                  onChange={(e) => setFormData({ ...formData, guestEmail: e.target.value })}
                  className="rounded-xl h-12 text-base"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Teléfono (opcional)
                </label>
                <Input
                  placeholder="+52 ************"
                  value={formData.guestPhone}
                  onChange={(e) => setFormData({ ...formData, guestPhone: e.target.value })}
                  className="rounded-xl h-12 text-base"
                />
              </div>
            </div>



            {/* Mensaje */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mensaje (opcional)
              </label>
              <Textarea
                placeholder="Cuéntanos qué te interesa de esta propiedad o cualquier pregunta específica..."
                value={formData.message}
                onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                className="rounded-xl min-h-[100px] md:min-h-[120px] resize-none text-base"
                rows={3}
              />
            </div>

            {/* Resumen de la cita */}
            {selectedSlot && (
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <h4 className="font-medium text-blue-900 mb-2">Resumen de tu Solicitud</h4>
                <div className="space-y-1 text-sm text-blue-800">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-3 h-3" />
                    {formatDateLocal(selectedSlot.startTime)}
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-3 h-3" />
                    {formatTimeRange(selectedSlot.startTime, selectedSlot.endTime)}
                  </div>
                  <div className="flex items-center gap-2">
                    {getMeetingTypeIcon(formData.meetingType)}
                    {formData.meetingType === 'in_person' && 'Presencial'}
                    {formData.meetingType === 'video_call' && 'Videollamada'}
                    {formData.meetingType === 'phone_call' && 'Llamada telefónica'}
                  </div>
                </div>
              </div>
            )}
          </form>
        </div>
        
        {/* Footer fijo en móvil con botones */}
        <div className="flex-shrink-0 bg-white border-t md:border-0 p-4 md:p-0">
          <div className="flex gap-3">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setIsOpen(false)}
              className="flex-1 rounded-xl h-12 md:h-12 font-medium"
            >
              Cancelar
            </Button>
            <Button 
              type="submit" 
              form="appointment-form"
              disabled={!selectedSlot || !formData.guestName || !formData.guestEmail || isSubmitting}
              className="flex-1 bg-blue-600 hover:bg-blue-700 rounded-xl h-12 md:h-12 font-semibold"
            >
              <Send className="w-4 h-4 mr-2" />
              {isSubmitting ? 'Enviando...' : 'Enviar Solicitud'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 