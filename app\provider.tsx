"use client";
import { <PERSON><PERSON><PERSON><PERSON>, useAuth } from "@clerk/nextjs";
import { esES } from "@clerk/localizations";
import { ConvexReactClient } from "convex/react";
import { ConvexProviderWithClerk } from "convex/react-clerk";
import { ReactNode, createContext, useContext, useEffect, useState } from "react";
import PageWrapper from "@/components/wrapper/page-wrapper";
import StoreUserEffect from "@/components/store-user-effect";

const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

// Contexto para el tema de colores
interface ThemeColorContextType {
  themeColor: string;
  setThemeColor: (color: string) => void;
}

const ThemeColorContext = createContext<ThemeColorContextType>({
  themeColor: 'blue',
  setThemeColor: () => {},
});

export const useThemeColor = () => useContext(ThemeColorContext);

// Función para aplicar colores del tema dinámicamente
const applyThemeColors = (color: string) => {
  const root = document.documentElement;
  
  const colorMap: Record<string, string> = {
    blue: '#2563eb',
    purple: '#7c3aed',
    green: '#16a34a',
    red: '#dc2626',
    orange: '#ea580c',
    pink: '#db2777',
    teal: '#0d9488',
    amber: '#d97706',
  };

  const primaryColor = colorMap[color] || colorMap.blue;
  
  // Aplicar variables CSS personalizadas
  root.style.setProperty('--color-primary', primaryColor);
  
  // Calcular variaciones automáticamente
  const hex = primaryColor.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  // Versión más oscura para hover
  const darkerR = Math.max(0, r - 30);
  const darkerG = Math.max(0, g - 30);
  const darkerB = Math.max(0, b - 30);
  const darkerColor = `rgb(${darkerR}, ${darkerG}, ${darkerB})`;
  
  root.style.setProperty('--color-primary-dark', darkerColor);
  
  // Versión más clara para backgrounds
  const lighterColor = `rgba(${r}, ${g}, ${b}, 0.1)`;
  root.style.setProperty('--color-primary-light', lighterColor);
  
  // Actualizar el valor de Tailwind CSS dinámicamente
  root.style.setProperty('--tw-color-primary', `${r} ${g} ${b}`);
};

function ThemeColorProvider({ children }: { children: ReactNode }) {
  const [themeColor, setThemeColorState] = useState('blue');

  const setThemeColor = (color: string) => {
    setThemeColorState(color);
    applyThemeColors(color);
    localStorage.setItem('theme-color', color);
  };

  useEffect(() => {
    // Cargar color guardado al inicializar
    const savedColor = localStorage.getItem('theme-color') || 'blue';
    setThemeColorState(savedColor);
    applyThemeColors(savedColor);
  }, []);

  return (
    <ThemeColorContext.Provider value={{ themeColor, setThemeColor }}>
      {children}
    </ThemeColorContext.Provider>
  );
}

export default function Provider({ children }: { children: ReactNode }) {
  return (
    <ClerkProvider 
      publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY!}
      localization={esES}
      appearance={{
        variables: {
          colorPrimary: "#2563eb",
          colorBackground: "#ffffff",
          colorText: "#1f2937",
          colorTextSecondary: "#6b7280",
          colorNeutral: "#f3f4f6",
          borderRadius: "0.75rem"
        },
        elements: {
          formButtonPrimary: "bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300",
          card: "rounded-2xl shadow-xl border-0",
          headerTitle: "text-2xl font-bold text-gray-900",
          headerSubtitle: "text-gray-600",
          socialButtonsBlockButton: "border-2 border-gray-200 hover:border-blue-300 rounded-xl py-3 transition-all",
          formFieldLabel: "text-gray-700 font-medium",
          formFieldInput: "rounded-xl border-gray-300 focus:border-blue-500 focus:ring-blue-500",
          footerActionText: "text-gray-600",
          footerActionLink: "text-blue-600 hover:text-blue-700",
          dividerLine: "bg-gray-200",
          dividerText: "text-gray-500",
          socialButtonsBlockButtonText: "text-gray-700 font-medium",
        },
        layout: {
          socialButtonsVariant: "blockButton",
          socialButtonsPlacement: "top"
        }
      }}
      signInFallbackRedirectUrl="/dashboard"
      signUpFallbackRedirectUrl="/onboarding"
    >
      <ConvexProviderWithClerk client={convex} useAuth={useAuth}>
        <StoreUserEffect />
        <ThemeColorProvider>
          <PageWrapper>
            {children}
          </PageWrapper>
        </ThemeColorProvider>
      </ConvexProviderWithClerk>
    </ClerkProvider>
  );
}

