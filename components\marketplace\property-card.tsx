"use client";

import { Property } from "@/types/marketplace";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Heart, MapPin } from "lucide-react";
import { Bed, Bathtub, Car, Square } from "@phosphor-icons/react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

interface PropertyCardProps {
  property: Property;
  onFavorite?: (propertyId: string) => void;
  isFavorite?: boolean;
}

export function PropertyCard({ property, onFavorite, isFavorite = false }: PropertyCardProps) {
  const [imageError, setImageError] = useState(false);
  
  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(price);
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      for_sale: { label: 'En Venta', variant: 'default' as const },
      for_rent: { label: 'En Alquiler', variant: 'secondary' as const },
      sold: { label: 'Vendido', variant: 'destructive' as const },
      rented: { label: 'Alquilado', variant: 'outline' as const },
    };
    
    return statusMap[status as keyof typeof statusMap] || { label: status, variant: 'default' as const };
  };

  const getTypeLabel = (type: string) => {
    const typeMap = {
      house: 'Casa',
      apartment: 'Apartamento',
      office: 'Oficina',
      land: 'Terreno',
      commercial: 'Comercial',
    };
    
    return typeMap[type as keyof typeof typeMap] || type;
  };



  const mainImage = property.images?.[0] || '/placeholder-property.jpg';
  const statusInfo = getStatusBadge(property.status);

  return (
    <Card className="group bg-white border-0 shadow-sm hover:shadow-xl transition-all duration-500 overflow-hidden rounded-2xl">
      <div className="relative aspect-[4/3] overflow-hidden">
        <Link href={`/properties/${property._id}`}>
          <Image
            src={imageError ? '/placeholder-property.jpg' : mainImage}
            alt={property.title}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
            onError={() => setImageError(true)}
          />
        </Link>
        
        {/* Light gradient overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-t from-white/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
        
        {/* Badges */}
        <div className="absolute top-2 md:top-4 left-2 md:left-4 flex flex-col sm:flex-row gap-1 sm:gap-2 pointer-events-none">
          <Badge 
            variant={statusInfo.variant}
            className="backdrop-blur-md bg-white/95 text-gray-900 border-0 font-medium shadow-lg text-xs sm:text-sm"
          >
            {statusInfo.label}
          </Badge>
          {property.featured && (
            <Badge className="backdrop-blur-md bg-gradient-to-r from-amber-400 to-orange-500 text-white border-0 font-medium shadow-lg text-xs sm:text-sm">
              ✨ Destacado
            </Badge>
          )}
        </div>

        {/* Favorite button */}
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onFavorite?.(property._id);
          }}
          className="absolute top-2 md:top-4 right-2 md:right-4 w-8 h-8 md:w-10 md:h-10 bg-white/90 hover:bg-white rounded-full flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl backdrop-blur-sm pointer-events-auto z-10"
        >
          <Heart className={`h-4 w-4 md:h-5 md:w-5 transition-colors ${isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-600 hover:text-red-500'}`} />
        </button>

        {/* Price overlay for mobile */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 md:hidden pointer-events-none">
          <div className="text-white">
            <div className="text-lg font-bold">
              {formatPrice(property.price, property.currency)}
            </div>
            <div className="text-xs opacity-90">
              {property.status === 'for_rent' ? 'por mes' : 'precio total'}
            </div>
          </div>
        </div>
      </div>

      <CardContent className="p-3 md:p-6">
        <div className="space-y-3 md:space-y-4">
          {/* Title and Location */}
          <div>
            <h3 className="font-bold text-base md:text-lg text-gray-900 mb-1 md:mb-2 line-clamp-2 leading-tight">
              {property.title}
            </h3>
            <div className="flex items-center text-gray-600 mb-2 md:mb-3">
              <MapPin className="h-3 w-3 md:h-4 md:w-4 mr-1 flex-shrink-0" />
              <span className="text-xs md:text-sm truncate">{property.address}, {property.city}</span>
            </div>
          </div>

          {/* Price - hidden on mobile as it's shown in overlay */}
          <div className="hidden md:block">
            <div className="text-xl md:text-2xl font-bold text-blue-600 mb-1">
              {formatPrice(property.price, property.currency)}
            </div>
            <div className="text-xs md:text-sm text-gray-600">
              {property.status === 'for_rent' ? 'por mes' : 'precio total'}
            </div>
          </div>

          {/* Property Details */}
          <div className="flex items-center justify-between gap-2 md:gap-4 text-gray-600">
            {property.bedrooms && property.bedrooms > 0 && (
              <div className="flex items-center gap-1 md:gap-1.5 flex-shrink-0">
                <div className="p-1 md:p-1.5 bg-blue-50 rounded-lg">
                  <Bed className="h-3 w-3 md:h-4 md:w-4 text-blue-600" />
                </div>
                <span className="font-medium text-xs md:text-sm">{property.bedrooms}</span>
              </div>
            )}

            {property.bathrooms && property.bathrooms > 0 && (
              <div className="flex items-center gap-1 md:gap-1.5 flex-shrink-0">
                <div className="p-1 md:p-1.5 bg-green-50 rounded-lg">
                  <Bathtub className="h-3 w-3 md:h-4 md:w-4 text-green-600" />
                </div>
                <span className="font-medium text-xs md:text-sm">{property.bathrooms}</span>
              </div>
            )}

            {property.area && property.area > 0 && (
              <div className="flex items-center gap-1 md:gap-1.5 flex-shrink-0">
                <div className="p-1 md:p-1.5 bg-purple-50 rounded-lg">
                  <Square className="h-3 w-3 md:h-4 md:w-4 text-purple-600" />
                </div>
                <span className="font-medium text-xs md:text-sm">{property.area}m²</span>
              </div>
            )}
            
            {property.parking && property.parking > 0 && (
              <div className="flex items-center gap-1 md:gap-1.5 flex-shrink-0">
                <div className="p-1 md:p-1.5 bg-orange-50 rounded-lg">
                  <Car className="h-3 w-3 md:h-4 md:w-4 text-orange-600" />
                </div>
                <span className="font-medium text-xs md:text-sm">{property.parking}</span>
              </div>
            )}
          </div>

          {/* Amenidades destacadas */}
          {property.amenities && property.amenities.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {property.amenities.slice(0, 3).map((amenity, index) => (
                <Badge 
                  key={`${property._id}-amenity-${amenity}-${index}`} 
                  variant="secondary" 
                  className="text-xs bg-blue-50 text-blue-700 border-blue-200"
                >
                  {amenity}
                </Badge>
              ))}
              {property.amenities.length > 3 && (
                <Badge variant="secondary" className="text-xs bg-gray-50 text-gray-600">
                  +{property.amenities.length - 3} más
                </Badge>
              )}
            </div>
          )}

          {/* Type and action */}
          <div className="flex items-center justify-between gap-2 pt-2 md:pt-3">
            <Badge variant="outline" className="text-gray-600 border-gray-200 bg-gray-50 self-start text-xs">
              {getTypeLabel(property.type)}
            </Badge>
            
            <Link href={`/properties/${property._id}`} className="flex-shrink-0">
              <Button 
                className="bg-blue-600 hover:bg-blue-700 text-white border-0 rounded-xl font-medium px-3 py-2 md:px-6 md:py-3 shadow-lg hover:shadow-xl transition-all duration-300 text-xs md:text-sm"
                size="sm"
              >
                <span className="hidden sm:inline">Ver detalles</span>
                <span className="sm:hidden">Ver</span>
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 