"use client"
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { CreditsDisplay } from "@/components/ui/credits-display";
import { useUser } from '@clerk/nextjs';
import { UserProfile } from '@clerk/nextjs';
import { useTheme } from 'next-themes';
import { Bell, Globe, Lock, Mail, Palette, Shield, Trash2 } from "lucide-react";
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useThemeColor } from '@/app/provider';
import { Dialog, DialogContent, DialogTrigger, DialogTitle, DialogHeader } from "@/components/ui/dialog";
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useRouter } from 'next/navigation';

export default function SettingsPage() {
  const { user } = useUser();
  const { theme, setTheme } = useTheme();
  const { themeColor, setThemeColor } = useThemeColor();
  const router = useRouter();
  const deleteUserMutation = useMutation(api.users.deleteUser);
  const syncProfileMutation = useMutation(api.users.syncPublicProfile);
  
  // Estados locales para las configuraciones
  const [pushNotifications, setPushNotifications] = useState(false);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [marketingEmails, setMarketingEmails] = useState(false);
  const [twoFactorAuth, setTwoFactorAuth] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState("es");
  const [selectedTimezone, setSelectedTimezone] = useState("cl");
  
  // Estados para el formulario de perfil
  const [profileData, setProfileData] = useState({
    name: "",
    email: "",
    bio: ""
  });

  // Cargar datos del usuario al montar el componente
  useEffect(() => {
    if (user) {
      const metadata = user.unsafeMetadata as any || {};
      setProfileData({
        name: (metadata.displayName as string) || user.firstName || "",
        email: user.emailAddresses?.[0]?.emailAddress || "",
        bio: (metadata.bio as string) || ""
      });
    }
  }, [user]);

  // Cargar color del tema guardado (ya no es necesario porque el contexto global lo maneja)
  useEffect(() => {
    // El contexto global ya maneja la carga del color guardado
  }, []);

  // Funciones para manejar cambios
  const handleSaveProfile = async () => {
    if (!user) return;
    
    try {
      // Guardar información personalizada en metadatos de Clerk
      const currentMetadata = user.unsafeMetadata as any || {};
      const newMetadata = {
        ...currentMetadata,
        displayName: profileData.name.trim(),
        bio: profileData.bio.trim(),
        lastUpdated: new Date().toISOString()
      };

      // Solo actualizar si hay cambios reales
      const hasChanges = 
        newMetadata.displayName !== currentMetadata.displayName ||
        newMetadata.bio !== currentMetadata.bio;

      if (hasChanges) {
        // Actualizar metadatos en Clerk
        await user.update({
          unsafeMetadata: newMetadata
        });
        
        // Sincronizar con la base de datos local
        await syncProfileMutation({
          displayName: newMetadata.displayName,
          bio: newMetadata.bio
        });
        
        toast.success("¡Información pública actualizada!");
      } else {
        toast.info("No hay cambios para guardar");
      }
    } catch (error: any) {
      console.error("Error actualizando perfil:", error);
      toast.error("Error al actualizar el perfil. Intenta de nuevo en unos momentos.");
    }
  };

  const handleNotificationChange = (type: string, value: boolean) => {
    switch (type) {
      case 'push':
        setPushNotifications(value);
        toast.success(`Notificaciones push ${value ? 'activadas' : 'desactivadas'}`);
        break;
      case 'email':
        setEmailNotifications(value);
        toast.success(`Notificaciones por email ${value ? 'activadas' : 'desactivadas'}`);
        break;
      case 'marketing':
        setMarketingEmails(value);
        toast.success(`Emails de marketing ${value ? 'activados' : 'desactivados'}`);
        break;
    }
  };

  const handleTwoFactorToggle = (value: boolean) => {
    setTwoFactorAuth(value);
    toast.success(`Autenticación de dos factores ${value ? 'activada' : 'desactivada'}`);
  };

  const handleLanguageChange = (language: string) => {
    setSelectedLanguage(language);
    toast.success("Idioma actualizado");
  };

  const handleTimezoneChange = (timezone: string) => {
    setSelectedTimezone(timezone);
    toast.success("Zona horaria actualizada");
  };

  const handleThemeColorChange = (color: string) => {
    setThemeColor(color);
    toast.success(`Color del tema cambiado a ${color}`);
  };

  const handlePasswordUpdate = () => {
    toast.success("Contraseña actualizada correctamente");
  };

  const handleDeleteAccount = async () => {
    if (!confirm('¿Estás seguro de que quieres eliminar tu cuenta? Esta acción no se puede deshacer.')) {
      return;
    }

    try {
      await deleteUserMutation();
      toast.success("Cuenta eliminada correctamente");
      router.push('/');
    } catch (error) {
      console.error("Error eliminando cuenta:", error);
      toast.error("Error al eliminar la cuenta");
    }
  };

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-semibold tracking-tight">Configuración</h1>
          <p className="text-muted-foreground mt-2">Gestiona la configuración de tu cuenta y preferencias</p>
        </div>
        
        <CreditsDisplay variant="compact" />
      </div>

      <Tabs defaultValue="account" className="space-y-6">
        <TabsList>
          <TabsTrigger value="account">Cuenta</TabsTrigger>
          <TabsTrigger value="notifications">Notificaciones</TabsTrigger>
          <TabsTrigger value="appearance">Apariencia</TabsTrigger>
          <TabsTrigger value="security">Seguridad</TabsTrigger>
        </TabsList>

        <TabsContent value="account">
          <div className="space-y-6">
            {/* Profile Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Información Pública</CardTitle>
                <CardDescription>Esta información aparecerá en tus propiedades publicadas</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nombre público</Label>
                    <Input 
                      id="name" 
                      placeholder="Ej: Juan Pérez" 
                      value={profileData.name}
                      onChange={(e) => setProfileData({...profileData, name: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="bio">Descripción profesional</Label>
                    <Textarea 
                      id="bio" 
                      placeholder="Ej: Agente inmobiliario con 5 años de experiencia en la zona norte..." 
                      value={profileData.bio}
                      onChange={(e) => setProfileData({...profileData, bio: e.target.value})}
                      rows={3}
                    />
                  </div>
                  
                  <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleSaveProfile}>
                    Guardar Información Pública
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Account Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Configuración de Cuenta</CardTitle>
                <CardDescription>Cambiar foto de perfil, email, contraseña y configuración de seguridad</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={user?.imageUrl} alt="Usuario" />
                    <AvatarFallback>
                      {(profileData.name || user?.firstName || "U").charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">{user?.emailAddresses?.[0]?.emailAddress}</p>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline">
                          Administrar Cuenta
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>Configuración de Cuenta</DialogTitle>
                        </DialogHeader>
                        <UserProfile 
                          routing="hash"
                          appearance={{
                            elements: {
                              rootBox: "w-full",
                              card: "shadow-none border-0"
                            }
                          }}
                        />
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Preferences */}
            <Card>
              <CardHeader>
                <CardTitle>Preferencias</CardTitle>
                <CardDescription>Gestiona las preferencias de tu cuenta</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Idioma</Label>
                    <p className="text-sm text-muted-foreground">Selecciona tu idioma preferido</p>
                  </div>
                  <Select value={selectedLanguage} onValueChange={handleLanguageChange}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Seleccionar idioma" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="es">Español</SelectItem>
                      <SelectItem value="en">Inglés</SelectItem>
                      <SelectItem value="fr">Francés</SelectItem>
                      <SelectItem value="de">Alemán</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Zona Horaria</Label>
                    <p className="text-sm text-muted-foreground">Configura tu zona horaria local</p>
                  </div>
                  <Select value={selectedTimezone} onValueChange={handleTimezoneChange}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Seleccionar zona horaria" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="gt">Guatemala (GTQ)</SelectItem>
                      <SelectItem value="ar">Argentina (ART)</SelectItem>
                      <SelectItem value="utc">UTC</SelectItem>
                      <SelectItem value="gmt">GMT</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Notificaciones</CardTitle>
              <CardDescription>Gestiona tus preferencias de notificaciones</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Bell className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <Label>Notificaciones Push</Label>
                      <p className="text-sm text-muted-foreground">Recibir notificaciones push</p>
                    </div>
                  </div>
                  <Switch 
                    checked={pushNotifications}
                    onCheckedChange={(value) => handleNotificationChange('push', value)}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Mail className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <Label>Notificaciones por Email</Label>
                      <p className="text-sm text-muted-foreground">Recibir actualizaciones por email</p>
                    </div>
                  </div>
                  <Switch 
                    checked={emailNotifications}
                    onCheckedChange={(value) => handleNotificationChange('email', value)}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Globe className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <Label>Emails de Marketing</Label>
                      <p className="text-sm text-muted-foreground">Recibir emails de marketing</p>
                    </div>
                  </div>
                  <Switch 
                    checked={marketingEmails}
                    onCheckedChange={(value) => handleNotificationChange('marketing', value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="appearance">
          <Card>
            <CardHeader>
              <CardTitle>Apariencia</CardTitle>
              <CardDescription>Personaliza la apariencia y el estilo</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Palette className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <Label>Color del Tema</Label>
                      <p className="text-sm text-muted-foreground">Elige el color de tu tema</p>
                    </div>
                  </div>
                  <Select value={themeColor} onValueChange={handleThemeColorChange}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Seleccionar color" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="blue">Azul</SelectItem>
                      <SelectItem value="purple">Morado</SelectItem>
                      <SelectItem value="orange">Naranja</SelectItem>
                      <SelectItem value="red">Rojo</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Seguridad</CardTitle>
              <CardDescription>Configuración de seguridad y privacidad</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Lock className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <Label>Autenticación de Dos Factores</Label>
                      <p className="text-sm text-muted-foreground">Añade una capa extra de seguridad a tu cuenta</p>
                    </div>
                  </div>
                  <Switch 
                    checked={twoFactorAuth}
                    onCheckedChange={handleTwoFactorToggle}
                  />
                </div>
                <Separator />
                <div className="space-y-2">
                  <Label className="text-red-600">Eliminar Cuenta</Label>
                  <p className="text-sm text-muted-foreground">
                    Esta acción eliminará permanentemente tu cuenta y todas tus propiedades. No se puede deshacer.
                  </p>
                  <Button 
                    variant="destructive"
                    onClick={handleDeleteAccount}
                    className="gap-2"
                  >
                    <Trash2 className="h-4 w-4" />
                    Eliminar Cuenta Permanentemente
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
