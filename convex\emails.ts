import { v } from "convex/values";
import { action } from "./_generated/server";

// Configuración de Resend
const RESEND_API_KEY = process.env.RESEND_API_KEY;

// Templates de email
const EMAIL_TEMPLATES = {
  appointmentRequestOwner: (data: any) => ({
    subject: `Nueva solicitud de cita - ${data.propertyTitle}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Nueva Solicitud de Cita</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
            .property-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb; }
            .client-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; }
            .button:hover { background: #1d4ed8; }
            .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🏠 Nueva Solicitud de Cita</h1>
            </div>
            <div class="content">
              <p>¡Hola! Has recibido una nueva solicitud de cita para tu propiedad.</p>
              
              <div class="property-card">
                <h3>📍 ${data.propertyTitle}</h3>
                <p><strong>Ubicación:</strong> ${data.propertyAddress}</p>
                <p><strong>Precio:</strong> ${data.propertyPrice}</p>
              </div>

              <div class="client-info">
                <h3>👤 Información del Cliente</h3>
                <p><strong>Nombre:</strong> ${data.guestName}</p>
                <p><strong>Email:</strong> ${data.guestEmail}</p>
                ${data.guestPhone ? `<p><strong>Teléfono:</strong> ${data.guestPhone}</p>` : ''}
                <p><strong>Fecha solicitada:</strong> ${data.requestedDate}</p>
                <p><strong>Hora:</strong> ${data.requestedTime}</p>
                <p><strong>Modalidad:</strong> ${data.meetingType === 'in_person' ? 'Presencial' : data.meetingType === 'video_call' ? 'Videollamada' : 'Llamada telefónica'}</p>
                ${data.message ? `<p><strong>Mensaje:</strong><br/>${data.message}</p>` : ''}
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.dashboardUrl}" class="button">Ver en Mi Agenda</a>
              </div>

              <p><strong>⏰ ¿Qué hacer ahora?</strong></p>
              <ol>
                <li>Revisa la solicitud en tu agenda</li>
                <li>Confirma o propón una nueva fecha</li>
                <li>El cliente será notificado automáticamente</li>
              </ol>
            </div>
            <div class="footer">
              <p>Este email fue enviado por <strong>Inmova.gt</strong><br/>
              Si no puedes ver el botón, visita: ${data.dashboardUrl}</p>
            </div>
          </div>
        </body>
      </html>
    `
  }),

  appointmentRequestGuest: (data: any) => ({
    subject: `Solicitud de cita recibida - ${data.propertyTitle}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Solicitud Recibida</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #10b981; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f0fdf4; padding: 30px; border-radius: 0 0 8px 8px; }
            .property-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981; }
            .status-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }
            .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>✅ ¡Solicitud Recibida!</h1>
            </div>
            <div class="content">
              <p>Hola <strong>${data.guestName}</strong>,</p>
              <p>Hemos recibido tu solicitud de visita. Un agente se pondrá en contacto contigo pronto.</p>
              
              <div class="property-card">
                <h3>📍 Propiedad Solicitada</h3>
                <p><strong>${data.propertyTitle}</strong></p>
                <p>${data.propertyAddress}</p>
                <p><strong>Precio:</strong> ${data.propertyPrice}</p>
              </div>

              <div class="status-card">
                <h3>📅 Detalles de tu Solicitud</h3>
                <p><strong>Fecha solicitada:</strong> ${data.requestedDate}</p>
                <p><strong>Hora:</strong> ${data.requestedTime}</p>
                <p><strong>Modalidad:</strong> ${data.meetingType === 'in_person' ? 'Presencial' : data.meetingType === 'video_call' ? 'Videollamada' : 'Llamada telefónica'}</p>
                <p style="background: #fef3c7; padding: 10px; border-radius: 6px; margin-top: 15px;">
                  <strong>Estado:</strong> Pendiente de confirmación
                </p>
              </div>

              <p><strong>🕐 ¿Qué sigue?</strong></p>
              <ul>
                <li>El propietario revisará tu solicitud</li>
                <li>Te contactaremos para confirmar la cita</li>
                <li>Recibirás una notificación con la confirmación</li>
              </ul>

              <p>Si tienes alguna pregunta, no dudes en contactarnos.</p>
            </div>
            <div class="footer">
              <p>Gracias por usar <strong>Inmova.gt</strong><br/>
              Tu plataforma inmobiliaria de confianza</p>
            </div>
          </div>
        </body>
      </html>
    `
  }),

  appointmentConfirmed: (data: any) => ({
    subject: `Cita confirmada - ${data.propertyTitle}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Cita Confirmada</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #059669; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f0fdf4; padding: 30px; border-radius: 0 0 8px 8px; }
            .appointment-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #059669; }
            .highlight { background: #d1fae5; padding: 15px; border-radius: 6px; margin: 15px 0; }
            .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🎉 ¡Cita Confirmada!</h1>
            </div>
            <div class="content">
              <p>Hola <strong>${data.guestName}</strong>,</p>
              <p>¡Excelente noticia! Tu cita ha sido confirmada.</p>
              
              <div class="appointment-card">
                <h3>📅 Detalles de tu Cita</h3>
                <p><strong>Propiedad:</strong> ${data.propertyTitle}</p>
                <p><strong>Fecha:</strong> ${data.appointmentDate}</p>
                <p><strong>Hora:</strong> ${data.appointmentTime}</p>
                <p><strong>Modalidad:</strong> ${data.meetingType === 'in_person' ? 'Presencial' : data.meetingType === 'video_call' ? 'Videollamada' : 'Llamada telefónica'}</p>
                ${data.location ? `<p><strong>Ubicación:</strong> ${data.location}</p>` : ''}
                ${data.meetingUrl ? `<p><strong>Link de videollamada:</strong> <a href="${data.meetingUrl}">${data.meetingUrl}</a></p>` : ''}
              </div>

              <div class="highlight">
                <p><strong>📞 Información de Contacto</strong></p>
                <p><strong>Agente:</strong> ${data.ownerName}</p>
                ${data.ownerPhone ? `<p><strong>Teléfono:</strong> ${data.ownerPhone}</p>` : ''}
                <p><strong>Email:</strong> ${data.ownerEmail}</p>
              </div>

              <p><strong>📝 Recordatorio:</strong></p>
              <ul>
                <li>Llega puntual a tu cita</li>
                <li>Trae una identificación oficial</li>
                <li>Prepara tus preguntas sobre la propiedad</li>
                ${data.meetingType === 'in_person' ? '<li>Verifica la dirección antes de salir</li>' : ''}
                ${data.meetingType === 'video_call' ? '<li>Prueba tu conexión de internet</li>' : ''}
              </ul>

              ${data.response ? `<div style="background: #e0f2fe; padding: 15px; border-radius: 6px; margin: 15px 0;"><strong>Mensaje del agente:</strong><br/>${data.response}</div>` : ''}
            </div>
            <div class="footer">
              <p>¡Nos vemos pronto! - <strong>Inmova.gt</strong></p>
            </div>
          </div>
        </body>
      </html>
    `
  }),

  // 📧 NUEVO: Notificación de mensaje web recibido
  webMessageReceived: (data: any) => ({
    subject: `Nueva consulta sobre ${data.propertyTitle}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Nueva Consulta Web</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #7c3aed; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #faf5ff; padding: 30px; border-radius: 0 0 8px 8px; }
            .property-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #7c3aed; }
            .message-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .lead-badge { display: inline-block; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; }
            .inquiry { background: #dbeafe; color: #1e40af; }
            .viewing { background: #d1fae5; color: #059669; }
            .offer { background: #fef3c7; color: #d97706; }
            .negotiation { background: #fce7f3; color: #be185d; }
            .button { display: inline-block; background: #7c3aed; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; }
            .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>💬 Nueva Consulta Web</h1>
            </div>
            <div class="content">
              <p>¡Hola! Has recibido una nueva consulta desde inmo.gt</p>
              
              <div class="property-card">
                <h3>🏠 ${data.propertyTitle}</h3>
                <p><strong>Ubicación:</strong> ${data.propertyAddress}</p>
                <p><strong>Precio:</strong> ${data.propertyPrice}</p>
                <span class="lead-badge ${data.leadType}">${data.leadTypeName}</span>
              </div>

              <div class="message-card">
                <h3>👤 Información del Cliente</h3>
                <p><strong>Nombre:</strong> ${data.senderName}</p>
                <p><strong>Email:</strong> ${data.senderEmail}</p>
                ${data.senderPhone ? `<p><strong>Teléfono:</strong> ${data.senderPhone}</p>` : ''}
                <p><strong>Asunto:</strong> ${data.subject}</p>
                
                <div style="background: #f8fafc; padding: 15px; border-radius: 6px; margin: 15px 0;">
                  <strong>Mensaje:</strong><br/>
                  ${data.message}
                </div>
                
                <p><strong>Enviado:</strong> ${data.createdAt}</p>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.dashboardUrl}" class="button">Responder Consulta</a>
              </div>

              <p><strong>💡 Consejo:</strong> Responde rápidamente para aumentar tus posibilidades de cerrar la venta. Los clientes valoran la atención inmediata.</p>
            </div>
            <div class="footer">
              <p>Este email fue enviado por <strong>Inmo.gt</strong></p>
            </div>
          </div>
        </body>
      </html>
    `
  }),

  // 📧 NUEVO: Notificación de créditos agotados
  lowCreditsWarning: (data: any) => ({
    subject: `⚠️ Créditos agotados - Actualiza tu plan`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Créditos Agotados</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #dc2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #fef2f2; padding: 30px; border-radius: 0 0 8px 8px; }
            .alert-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #dc2626; }
            .upgrade-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #059669; }
            .button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 5px; }
            .button-green { background: #059669; }
            .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
            .features { list-style: none; padding: 0; }
            .features li { padding: 8px 0; }
            .features li:before { content: "✅ "; color: #059669; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>⚠️ Créditos Agotados</h1>
            </div>
            <div class="content">
              <p>Hola <strong>${data.userName}</strong>,</p>
              
              <div class="alert-card">
                <h3>🚨 Has agotado tus créditos</h3>
                <p><strong>Plan actual:</strong> ${data.currentPlan}</p>
                <p><strong>Créditos usados:</strong> ${data.creditsUsed} de ${data.totalCredits}</p>
                <p><strong>Créditos restantes:</strong> <span style="color: #dc2626; font-weight: bold;">0</span></p>
                
                <div style="background: #fee2e2; padding: 15px; border-radius: 6px; margin: 15px 0;">
                  <strong>⚠️ Limitaciones actuales:</strong>
                  <ul style="margin: 10px 0 0 20px;">
                    <li>No puedes recibir más consultas</li>
                    <li>No puedes destacar propiedades</li>
                    <li>Funciones premium deshabilitadas</li>
                  </ul>
                </div>
              </div>

              <div class="upgrade-card">
                <h3>🚀 Soluciones Disponibles</h3>
                
                <p><strong>Plan Pro - $19/mes</strong></p>
                <ul class="features">
                  <li>100 créditos mensuales</li>
                  <li>20 propiedades máximo</li>
                  <li>Soporte prioritario</li>
                </ul>
                
                <p><strong>Plan Premium - $49/mes</strong></p>
                <ul class="features">
                  <li>Créditos ilimitados</li>
                  <li>Propiedades ilimitadas</li>
                  <li>Analytics avanzados</li>
                  <li>Soporte VIP</li>
                </ul>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.upgradeUrl}" class="button button-green">Actualizar Plan Ahora</a>
                <a href="${data.dashboardUrl}" class="button">Ver Mi Cuenta</a>
              </div>

              <p><strong>💰 ¿No quieres actualizar?</strong> Puedes esperar hasta el próximo mes para que se renueven tus créditos, pero perderás oportunidades de negocio.</p>
            </div>
            <div class="footer">
              <p>¡No pierdas clientes! - <strong>Inmo.gt</strong></p>
            </div>
          </div>
        </body>
             </html>
     `
   }),

   // 📧 NUEVO: Notificación de posición destacada vencida
   featuredPositionExpired: (data: any) => ({
     subject: `Tu posición destacada ha vencido - ${data.propertyTitle}`,
     html: `
       <!DOCTYPE html>
       <html>
         <head>
           <meta charset="utf-8">
           <title>Posición Destacada Vencida</title>
           <style>
             body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
             .container { max-width: 600px; margin: 0 auto; padding: 20px; }
             .header { background: #f59e0b; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
             .content { background: #fffbeb; padding: 30px; border-radius: 0 0 8px 8px; }
             .property-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b; }
             .renewal-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #10b981; }
             .button { display: inline-block; background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 5px; }
             .button-green { background: #10b981; }
             .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
           </style>
         </head>
         <body>
           <div class="container">
             <div class="header">
               <h1>⏰ Posición Destacada Vencida</h1>
             </div>
             <div class="content">
               <p>Hola <strong>${data.userName}</strong>,</p>
               
               <div class="property-card">
                 <h3>🏠 ${data.propertyTitle}</h3>
                 <p><strong>Ubicación:</strong> ${data.propertyAddress}</p>
                 <p><strong>Precio:</strong> ${data.propertyPrice}</p>
                 <p><strong>Período destacado:</strong> ${data.featuredDuration} días</p>
                 <p><strong>Vencido el:</strong> ${data.expiredDate}</p>
               </div>

               <div style="background: #fef3c7; padding: 15px; border-radius: 6px; margin: 15px 0;">
                 <p><strong>⚠️ Tu propiedad ya no aparece en la sección destacada</strong></p>
                 <p>Esto significa menos visibilidad y posibles menos consultas.</p>
               </div>

               <div class="renewal-card">
                 <h3>🚀 ¿Quieres renovar la posición destacada?</h3>
                 <p><strong>Beneficios de destacar tu propiedad:</strong></p>
                 <ul>
                   <li>✅ Mayor visibilidad en la página principal</li>
                   <li>✅ Aparece en los primeros resultados</li>
                   <li>✅ Hasta 5x más consultas</li>
                   <li>✅ Vende más rápido</li>
                 </ul>
                 <p><strong>Costo:</strong> Solo 10 créditos por 7 días más</p>
               </div>

               <div style="text-align: center; margin: 30px 0;">
                 <a href="${data.renewUrl}" class="button button-green">Renovar Ahora</a>
                 <a href="${data.dashboardUrl}" class="button">Ver Mi Dashboard</a>
               </div>

               <p><strong>💡 Tip:</strong> Las propiedades destacadas reciben 5 veces más consultas que las normales.</p>
             </div>
             <div class="footer">
               <p>¡Mantén tu propiedad visible! - <strong>Inmo.gt</strong></p>
             </div>
           </div>
         </body>
       </html>
     `
   }),

   // 📧 NUEVO: Notificación de posición premium vencida
   premiumPositionExpired: (data: any) => ({
     subject: `Tu posición premium ha vencido - ${data.propertyTitle}`,
     html: `
       <!DOCTYPE html>
       <html>
         <head>
           <meta charset="utf-8">
           <title>Posición Premium Vencida</title>
           <style>
             body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
             .container { max-width: 600px; margin: 0 auto; padding: 20px; }
             .header { background: #dc2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
             .content { background: #fef2f2; padding: 30px; border-radius: 0 0 8px 8px; }
             .property-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626; }
             .premium-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #7c3aed; }
             .button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 5px; }
             .button-purple { background: #7c3aed; }
             .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
           </style>
         </head>
         <body>
           <div class="container">
             <div class="header">
               <h1>👑 Posición Premium Vencida</h1>
             </div>
             <div class="content">
               <p>Hola <strong>${data.userName}</strong>,</p>
               
               <div class="property-card">
                 <h3>🏠 ${data.propertyTitle}</h3>
                 <p><strong>Ubicación:</strong> ${data.propertyAddress}</p>
                 <p><strong>Precio:</strong> ${data.propertyPrice}</p>
                 <p><strong>Período premium:</strong> ${data.premiumDuration} días</p>
                 <p><strong>Vencido el:</strong> ${data.expiredDate}</p>
               </div>

               <div style="background: #fee2e2; padding: 15px; border-radius: 6px; margin: 15px 0;">
                 <p><strong>⚠️ Tu propiedad ya no está en la posición premium del home</strong></p>
                 <p>Perdiste la máxima visibilidad posible en la plataforma.</p>
               </div>

               <div class="premium-card">
                 <h3>👑 ¿Quieres renovar la posición premium?</h3>
                 <p><strong>Beneficios exclusivos de Premium Home:</strong></p>
                 <ul>
                   <li>🌟 Posición #1 en la página principal</li>
                   <li>🚀 Primera propiedad que ven todos los visitantes</li>
                   <li>📈 Hasta 10x más consultas</li>
                   <li>⚡ Venta ultra rápida garantizada</li>
                   <li>💎 Badge premium visible</li>
                 </ul>
                 <p><strong>Costo:</strong> Solo 25 créditos por 7 días más</p>
                 <p><em>Disponibilidad limitada: ${data.availableSlots ? 'Posición disponible' : 'Posición ocupada por otra propiedad'}</em></p>
               </div>

               <div style="text-align: center; margin: 30px 0;">
                 <a href="${data.renewUrl}" class="button button-purple">Renovar Premium</a>
                 <a href="${data.dashboardUrl}" class="button">Ver Dashboard</a>
               </div>

               <p><strong>🔥 ¡Acción limitada!</strong> Solo hay 1 posición premium disponible. Renueva ahora antes que la tome otra propiedad.</p>
             </div>
             <div class="footer">
               <p>¡Mantén tu posición #1! - <strong>Inmo.gt</strong></p>
             </div>
           </div>
         </body>
       </html>
     `
   })
};

// Función para enviar email usando Resend
async function sendEmail(to: string, template: any) {
  if (!RESEND_API_KEY) {
    console.error("RESEND_API_KEY no está configurada");
    return { success: false, error: "Email service not configured" };
  }

  try {
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'Inmova.gt <<EMAIL>>',
        to,
        subject: template.subject,
        html: template.html,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      console.error("Error enviando email:", error);
      return { success: false, error };
    }

    const result = await response.json();
    return { success: true, id: result.id };
  } catch (error) {
    console.error("Error en sendEmail:", error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

// Action para notificar nueva solicitud de cita
export const notifyAppointmentRequest = action({
  args: {
    ownerEmail: v.string(),
    ownerName: v.string(),
    guestEmail: v.string(),
    guestName: v.string(),
    guestPhone: v.optional(v.string()),
    propertyTitle: v.string(),
    propertyAddress: v.string(),
    propertyPrice: v.string(),
    requestedStartTime: v.string(),
    requestedEndTime: v.string(),
    meetingType: v.string(),
    message: v.optional(v.string()),
    dashboardUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const requestedDate = new Date(args.requestedStartTime).toLocaleDateString('es-ES', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const requestedTime = new Date(args.requestedStartTime).toLocaleTimeString('es-ES', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });

    // Notificar al propietario
    const ownerTemplate = EMAIL_TEMPLATES.appointmentRequestOwner({
      ...args,
      requestedDate,
      requestedTime,
    });

    // Notificar al cliente
    const guestTemplate = EMAIL_TEMPLATES.appointmentRequestGuest({
      ...args,
      requestedDate,
      requestedTime,
    });

    const results = await Promise.allSettled([
      sendEmail(args.ownerEmail, ownerTemplate),
      sendEmail(args.guestEmail, guestTemplate),
    ]);

    return {
      ownerNotification: results[0],
      guestNotification: results[1],
    };
  },
});

// Action para notificar cita confirmada
export const notifyAppointmentConfirmed = action({
  args: {
    guestEmail: v.string(),
    guestName: v.string(),
    ownerName: v.string(),
    ownerEmail: v.string(),
    ownerPhone: v.optional(v.string()),
    propertyTitle: v.string(),
    appointmentStartTime: v.string(),
    appointmentEndTime: v.string(),
    meetingType: v.string(),
    location: v.optional(v.string()),
    meetingUrl: v.optional(v.string()),
    response: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const appointmentDate = new Date(args.appointmentStartTime).toLocaleDateString('es-ES', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const appointmentTime = new Date(args.appointmentStartTime).toLocaleTimeString('es-ES', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });

    const template = EMAIL_TEMPLATES.appointmentConfirmed({
      ...args,
      appointmentDate,
      appointmentTime,
    });

    const result = await sendEmail(args.guestEmail, template);
    return result;
  },
});

// 📧 NUEVA: Action para notificar mensaje web recibido
export const notifyWebMessageReceived = action({
  args: {
    ownerEmail: v.string(),
    ownerName: v.string(),
    senderName: v.string(),
    senderEmail: v.string(),
    senderPhone: v.optional(v.string()),
    propertyTitle: v.string(),
    propertyAddress: v.string(),
    propertyPrice: v.string(),
    subject: v.string(),
    message: v.string(),
    leadType: v.string(),
    createdAt: v.string(),
    dashboardUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const leadTypeNames = {
      inquiry: "Consulta General",
      viewing: "Solicitud de Visita", 
      offer: "Oferta de Compra",
      negotiation: "Negociación"
    };

    const template = EMAIL_TEMPLATES.webMessageReceived({
      ...args,
      leadTypeName: leadTypeNames[args.leadType as keyof typeof leadTypeNames] || "Consulta",
      createdAt: new Date(args.createdAt).toLocaleDateString('es-ES', {
        weekday: 'long',
        year: 'numeric',
        month: 'long', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }),
    });

    const result = await sendEmail(args.ownerEmail, template);
    return result;
  },
});

// 📧 NUEVA: Action para notificar créditos agotados  
export const notifyLowCredits = action({
  args: {
    userEmail: v.string(),
    userName: v.string(),
    currentPlan: v.string(),
    creditsUsed: v.number(),
    totalCredits: v.number(),
    dashboardUrl: v.string(),
    upgradeUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const template = EMAIL_TEMPLATES.lowCreditsWarning({
      ...args,
      currentPlan: args.currentPlan === 'free' ? 'Plan Gratuito' : 
                   args.currentPlan === 'pro' ? 'Plan Pro' : 'Plan Premium'
    });

    const result = await sendEmail(args.userEmail, template);
    return result;
  },
});

// 📧 NUEVA: Action para notificar posición destacada vencida
export const notifyFeaturedExpired = action({
  args: {
    userEmail: v.string(),
    userName: v.string(),
    propertyTitle: v.string(),
    propertyAddress: v.string(),
    propertyPrice: v.string(),
    featuredDuration: v.number(),
    expiredDate: v.string(),
    renewUrl: v.string(),
    dashboardUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const template = EMAIL_TEMPLATES.featuredPositionExpired({
      ...args,
      expiredDate: new Date(args.expiredDate).toLocaleDateString('es-ES', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
    });

    const result = await sendEmail(args.userEmail, template);
    return result;
  },
});

// 📧 NUEVA: Action para notificar posición premium vencida
export const notifyPremiumExpired = action({
  args: {
    userEmail: v.string(),
    userName: v.string(),
    propertyTitle: v.string(),
    propertyAddress: v.string(),
    propertyPrice: v.string(),
    premiumDuration: v.number(),
    expiredDate: v.string(),
    availableSlots: v.number(),
    renewUrl: v.string(),
    dashboardUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const template = EMAIL_TEMPLATES.premiumPositionExpired({
      ...args,
      expiredDate: new Date(args.expiredDate).toLocaleDateString('es-ES', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
    });

    const result = await sendEmail(args.userEmail, template);
    return result;
  },
}); 