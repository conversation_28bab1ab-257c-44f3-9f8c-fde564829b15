# Configuración de Seguridad para Endpoints

## 🔐 API Key Authentication

### 1. Configurar API Key en Convex

```bash
# En el dashboard de Convex, ir a Settings > Environment Variables
CONVEX_API_KEY=tu-super-secreto-api-key-aqui-2025
```

### 2. Uso en n8n

Para todos los requests a los endpoints protegidos, agregar header:

```json
{
  "headers": {
    "Content-Type": "application/json",
    "x-api-key": "tu-super-secreto-api-key-aqui-2025"
  }
}
```

### 3. Endpoints Protegidos

- `POST /saveConversation` 
- `POST /generateWeeklyReport`

### 4. Configuración en n8n para Save Conversation

```json
{
  "name": "Save to Convex",
  "type": "n8n-nodes-base.httpRequest",
  "parameters": {
    "method": "POST",
    "url": "https://capable-cod-213.convex.site/saveConversation",
    "sendHeaders": true,
    "headerParameters": {
      "parameters": [
        {
          "name": "Content-Type",
          "value": "application/json"
        },
        {
          "name": "x-api-key",
          "value": "tu-super-secreto-api-key-aqui-2025"
        }
      ]
    },
    "sendBody": true,
    "specifyBody": "json",
    "jsonBody": {
      "chatId": "{{ $('Edit Fields').item.json.message.chatId }}",
      "userName": "{{ $('Edit Fields').item.json.userName }}",
      "messageId": "{{ $('Edit Fields').item.json.message.messageId }}",
      "messageType": "user",
      "content": "{{ $('Edit Fields').item.json.message.messageContent }}",
      "timestamp": "{{ $('Edit Fields').item.json.message.messageTimeStamp }}"
    }
  }
}
```

### 5. Test de Autenticación

```bash
# Con API Key válida (debería funcionar)
curl -X POST https://capable-cod-213.convex.site/saveConversation \
  -H "Content-Type: application/json" \
  -H "x-api-key: tu-super-secreto-api-key-aqui-2025" \
  -d '{"chatId":"test","userName":"Test","messageId":"123","messageType":"user","content":"test"}'

# Sin API Key (debería retornar 401)
curl -X POST https://capable-cod-213.convex.site/saveConversation \
  -H "Content-Type: application/json" \
  -d '{"chatId":"test","userName":"Test","messageId":"123","messageType":"user","content":"test"}'
```

## 🛡️ Consideraciones de Seguridad

### Nivel Actual: BÁSICO ✅
- ✅ API Key validation
- ✅ Header authentication
- ✅ 401 responses

### Nivel AVANZADO (futuro):
- 🔄 JWT tokens con expiración
- 🔄 Rate limiting 
- 🔄 IP whitelisting
- 🔄 Audit logging
- 🔄 Role-based access

### API Key Management:
- 🔒 **No hardcodear** en código
- 🔒 Usar **variables de entorno**
- 🔒 **Rotar regularmente**
- 🔒 Diferente para **dev/prod**

## 🚨 IMPORTANTE

**¡Cambiar el API Key por defecto antes de producción!**

Generar key segura:
```bash
# Linux/Mac
openssl rand -hex 32

# O usar generador online
# https://www.uuidgenerator.net/api/version4
``` 