# Integración de Agenda con N8N

Este documento explica cómo integrar el sistema de agenda de tu aplicación inmobiliaria con N8N para que el asistente IA pueda coordinar citas automáticamente.

## Funciones Disponibles en Convex

### 1. `checkPropertyAvailability`

Verifica la disponibilidad de horarios para una propiedad específica.

**Parámetros:**
```json
{
  "propertyId": "kd7...", // ID de la propiedad
  "preferredDates": ["2024-01-15", "2024-01-16"], // Fechas preferidas (YYYY-MM-DD)
  "duration": 60 // Duración en minutos (opcional, default: 60)
}
```

**Respuesta:**
```json
{
  "hasAvailability": true,
  "property": {
    "title": "Casa en Las Condes",
    "address": "Av. Las Condes 1234",
    "city": "Santiago"
  },
  "availableSlots": [
    {
      "date": "2024-01-15",
      "startTime": "2024-01-15T10:00:00.000Z",
      "endTime": "2024-01-15T11:00:00.000Z",
      "duration": 60,
      "dayName": "lunes",
      "timeSlot": "10:00"
    }
  ],
  "message": "Encontré 3 horarios disponibles para visitar esta propiedad."
}
```

### 2. `createAIAppointmentRequest`

Crea una solicitud de cita que será enviada al propietario para aprobación.

**Parámetros:**
```json
{
  "propertyId": "kd7...",
  "guestName": "Juan Pérez",
  "guestEmail": "<EMAIL>",
  "guestPhone": "+56912345678",
  "requestedStartTime": "2024-01-15T10:00:00.000Z",
  "requestedEndTime": "2024-01-15T11:00:00.000Z",
  "message": "Me interesa mucho esta propiedad",
  "type": "property_viewing",
  "meetingType": "in_person",
  "source": "ai_assistant"
}
```

**Tipos de cita válidos:**
- `property_viewing` - Visita a la propiedad
- `consultation` - Consulta
- `negotiation` - Negociación
- `document_signing` - Firma de documentos
- `other` - Otro

**Tipos de reunión válidos:**
- `in_person` - Presencial
- `video_call` - Videollamada
- `phone_call` - Llamada telefónica

**Respuesta:**
```json
{
  "success": true,
  "requestId": "kd9...",
  "message": "Solicitud de cita creada exitosamente. El propietario será notificado.",
  "expiresAt": "2024-01-22T10:00:00.000Z"
}
```

### 3. `getPropertyContextForAI`

Obtiene información completa de una propiedad para que el asistente tenga contexto.

**Parámetros:**
```json
{
  "propertyId": "kd7..."
}
```

**Respuesta:**
```json
{
  "property": {
    "id": "kd7...",
    "title": "Casa en Las Condes",
    "address": "Av. Las Condes 1234",
    "city": "Santiago",
    "type": "house",
    "price": *********,
    "currency": "CLP",
    "bedrooms": 3,
    "bathrooms": 2,
    "area": 120,
    "amenities": ["Wi-Fi", "Parking", "Jardín"]
  },
  "owner": {
    "name": "María González",
    "role": "agent",
    "company": "Inmobiliaria ABC",
    "email": "<EMAIL>",
    "phone": "+56987654321"
  },
  "availability": {
    "configured": true,
    "schedule": [
      {
        "dayOfWeek": 1,
        "startTime": "09:00",
        "endTime": "18:00",
        "slotDuration": 60
      }
    ]
  },
  "activity": {
    "pendingRequests": 2,
    "upcomingAppointments": 1,
    "lastRequestDate": "2024-01-10T15:30:00.000Z",
    "nextAppointmentDate": "2024-01-16T14:00:00.000Z"
  }
}
```

## Configuración de N8N

### 1. Nodo HTTP Request para Convex

Configura un nodo HTTP Request con:

- **URL:** `https://your-deployment.convex.cloud/api/query` (para queries) o `/api/mutation` (para mutations)
- **Method:** POST
- **Headers:**
  ```json
  {
    "Content-Type": "application/json",
    "Authorization": "Bearer YOUR_API_KEY"
  }
  ```

### 2. Ejemplo de Workflow de N8N

```json
{
  "path": "api:aiAppointments:checkPropertyAvailability",
  "args": {
    "propertyId": "{{ $json.propertyId }}",
    "preferredDates": ["{{ $json.date1 }}", "{{ $json.date2 }}"]
  }
}
```

### 3. Flujo Completo de Coordinación de Citas

1. **Recibir consulta del usuario** (vía WhatsApp/Chat)
2. **Extraer información** (propiedad de interés, fechas preferidas)
3. **Verificar disponibilidad** (`checkPropertyAvailability`)
4. **Presentar opciones al usuario**
5. **Crear solicitud de cita** (`createAIAppointmentRequest`)
6. **Notificar al propietario** (email/WhatsApp)
7. **Confirmar con el usuario**

### 4. Configuración de URLs

- **Deployment URL:** Encuentra tu URL en el dashboard de Convex
- **Queries:** `https://your-deployment.convex.cloud/api/query`
- **Mutations:** `https://your-deployment.convex.cloud/api/mutation`

### 5. Autenticación

Para usar estas funciones desde N8N, necesitas:

1. Crear una API Key en Convex
2. Configurar la autenticación en N8N
3. Usar el token en los headers de las requests

## Ejemplos de Uso en N8N

### Verificar Disponibilidad

```javascript
// En un nodo Code de N8N
const propertyId = $input.first().json.propertyId;
const preferredDates = ['2024-01-15', '2024-01-16'];

return {
  path: 'api:aiAppointments:checkPropertyAvailability',
  args: {
    propertyId,
    preferredDates,
    duration: 60
  }
};
```

### Crear Solicitud de Cita

```javascript
// En un nodo Code de N8N
return {
  path: 'api:aiAppointments:createAIAppointmentRequest',
  args: {
    propertyId: $json.propertyId,
    guestName: $json.clientName,
    guestEmail: $json.clientEmail,
    guestPhone: $json.clientPhone,
    requestedStartTime: $json.selectedSlot.startTime,
    requestedEndTime: $json.selectedSlot.endTime,
    message: `Solicitud creada por asistente IA para ${$json.clientName}`,
    type: 'property_viewing',
    meetingType: 'in_person',
    source: 'ai_assistant'
  }
};
```

## Próximos Pasos

1. **Configurar webhooks** para notificar cambios de estado de citas
2. **Implementar recordatorios automáticos**
3. **Crear flujos de seguimiento post-cita**
4. **Integrar con calendarios externos** (Google Calendar, Outlook)

## Soporte

Para más información sobre la implementación o dudas específicas, revisa:
- Documentación de Convex: https://docs.convex.dev
- Documentación de N8N: https://docs.n8n.io 