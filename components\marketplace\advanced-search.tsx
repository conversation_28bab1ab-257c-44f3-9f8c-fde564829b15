"use client";

import { useState } from "react";
import { useQuery, useAction } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Search, Filter, ChevronDown, ChevronUp, X, Sparkles } from "lucide-react";
import { PropertyFilters } from "@/types/marketplace";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface AdvancedSearchProps {
  onSearch?: (filters: PropertyFilters & { searchTerm?: string }) => void;
  onAISearch?: (results: any[]) => void;
  showAdvancedByDefault?: boolean;
  variant?: "home" | "page";
}

export function AdvancedSearch({ 
  onSearch,
  onAISearch, 
  showAdvancedByDefault = false, 
  variant = "page" 
}: AdvancedSearchProps) {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<PropertyFilters>({});
  const [showAdvanced, setShowAdvanced] = useState(showAdvancedByDefault);
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [isAISearching, setIsAISearching] = useState(false);

  const amenitiesGrouped = useQuery(api.amenities.getAmenitiesGroupedByCategory);
  const searchWithAI = useAction(api.properties.intelligentSearch);

  const handleFilterChange = (key: keyof PropertyFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleAmenityToggle = (amenityId: string) => {
    const newAmenities = selectedAmenities.includes(amenityId)
      ? selectedAmenities.filter(id => id !== amenityId)
      : [...selectedAmenities, amenityId];
    
    setSelectedAmenities(newAmenities);
    handleFilterChange('amenities', newAmenities);
  };

  const handleSearch = () => {
    const searchData = {
      ...filters,
      searchTerm: searchTerm || undefined,
      amenities: selectedAmenities.length > 0 ? selectedAmenities : undefined
    };

    if (onSearch) {
      onSearch(searchData);
    } else {
      // Navegar a la página de propiedades con filtros
      const params = new URLSearchParams();
      if (searchTerm) params.set('search', searchTerm);
      if (filters.type?.[0]) params.set('type', filters.type[0]);
      if (filters.status?.[0]) params.set('status', filters.status[0]);
      if (filters.city) params.set('city', filters.city);
      if (filters.minPrice) params.set('minPrice', filters.minPrice.toString());
      if (filters.maxPrice) params.set('maxPrice', filters.maxPrice.toString());
      if (selectedAmenities.length > 0) params.set('amenities', selectedAmenities.join(','));
      
      router.push(`/properties?${params.toString()}`);
    }
  };

  const handleAISearch = async () => {
    if (!searchTerm.trim()) {
      toast.error("Escribe algo para buscar, ej: 'apartamento zona 14 con piscina'");
      return;
    }

    setIsAISearching(true);
    
    try {
      const results = await searchWithAI({
        searchText: searchTerm.trim()
      });

      toast.success(`🤖 IA encontró ${results.properties?.length || 0} propiedades`);

      if (onAISearch) {
        onAISearch(results.properties || []);
      } else {
        // Navegar a resultados con IA
        const params = new URLSearchParams();
        params.set('ai-search', searchTerm);
        router.push(`/properties?${params.toString()}`);
      }
      
    } catch (error) {
      console.error("Error en búsqueda IA:", error);
      toast.error("Error en la búsqueda inteligente. Intenta con búsqueda normal.");
    } finally {
      setIsAISearching(false);
    }
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm("");
    setSelectedAmenities([]);
  };

  const activeFiltersCount = Object.values(filters).filter(Boolean).length + 
    (selectedAmenities.length > 0 ? 1 : 0);

  const isHomeVariant = variant === "home";

  return (
    <div className={`${isHomeVariant ? 'bg-white rounded-3xl shadow-2xl border border-white/20' : 'bg-white rounded-2xl shadow-lg'} p-4 md:p-6`}>
      <div className="space-y-4 md:space-y-6">
        {/* Buscador Principal */}
        <div className="space-y-4">
          {/* Primera fila: Campo de búsqueda y filtros básicos */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-12 md:gap-4">
            <div className="md:col-span-6">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  placeholder="Ej: 'apartamento zona 14 con piscina' o 'casa barata Guatemala'..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`pl-12 ${isHomeVariant ? 'h-12 md:h-14' : 'h-12'} text-gray-900 border-0 bg-gray-50 rounded-xl focus:ring-2 focus:ring-blue-500`}
                />
              </div>
            </div>
            
            <div className="md:col-span-2">
              <Select onValueChange={(value) => handleFilterChange('type', [value])}>
                <SelectTrigger className={`${isHomeVariant ? 'h-12 md:h-14' : 'h-12'} text-gray-900 border-0 bg-gray-50 rounded-xl focus:ring-2 focus:ring-blue-500`}>
                  <SelectValue placeholder="Tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="house">Casa</SelectItem>
                  <SelectItem value="apartment">Apartamento</SelectItem>
                  <SelectItem value="office">Oficina</SelectItem>
                  <SelectItem value="land">Terreno</SelectItem>
                  <SelectItem value="commercial">Comercial</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="md:col-span-2">
              <Select onValueChange={(value) => handleFilterChange('status', [value])}>
                <SelectTrigger className={`${isHomeVariant ? 'h-12 md:h-14' : 'h-12'} text-gray-900 border-0 bg-gray-50 rounded-xl focus:ring-2 focus:ring-blue-500`}>
                  <SelectValue placeholder="Estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="for_sale">En Venta</SelectItem>
                  <SelectItem value="for_rent">En Alquiler</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="md:col-span-2">
              <Button
                variant="outline"
                onClick={() => setShowAdvanced(!showAdvanced)}
                className={`w-full ${isHomeVariant ? 'h-12 md:h-14' : 'h-12'} border-2 border-gray-300 bg-white text-gray-700 hover:border-blue-500 hover:bg-blue-50 hover:text-blue-600 rounded-xl shadow-sm`}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filtros
                {showAdvanced ? <ChevronUp className="ml-2 h-4 w-4" /> : <ChevronDown className="ml-2 h-4 w-4" />}
                {activeFiltersCount > 0 && (
                  <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-700">
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
            </div>
          </div>

          {/* Segunda fila: Botones de búsqueda */}
          <div className="space-y-3">
            <div className="flex gap-3">
              <Button 
                onClick={handleSearch}
                size="lg" 
                className={`flex-1 ${isHomeVariant ? 'h-12 md:h-14' : 'h-12'} bg-blue-600 hover:bg-blue-700 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300`}
              >
                <Search className="mr-2 h-5 w-5" />
                Búsqueda Normal
              </Button>

              <Button 
                onClick={handleAISearch}
                disabled={isAISearching}
                size="lg" 
                className={`flex-1 ${isHomeVariant ? 'h-12 md:h-14' : 'h-12'} bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 text-white`}
              >
                {isAISearching ? (
                  <>
                    <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full mr-2" />
                    Analizando...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-5 w-5" />
                    Búsqueda Inteligente
                  </>
                )}
              </Button>
            </div>
            
            {/* Texto explicativo */}
            <div className="text-center text-sm text-gray-500">
              <span className="hidden md:inline">💡 Prueba la búsqueda inteligente: describe lo que buscas en lenguaje natural</span>
              <span className="md:hidden">💡 Usa IA para buscar en lenguaje natural</span>
            </div>
          </div>
        </div>

        {/* Filtros Avanzados */}
        {showAdvanced && (
          <div className="bg-gray-50 p-4 md:p-6 rounded-xl border border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-gray-900">Filtros Avanzados</h3>
              {activeFiltersCount > 0 && (
                <Button 
                  variant="ghost" 
                  onClick={clearFilters}
                  className="text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-xl"
                >
                  <X className="h-4 w-4 mr-1" />
                  Limpiar
                </Button>
              )}
            </div>
            
            <div className="space-y-6">
              {/* Filtros de Ubicación y Precio */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2 text-gray-700">Ciudad</label>
                  <Input
                    placeholder="Ej: Guatemala, Antigua..."
                    value={filters.city || ""}
                    onChange={(e) => handleFilterChange('city', e.target.value)}
                    className="border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2 text-gray-700">Precio mínimo (GTQ)</label>
                  <Input
                    type="number"
                    placeholder="0"
                    value={filters.minPrice || ""}
                    onChange={(e) => handleFilterChange('minPrice', Number(e.target.value))}
                    className="border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2 text-gray-700">Precio máximo (GTQ)</label>
                  <Input
                    type="number"
                    placeholder="Sin límite"
                    value={filters.maxPrice || ""}
                    onChange={(e) => handleFilterChange('maxPrice', Number(e.target.value))}
                    className="border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Amenidades */}
              {amenitiesGrouped && Object.keys(amenitiesGrouped).length > 0 && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-4">Amenidades</h4>
                  <div className="space-y-4">
                    {Object.entries(amenitiesGrouped).map(([category, amenities]) => (
                      <div key={`amenity-category-${category}`} className="space-y-2">
                        <h5 className="text-sm font-medium text-gray-700 capitalize">
                          {category === 'servicios' ? 'Servicios' :
                           category === 'comodidades' ? 'Comodidades' :
                           category === 'ubicacion' ? 'Ubicación' :
                           category === 'vistas' ? 'Vistas' : category}
                        </h5>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                          {amenities && Array.isArray(amenities) && amenities.map((amenity) => (
                            <button
                              key={`amenity-${amenity._id}`}
                              onClick={() => handleAmenityToggle(amenity._id)}
                              className={`text-left p-2 rounded-lg border-2 transition-all duration-200 ${
                                selectedAmenities.includes(amenity._id)
                                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                                  : 'border-gray-200 hover:border-gray-300 text-gray-700'
                              }`}
                            >
                              <div className="text-sm font-medium">{amenity.name}</div>
                              {amenity.description && (
                                <div className="text-xs text-gray-500 mt-1">{amenity.description}</div>
                              )}
                            </button>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 