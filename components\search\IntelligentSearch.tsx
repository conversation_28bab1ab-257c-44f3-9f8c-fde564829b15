"use client";

import { useState } from "react";
import { <PERSON>, Sparkles, Filter } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";

interface IntelligentSearchProps {
  onResults: (properties: any[]) => void;
  onCriteriaExtracted?: (criteria: any) => void;
}

export function IntelligentSearch({ onResults, onCriteriaExtracted }: IntelligentSearchProps) {
  const [searchText, setSearchText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [extractedCriteria, setExtractedCriteria] = useState<any>(null);

  const handleIntelligentSearch = async () => {
    if (!searchText.trim()) return;
    
    setIsLoading(true);
    try {
      // Llamar al endpoint de búsqueda inteligente
      const response = await fetch("/api/intelligent-search", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ query: searchText })
      });
      
      const data = await response.json();
      
      if (data.success) {
        setExtractedCriteria(data.criteria);
        onResults(data.properties);
        onCriteriaExtracted?.(data.criteria);
      }
    } catch (error) {
      console.error("Error en búsqueda inteligente:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearCriteria = () => {
    setExtractedCriteria(null);
    setSearchText("");
    onResults([]);
  };

  return (
    <div className="space-y-4">
      {/* Barra de búsqueda principal */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <Input
          type="text"
          placeholder="Ej: Apartamento moderno con piscina en zona 14, hasta 400K, 2 habitaciones..."
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleIntelligentSearch()}
          className="pl-10 pr-24 py-3 text-base"
        />
        <div className="absolute inset-y-0 right-0 flex items-center pr-2">
          <Button
            onClick={handleIntelligentSearch}
            disabled={isLoading || !searchText.trim()}
            className="h-8 px-3"
          >
            {isLoading ? (
              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-1" />
                Buscar
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Criterios extraídos */}
      {extractedCriteria && Object.keys(extractedCriteria).length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <Filter className="h-4 w-4 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-blue-800">
                Criterios detectados automáticamente:
              </span>
            </div>
            <Button
              onClick={clearCriteria}
              variant="ghost"
              size="sm"
              className="text-blue-600 hover:text-blue-800"
            >
              Limpiar
            </Button>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {extractedCriteria.type && (
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                Tipo: {extractedCriteria.type === 'apartment' ? 'Apartamento' : 
                       extractedCriteria.type === 'house' ? 'Casa' : extractedCriteria.type}
              </Badge>
            )}
            
            {extractedCriteria.maxPrice && (
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                Hasta: ${extractedCriteria.maxPrice.toLocaleString()}
              </Badge>
            )}
            
            {extractedCriteria.bedrooms && (
              <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                {extractedCriteria.bedrooms} habitaciones
              </Badge>
            )}
            
            {extractedCriteria.bathrooms && (
              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                {extractedCriteria.bathrooms} baños
              </Badge>
            )}
            
            {extractedCriteria.zones && extractedCriteria.zones.length > 0 && (
              <Badge variant="secondary" className="bg-indigo-100 text-indigo-800">
                Zonas: {extractedCriteria.zones.join(", ")}
              </Badge>
            )}
            
            {extractedCriteria.amenities && extractedCriteria.amenities.length > 0 && (
              extractedCriteria.amenities.map((amenity: string, index: number) => (
                <Badge key={`amenity-${amenity}-${index}`} variant="secondary" className="bg-teal-100 text-teal-800">
                  {amenity}
                </Badge>
              ))
            )}
            
            {extractedCriteria.keywords && extractedCriteria.keywords.length > 0 && (
              extractedCriteria.keywords.map((keyword: string, index: number) => (
                <Badge key={`keyword-${keyword}-${index}`} variant="outline" className="border-gray-300">
                  {keyword}
                </Badge>
              ))
            )}
          </div>
        </div>
      )}

      {/* Ejemplos de búsqueda */}
      <div className="text-sm text-gray-600">
        <p className="mb-2 font-medium">Ejemplos de búsqueda:</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
          <button
            onClick={() => setSearchText("Apartamento moderno con piscina en zona 14, hasta 400K")}
            className="text-left p-2 bg-gray-50 rounded hover:bg-gray-100 transition-colors"
          >
            &ldquo;Apartamento moderno con piscina en zona 14, hasta 400K&rdquo;
          </button>
          <button
            onClick={() => setSearchText("Casa con jardín, 3 habitaciones, cerca del metro")}
            className="text-left p-2 bg-gray-50 rounded hover:bg-gray-100 transition-colors"
          >
            &ldquo;Casa con jardín, 3 habitaciones, cerca del metro&rdquo;
          </button>
          <button
            onClick={() => setSearchText("Depto amueblado para alquiler, 2 baños, zona 10")}
            className="text-left p-2 bg-gray-50 rounded hover:bg-gray-100 transition-colors"
          >
            &ldquo;Depto amueblado para alquiler, 2 baños, zona 10&rdquo;
          </button>
          <button
            onClick={() => setSearchText("Oficina premium en centro comercial, parking incluido")}
            className="text-left p-2 bg-gray-50 rounded hover:bg-gray-100 transition-colors"
          >
            &ldquo;Oficina premium en centro comercial, parking incluido&rdquo;
          </button>
        </div>
      </div>
    </div>
  );
} 