import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { api } from "./_generated/api";

// Destacar una propiedad (consume créditos)
export const featureProperty = mutation({
  args: {
    propertyId: v.id("properties"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Verificar que la propiedad existe y pertenece al usuario
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    if (property.ownerId !== identity.subject) {
      throw new Error("No autorizado: no eres el propietario");
    }

    // Verificar si ya está destacada
    if (property.featured && property.featuredUntil && property.featuredUntil > Date.now()) {
      throw new Error("La propiedad ya está destacada");
    }

    // 🚨 NUEVO: Verificar límite de propiedades destacadas (máximo 9 en total)
    const currentFeaturedProperties = await ctx.db
      .query("properties")
      .filter((q) => 
        q.and(
          q.eq(q.field("featured"), true),
          q.gt(q.field("featuredUntil"), Date.now())
        )
      )
      .collect();

    const FEATURED_LIMIT = 9;
    if (currentFeaturedProperties.length >= FEATURED_LIMIT) {
      throw new Error(`Límite alcanzado: Solo se permiten ${FEATURED_LIMIT} propiedades destacadas simultáneamente. Actualmente hay ${currentFeaturedProperties.length} destacadas.`);
    }

    // Obtener configuración de créditos para destacar
    const actionConfig = await ctx.db
      .query("creditActions")
      .withIndex("by_action", (q) => q.eq("action", "featured_property"))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (!actionConfig) {
      throw new Error("Configuración de destacadas no encontrada");
    }

    // Verificar o crear suscripción
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    // Si no existe suscripción, crear una gratuita
    if (!subscription) {
      const now = Date.now();
      const subscriptionId = await ctx.db.insert("subscriptions", {
        userId: identity.subject,
        plan: "free",
        status: "active",
        credits: 10,
        creditsUsed: 0,
        maxProperties: 5,
        propertiesCount: 0,
        createdAt: now,
        updatedAt: now,
      });
      
      // Obtener la suscripción recién creada
      subscription = await ctx.db.get(subscriptionId);
      if (!subscription) {
        throw new Error("Error al crear suscripción inicial");
      }
    }

    const availableCredits = subscription.credits - subscription.creditsUsed;
    if (availableCredits < actionConfig.cost) {
      throw new Error(`Créditos insuficientes. Necesitas ${actionConfig.cost}, tienes ${availableCredits}`);
    }

    const now = Date.now();
    const duration = actionConfig.duration || 30; // 30 días por defecto
    const expiresAt = now + (duration * 24 * 60 * 60 * 1000);

    // Crear transacción
    const transactionId = await ctx.db.insert("transactions", {
      userId: identity.subject,
      action: "featured_property",
      creditsCost: actionConfig.cost,
      description: `Destacar "${property.title}" por ${duration} días`,
      propertyId: args.propertyId,
      duration,
      expiresAt,
      status: "active",
      createdAt: now,
    });

    // Actualizar suscripción (consumir créditos)
    await ctx.db.patch(subscription._id, {
      creditsUsed: subscription.creditsUsed + actionConfig.cost,
      updatedAt: now,
    });

    // Actualizar propiedad
    await ctx.db.patch(args.propertyId, {
      featured: true,
      featuredUntil: expiresAt,
      featuredTransaction: transactionId,
    });

    // 🚨 VERIFICAR CRÉDITOS RESTANTES Y NOTIFICAR SI ESTÁN BAJOS
    try {
      const updatedSubscription = await ctx.db.get(subscription._id);
      if (updatedSubscription) {
        const remainingCredits = updatedSubscription.credits - updatedSubscription.creditsUsed;
        
        // Notificar cuando los créditos se agotan completamente
        if (remainingCredits <= 0) {
          const user = await ctx.db
            .query("users")
            .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
            .first();

          if (user && user.email) {
            ctx.scheduler.runAfter(0, api.emails.notifyLowCredits, {
              userEmail: user.email,
              userName: user.name || "Usuario",
              currentPlan: updatedSubscription.plan,
              creditsUsed: updatedSubscription.creditsUsed,
              totalCredits: updatedSubscription.credits,
              dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/finance`,
              upgradeUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/finance`,
            }).catch((error) => {
              console.error("Error enviando notificación de créditos agotados:", error);
            });
          }
        }
      }
    } catch (error) {
      console.error("Error verificando créditos restantes:", error);
    }

    return {
      success: true,
      transactionId,
      expiresAt,
      creditsCost: actionConfig.cost,
      remainingCredits: availableCredits - actionConfig.cost,
    };
  },
});

// Quitar destacado manualmente (cancela transacción)
export const unfeatureProperty = mutation({
  args: {
    propertyId: v.id("properties"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    if (property.ownerId !== identity.subject) {
      throw new Error("No autorizado");
    }

    if (!property.featured) {
      throw new Error("La propiedad no está destacada");
    }

    // Buscar transacción activa
    if (property.featuredTransaction) {
      const transaction = await ctx.db.get(property.featuredTransaction);
      if (transaction && transaction.status === "active") {
        // Marcar transacción como cancelada
        await ctx.db.patch(property.featuredTransaction, {
          status: "cancelled",
          updatedAt: Date.now(),
        });
      }
    }

    // Desactivar destacado
    await ctx.db.patch(args.propertyId, {
      featured: false,
      featuredUntil: undefined,
      featuredTransaction: undefined,
    });

    return { success: true };
  },
});

// Promover propiedad a Premium Home (posición VIP)
export const promoteToHomePremium = mutation({
  args: {
    propertyId: v.id("properties"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Verificar propiedad
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    if (property.ownerId !== identity.subject) {
      throw new Error("No autorizado");
    }

    // Verificar si ya está en premium
    if (property.premiumHomeUntil && property.premiumHomeUntil > Date.now()) {
      throw new Error("La propiedad ya está en posición premium");
    }

    // 🚨 NUEVO: Verificar límite de Premium Home (solo 1 posición)
    const existingPremium = await ctx.db
      .query("properties")
      .filter((q) => 
        q.gt(q.field("premiumHomeUntil"), Date.now())
      )
      .first();

    if (existingPremium) {
      throw new Error("Ya hay una propiedad en posición Premium Home. Solo se permite una a la vez.");
    }

    // Obtener configuración
    const actionConfig = await ctx.db
      .query("creditActions")
      .withIndex("by_action", (q) => q.eq("action", "premium_home"))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (!actionConfig) {
      throw new Error("Configuración de premium home no encontrada");
    }

    // Verificar créditos
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (!subscription) {
      const now = Date.now();
      const subscriptionId = await ctx.db.insert("subscriptions", {
        userId: identity.subject,
        plan: "free",
        status: "active",
        credits: 10,
        creditsUsed: 0,
        maxProperties: 5,
        propertiesCount: 0,
        createdAt: now,
        updatedAt: now,
      });
      
      // Obtener la suscripción recién creada
      subscription = await ctx.db.get(subscriptionId);
      if (!subscription) {
        throw new Error("Error al crear suscripción inicial");
      }
    }

    const availableCredits = subscription.credits - subscription.creditsUsed;
    if (availableCredits < actionConfig.cost) {
      throw new Error(`Créditos insuficientes. Necesitas ${actionConfig.cost}, tienes ${availableCredits}`);
    }

    const now = Date.now();
    const duration = actionConfig.duration || 7; // 7 días por defecto
    const expiresAt = now + (duration * 24 * 60 * 60 * 1000);

    // Crear transacción
    const transactionId = await ctx.db.insert("transactions", {
      userId: identity.subject,
      action: "premium_home",
      creditsCost: actionConfig.cost,
      description: `Posición premium home "${property.title}" por ${duration} días`,
      propertyId: args.propertyId,
      duration,
      expiresAt,
      status: "active",
      createdAt: now,
    });

    // Consumir créditos
    await ctx.db.patch(subscription._id, {
      creditsUsed: subscription.creditsUsed + actionConfig.cost,
      updatedAt: now,
    });

    // Actualizar propiedad
    await ctx.db.patch(args.propertyId, {
      premiumHomeUntil: expiresAt,
      premiumTransaction: transactionId,
    });

    // 🚨 VERIFICAR CRÉDITOS RESTANTES Y NOTIFICAR SI ESTÁN BAJOS
    try {
      const updatedSubscription = await ctx.db.get(subscription._id);
      if (updatedSubscription) {
        const remainingCredits = updatedSubscription.credits - updatedSubscription.creditsUsed;
        
        // Notificar cuando los créditos se agotan completamente
        if (remainingCredits <= 0) {
          const user = await ctx.db
            .query("users")
            .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
            .first();

          if (user && user.email) {
            ctx.scheduler.runAfter(0, api.emails.notifyLowCredits, {
              userEmail: user.email,
              userName: user.name || "Usuario",
              currentPlan: updatedSubscription.plan,
              creditsUsed: updatedSubscription.creditsUsed,
              totalCredits: updatedSubscription.credits,
              dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/finance`,
              upgradeUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/finance`,
            }).catch((error) => {
              console.error("Error enviando notificación de créditos agotados:", error);
            });
          }
        }
      }
    } catch (error) {
      console.error("Error verificando créditos restantes:", error);
    }

    return {
      success: true,
      transactionId,
      expiresAt,
      creditsCost: actionConfig.cost,
      remainingCredits: availableCredits - actionConfig.cost,
    };
  },
});

// Quitar de premium home
export const removeFromHomePremium = mutation({
  args: {
    propertyId: v.id("properties"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    if (property.ownerId !== identity.subject) {
      throw new Error("No autorizado");
    }

    if (!property.premiumHomeUntil) {
      throw new Error("La propiedad no está en premium home");
    }

    // Cancelar transacción si existe
    if (property.premiumTransaction) {
      const transaction = await ctx.db.get(property.premiumTransaction);
      if (transaction && transaction.status === "active") {
        await ctx.db.patch(property.premiumTransaction, {
          status: "cancelled",
          updatedAt: Date.now(),
        });
      }
    }

    // Remover de premium
    await ctx.db.patch(args.propertyId, {
      premiumHomeUntil: undefined,
      premiumTransaction: undefined,
    });

    return { success: true };
  },
});

// Obtener propiedad premium actual para el home
export const getCurrentPremiumProperty = query({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    const premiumProperty = await ctx.db
      .query("properties")
      .withIndex("by_premium_until", (q) => q.gt("premiumHomeUntil", now))
      .first();

    return premiumProperty;
  },
});

// Obtener propiedades destacadas con ordenamiento correcto
export const getFeaturedProperties = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Obtener todas las propiedades publicadas
    const allProperties = await ctx.db
      .query("properties")
      .filter((q) => q.neq(q.field("status"), "draft"))
      .collect();

    // Filtrar y ordenar con prioridad:
    // 1. Premium home (si está activo)
    // 2. Destacadas activas
    // 3. Normales por fecha de creación
    const sortedProperties = allProperties
      .filter(p => {
        // Limpiar destacadas expiradas
        if (p.featured && p.featuredUntil && p.featuredUntil <= now) {
          // Esta propiedad debería ser actualizada por el cron job
          return true; // incluir pero no como destacada
        }
        // Limpiar premium expiradas
        if (p.premiumHomeUntil && p.premiumHomeUntil <= now) {
          // Esta propiedad debería ser actualizada por el cron job
          return true;
        }
        return true;
      })
      .sort((a, b) => {
        // Prioridad 1: Premium home activo
        const aPremium = a.premiumHomeUntil && a.premiumHomeUntil > now;
        const bPremium = b.premiumHomeUntil && b.premiumHomeUntil > now;
        
        if (aPremium && !bPremium) return -1;
        if (!aPremium && bPremium) return 1;
        
        // Prioridad 2: Destacadas activas
        const aFeatured = a.featured && a.featuredUntil && a.featuredUntil > now;
        const bFeatured = b.featured && b.featuredUntil && b.featuredUntil > now;
        
        if (aFeatured && !bFeatured) return -1;
        if (!aFeatured && bFeatured) return 1;
        
        // Prioridad 3: Fecha de creación (más recientes primero)
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    return sortedProperties.slice(0, args.limit || 20);
  },
});

// Obtener estado de destacados del usuario
export const getUserFeaturedStatus = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const now = Date.now();

    // Obtener propiedades del usuario
    const userProperties = await ctx.db
      .query("properties")
      .withIndex("by_owner", (q) => q.eq("ownerId", identity.subject))
      .collect();

    // Analizar estado de cada propiedad
    const propertyStatus = userProperties.map(property => {
      const isFeatured = property.featured && property.featuredUntil && property.featuredUntil > now;
      const isPremium = property.premiumHomeUntil && property.premiumHomeUntil > now;

      return {
        id: property._id,
        title: property.title,
        isFeatured,
        isPremium,
        featuredUntil: property.featuredUntil,
        premiumUntil: property.premiumHomeUntil,
        canFeature: !isFeatured,
        canPremium: !isPremium,
      };
    });

    return {
      properties: propertyStatus,
      totalProperties: userProperties.length,
      featuredCount: propertyStatus.filter(p => p.isFeatured).length,
      premiumCount: propertyStatus.filter(p => p.isPremium).length,
    };
  },
});

// Migrar propiedades destacadas del sistema viejo al nuevo
export const migrateLegacyFeaturedProperties = mutation({
  args: {},
  handler: async (ctx) => {
    // Buscar propiedades con featured=true pero sin featuredUntil
    const legacyFeatured = await ctx.db
      .query("properties")
      .filter((q) => 
        q.and(
          q.eq(q.field("featured"), true),
          q.eq(q.field("featuredUntil"), undefined)
        )
      )
      .collect();

    console.log(`🔄 Encontradas ${legacyFeatured.length} propiedades destacadas legacy`);

    // Migrar cada propiedad
    for (const property of legacyFeatured) {
      const now = Date.now();
      const thirtyDaysFromNow = now + (30 * 24 * 60 * 60 * 1000); // 30 días

      await ctx.db.patch(property._id, {
        featuredUntil: thirtyDaysFromNow,
        updatedAt: new Date().toISOString(),
      });

      console.log(`✅ Migrada propiedad destacada: ${property.title}`);
    }

    return {
      success: true,
      migratedCount: legacyFeatured.length,
      message: `Se migraron ${legacyFeatured.length} propiedades destacadas del sistema legacy`
    };
  },
});

// Limpiar propiedades no destacadas del sistema viejo
export const cleanLegacyFeaturedFlags = mutation({
  args: {},
  handler: async (ctx) => {
    // Buscar propiedades con featured=false pero que podrían estar mal
    const legacyNotFeatured = await ctx.db
      .query("properties")
      .filter((q) => 
        q.and(
          q.eq(q.field("featured"), true),
          q.eq(q.field("featuredUntil"), undefined)
        )
      )
      .collect();

    for (const property of legacyNotFeatured) {
      await ctx.db.patch(property._id, {
        featured: false,
        featuredUntil: undefined,
        premiumHomeUntil: undefined,
        featuredTransaction: undefined,
        premiumTransaction: undefined,
        updatedAt: new Date().toISOString(),
      });
    }

    return {
      success: true,
      cleanedCount: legacyNotFeatured.length
    };
  },
});

// Limpiar TODAS las propiedades destacadas (para pruebas)
export const clearAllFeaturedProperties = mutation({
  args: {},
  handler: async (ctx) => {
    // Obtener TODAS las propiedades que tienen algún tipo de destacado
    const allProperties = await ctx.db
      .query("properties")
      .collect();

    const featuredProperties = allProperties.filter(p => 
      p.featured || 
      p.featuredUntil || 
      p.premiumHomeUntil || 
      p.featuredTransaction || 
      p.premiumTransaction
    );

    console.log(`🧹 Limpiando ${featuredProperties.length} propiedades con destacados/premium`);

    // Obtener todas las transacciones activas relacionadas con destacados/premium
    const activeTransactions = await ctx.db
      .query("transactions")
      .filter((q) => 
        q.and(
          q.eq(q.field("status"), "active"),
          q.or(
            q.eq(q.field("action"), "featured_property"),
            q.eq(q.field("action"), "premium_home")
          )
        )
      )
      .collect();

    console.log(`💰 Devolviendo créditos para ${activeTransactions.length} transacciones`);

    // Devolver créditos a cada usuario
    let totalCreditsReturned = 0;
    const usersProcessed = new Set();

    for (const transaction of activeTransactions) {
      // Buscar la suscripción del usuario
      const subscription = await ctx.db
        .query("subscriptions")
        .withIndex("userId", (q) => q.eq("userId", transaction.userId))
        .first();

      if (subscription) {
        // Devolver los créditos restando de creditsUsed
        const newCreditsUsed = Math.max(0, subscription.creditsUsed - transaction.creditsCost);
        
        await ctx.db.patch(subscription._id, {
          creditsUsed: newCreditsUsed,
          updatedAt: Date.now(),
        });

        totalCreditsReturned += transaction.creditsCost;
        usersProcessed.add(transaction.userId);
        
        console.log(`💰 Devueltos ${transaction.creditsCost} créditos al usuario ${transaction.userId}`);
      }

      // Marcar transacción como cancelada
      await ctx.db.patch(transaction._id, {
        status: "cancelled",
        updatedAt: Date.now(),
      });
    }

    // Limpiar cada propiedad
    for (const property of featuredProperties) {
      await ctx.db.patch(property._id, {
        featured: false,
        featuredUntil: undefined,
        premiumHomeUntil: undefined,
        featuredTransaction: undefined,
        premiumTransaction: undefined,
        updatedAt: new Date().toISOString(),
      });

      console.log(`🧽 Limpiada propiedad: ${property.title}`);
    }

    return {
      success: true,
      clearedPropertiesCount: featuredProperties.length,
      cancelledTransactionsCount: activeTransactions.length,
      totalCreditsReturned,
      usersAffected: usersProcessed.size,
      message: `Se limpiaron ${featuredProperties.length} propiedades, se cancelaron ${activeTransactions.length} transacciones y se devolvieron ${totalCreditsReturned} créditos a ${usersProcessed.size} usuarios`
    };
  },
});

// Función para limpiar datos inconsistentes
export const cleanInconsistentData = mutation({
  args: {},
  handler: async (ctx) => {
    // Buscar propiedades que referencien transacciones canceladas o que no existen
    const allProperties = await ctx.db
      .query("properties")
      .collect();

    let cleanedCount = 0;

    for (const property of allProperties) {
      let needsUpdate = false;
      const updates: any = {};

      // Verificar transacción de destacado
      if (property.featuredTransaction) {
        const transaction = await ctx.db.get(property.featuredTransaction);
        if (!transaction || transaction.status !== "active") {
          updates.featured = false;
          updates.featuredUntil = undefined;
          updates.featuredTransaction = undefined;
          needsUpdate = true;
        }
      }

      // Verificar transacción de premium
      if (property.premiumTransaction) {
        const transaction = await ctx.db.get(property.premiumTransaction);
        if (!transaction || transaction.status !== "active") {
          updates.premiumHomeUntil = undefined;
          updates.premiumTransaction = undefined;
          needsUpdate = true;
        }
      }

      // Verificar fechas expiradas
      const now = Date.now();
      if (property.featuredUntil && property.featuredUntil <= now) {
        updates.featured = false;
        updates.featuredUntil = undefined;
        updates.featuredTransaction = undefined;
        needsUpdate = true;
      }

      if (property.premiumHomeUntil && property.premiumHomeUntil <= now) {
        updates.premiumHomeUntil = undefined;
        updates.premiumTransaction = undefined;
        needsUpdate = true;
      }

      if (needsUpdate) {
        await ctx.db.patch(property._id, {
          ...updates,
          updatedAt: new Date().toISOString(),
        });
        cleanedCount++;
        console.log(`🧹 Limpiada propiedad inconsistente: ${property.title}`);
      }
    }

    return {
      success: true,
      cleanedCount,
      message: `Se limpiaron ${cleanedCount} propiedades con datos inconsistentes`
    };
  },
});

// 🚨 NUEVA: Función para verificar y procesar vencimientos (cron job)
export const processExpirations = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    // 1. Procesar propiedades destacadas vencidas
    const expiredFeatured = await ctx.db
      .query("properties")
      .filter((q) => 
        q.and(
          q.eq(q.field("featured"), true),
          q.lt(q.field("featuredUntil"), now)
        )
      )
      .collect();

    for (const property of expiredFeatured) {
      // Desactivar featured
      await ctx.db.patch(property._id, {
        featured: false,
        featuredUntil: undefined,
      });

      // Marcar transacción como expirada
      if (property.featuredTransaction) {
        const transaction = await ctx.db.get(property.featuredTransaction);
        if (transaction && transaction.status === "active") {
          await ctx.db.patch(property.featuredTransaction, {
            status: "expired",
            updatedAt: now,
          });
        }
      }

      // 📧 Enviar notificación de vencimiento
      try {
        const owner = await ctx.db
          .query("users")
          .withIndex("by_token", (q) => q.eq("tokenIdentifier", property.ownerId))
          .first();

        if (owner && owner.email) {
          ctx.scheduler.runAfter(0, api.emails.notifyFeaturedExpired, {
            userEmail: owner.email,
            userName: owner.name || "Usuario",
            propertyTitle: property.title,
            propertyAddress: `${property.address}, ${property.city}`,
            propertyPrice: `${property.currency} ${property.price?.toLocaleString()}`,
            featuredDuration: 7, // Duración estándar
            expiredDate: new Date(property.featuredUntil || now).toISOString(),
            renewUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/properties`,
            dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`,
          }).catch((error) => {
            console.error("Error enviando notificación de destacada vencida:", error);
          });
        }
      } catch (error) {
        console.error("Error procesando notificación de destacada vencida:", error);
      }
    }

    // 2. Procesar propiedades premium vencidas
    const expiredPremium = await ctx.db
      .query("properties")
      .filter((q) => 
        q.and(
          q.gt(q.field("premiumHomeUntil"), 0),
          q.lt(q.field("premiumHomeUntil"), now)
        )
      )
      .collect();

    for (const property of expiredPremium) {
      // Desactivar premium
      await ctx.db.patch(property._id, {
        premiumHomeUntil: undefined,
      });

      // Marcar transacción como expirada
      if (property.premiumTransaction) {
        const transaction = await ctx.db.get(property.premiumTransaction);
        if (transaction && transaction.status === "active") {
          await ctx.db.patch(property.premiumTransaction, {
            status: "expired",
            updatedAt: now,
          });
        }
      }

      // 📧 Enviar notificación de vencimiento premium
      try {
        const owner = await ctx.db
          .query("users")
          .withIndex("by_token", (q) => q.eq("tokenIdentifier", property.ownerId))
          .first();

        // Verificar si hay espacio premium disponible (solo 1)
        const currentPremium = await ctx.db
          .query("properties")
          .filter((q) => q.gt(q.field("premiumHomeUntil"), now))
          .first();

        if (owner && owner.email) {
          ctx.scheduler.runAfter(0, api.emails.notifyPremiumExpired, {
            userEmail: owner.email,
            userName: owner.name || "Usuario",
            propertyTitle: property.title,
            propertyAddress: `${property.address}, ${property.city}`,
            propertyPrice: `${property.currency} ${property.price?.toLocaleString()}`,
            premiumDuration: 7, // Duración estándar
            expiredDate: new Date(property.premiumHomeUntil || now).toISOString(),
            availableSlots: currentPremium ? 0 : 1,
            renewUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/properties`,
            dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`,
          }).catch((error) => {
            console.error("Error enviando notificación de premium vencida:", error);
          });
        }
      } catch (error) {
        console.error("Error procesando notificación de premium vencida:", error);
      }
    }

    return {
      expiredFeatured: expiredFeatured.length,
      expiredPremium: expiredPremium.length,
      processedAt: now,
    };
  }
});

// 🚨 NUEVA: Query para obtener estadísticas de límites
export const getFeaturedLimitsStats = query({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    // Contar propiedades destacadas activas
    const activeFeatured = await ctx.db
      .query("properties")
      .filter((q) => 
        q.and(
          q.eq(q.field("featured"), true),
          q.gt(q.field("featuredUntil"), now)
        )
      )
      .collect();

    // Contar propiedades premium activas
    const activePremium = await ctx.db
      .query("properties")
      .filter((q) => 
        q.gt(q.field("premiumHomeUntil"), now)
      )
      .collect();

    return {
      featured: {
        active: activeFeatured.length,
        limit: 9,
        available: 9 - activeFeatured.length,
        percentage: Math.round((activeFeatured.length / 9) * 100)
      },
               premium: {
           active: activePremium.length,
           limit: 1,
           available: 1 - activePremium.length,
           percentage: Math.round((activePremium.length / 1) * 100)
         }
    };
  }
});

// 🚨 NUEVA: Función automática para ejecutar cada hora
export const scheduleExpirationCheck = mutation({
  args: {},
  handler: async (ctx) => {
    // Programar verificación de vencimientos cada hora
    ctx.scheduler.runAfter(60 * 60 * 1000, api.featuredProperties.processExpirations, {});
    
    return { 
      success: true, 
      message: "Verificación de vencimientos programada para 1 hora" 
    };
  }
});

// 🧪 FUNCIÓN DE TESTING: Simular vencimientos para pruebas
export const simulateExpirations = mutation({
  args: { 
    expireFeatured: v.optional(v.boolean()),
    expirePremium: v.optional(v.boolean())
  },
  handler: async (ctx, args): Promise<{
    success: boolean;
    message: string;
    results: {
      featuredExpired: number;
      premiumExpired: number;
      notificationsSent: number;
    };
    processed: {
      expiredFeatured: number;
      expiredPremium: number;
      processedAt: number;
    };
  }> => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const now = Date.now();
    const results = {
      featuredExpired: 0,
      premiumExpired: 0,
      notificationsSent: 0
    };

    // Simular vencimiento de destacadas del usuario
    if (args.expireFeatured) {
      const userFeaturedProperties = await ctx.db
        .query("properties")
        .filter((q) => 
          q.and(
            q.eq(q.field("ownerId"), identity.subject),
            q.eq(q.field("featured"), true),
            q.gt(q.field("featuredUntil"), now)
          )
        )
        .collect();

      for (const property of userFeaturedProperties) {
        // Cambiar fecha a pasado para simular vencimiento
        await ctx.db.patch(property._id, {
          featuredUntil: now - 1000 // 1 segundo en el pasado
        });
        results.featuredExpired++;
      }
    }

    // Simular vencimiento de premium del usuario
    if (args.expirePremium) {
      const userPremiumProperties = await ctx.db
        .query("properties")
        .filter((q) => 
          q.and(
            q.eq(q.field("ownerId"), identity.subject),
            q.gt(q.field("premiumHomeUntil"), now)
          )
        )
        .collect();

      for (const property of userPremiumProperties) {
        // Cambiar fecha a pasado para simular vencimiento
        await ctx.db.patch(property._id, {
          premiumHomeUntil: now - 1000 // 1 segundo en el pasado
        });
        results.premiumExpired++;
      }
    }

    // Ejecutar el procesamiento de vencimientos
    const processed: {
      expiredFeatured: number;
      expiredPremium: number;
      processedAt: number;
    } = await ctx.runMutation(api.featuredProperties.processExpirations, {});
    results.notificationsSent = processed.expiredFeatured + processed.expiredPremium;

    return {
      success: true,
      message: `Simulación completada: ${results.featuredExpired} destacadas y ${results.premiumExpired} premium vencidas. Se enviaron ${results.notificationsSent} notificaciones.`,
      results,
      processed
    };
  }
});

// 🧪 FUNCIÓN DE TESTING: Verificar estado actual
export const getTestingStats = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const now = Date.now();
    
    // Propiedades del usuario
    const userProperties = await ctx.db
      .query("properties")
      .withIndex("by_owner", (q) => q.eq("ownerId", identity.subject))
      .collect();

    const analysis = userProperties.map(p => ({
      title: p.title,
      featured: p.featured,
      featuredUntil: p.featuredUntil,
      premiumUntil: p.premiumHomeUntil,
      status: {
        featuredActive: p.featured && p.featuredUntil && p.featuredUntil > now,
        premiumActive: p.premiumHomeUntil && p.premiumHomeUntil > now,
        featuredExpired: p.featuredUntil && p.featuredUntil <= now,
        premiumExpired: p.premiumHomeUntil && p.premiumHomeUntil <= now
      },
      timeLeft: {
        featured: p.featuredUntil ? Math.round((p.featuredUntil - now) / (60 * 1000)) : null, // minutos
        premium: p.premiumHomeUntil ? Math.round((p.premiumHomeUntil - now) / (60 * 1000)) : null // minutos
      }
    }));

    return {
      currentTime: now,
      userProperties: analysis,
      summary: {
        total: userProperties.length,
        featuredActive: analysis.filter(p => p.status.featuredActive).length,
        premiumActive: analysis.filter(p => p.status.premiumActive).length,
        featuredExpired: analysis.filter(p => p.status.featuredExpired).length,
        premiumExpired: analysis.filter(p => p.status.premiumExpired).length
      }
    };
  }
}); 