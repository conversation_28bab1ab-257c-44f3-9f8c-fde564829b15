"use client";

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Calendar } from 'lucide-react';

interface CalendarViewProps {
  appointments: any[];
  onDateSelect: (date: Date) => void;
  selectedDate: Date;
}

export function CalendarView({ appointments, onDateSelect, selectedDate }: CalendarViewProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="w-5 h-5" />
          Vista de Calendario
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-12">
          <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Vista de Calendario
          </h3>
          <p className="text-gray-600">
            Próximamente: Vista de calendario completa con todas tus citas
          </p>
        </div>
      </CardContent>
    </Card>
  );
} 