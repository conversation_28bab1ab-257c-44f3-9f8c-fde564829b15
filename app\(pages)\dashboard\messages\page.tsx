"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  Mail, 
  MailOpen, 
  Archive, 
  Search, 
  Filter,
  Eye,
  MessageSquare,
  Calendar,
  TrendingUp,
  CreditCard,
  Building2,
  Lock,
  Reply
} from "lucide-react";
import Image from "next/image";
import { formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { toast } from "sonner";
import { CreditsDisplay } from "@/components/ui/credits-display";
import { useUser } from "@clerk/clerk-react";
import Link from "next/link";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function MessagesPage() {
  const [activeTab, setActiveTab] = useState<"inbox" | "sent">("inbox");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMessage, setSelectedMessage] = useState<string | null>(null);

  // Queries
  const inboxMessages = useQuery(api.messages.getInboxMessages, {});
  const sentMessages = useQuery(api.messages.getSentMessages, {});
  const messageStats = useQuery(api.messages.getMessageStats, {});

  // Mutations
  const markAsRead = useMutation(api.messages.markAsRead);
  const archiveMessage = useMutation(api.messages.archiveMessage);

  const handleMarkAsRead = async (messageId: string) => {
    try {
      await markAsRead({ messageId: messageId as any });
      toast.success("Mensaje marcado como leído");
    } catch (error) {
      toast.error("Error al marcar como leído");
    }
  };

  const handleArchive = async (messageId: string) => {
    try {
      await archiveMessage({ messageId: messageId as any });
      toast.success("Mensaje archivado");
    } catch (error) {
      toast.error("Error al archivar mensaje");
    }
  };

  const getLeadTypeBadge = (leadType: string) => {
    const types = {
      inquiry: { label: "Consulta", variant: "default" as const, color: "bg-blue-100 text-blue-800" },
      viewing: { label: "Visita", variant: "secondary" as const, color: "bg-green-100 text-green-800" },
      offer: { label: "Oferta", variant: "destructive" as const, color: "bg-yellow-100 text-yellow-800" },
      negotiation: { label: "Negociación", variant: "outline" as const, color: "bg-purple-100 text-purple-800" },
    };
    return types[leadType as keyof typeof types] || types.inquiry;
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(price);
  };

  const filteredInboxMessages = inboxMessages?.filter(message =>
    message.senderName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.property?.title.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const filteredSentMessages = sentMessages?.filter(message =>
    message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.property?.title.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Componente para mensaje protegido por créditos
  function ProtectedMessageCard({ message }: { message: any }) {
    const [isReading, setIsReading] = useState(false);
    
    // Verificar si puede leer el mensaje
    const canRead = useQuery(api.subscriptions.canPerformAction, {
      action: "read_message",
      resourceId: message._id,
    });

    // Consumir créditos para leer el mensaje
    const consumeCredits = useMutation(api.subscriptions.consumeCreditsForAction);

    const handleReadMessage = async () => {
      if (canRead?.alreadyPaid) {
        setIsReading(true);
        // Marcar como leído
        if (message.status === 'unread') {
          handleMarkAsRead(message._id);
        }
        return;
      }

      try {
        await consumeCredits({
          action: "read_message",
          resourceId: message._id,
        });
        setIsReading(true);
        // Marcar como leído
        if (message.status === 'unread') {
          handleMarkAsRead(message._id);
        }
      } catch (error) {
        console.error('Error consuming credits:', error);
      }
    };

    // Si ya pagó o ya está leyendo, mostrar el mensaje completo
    if (canRead?.alreadyPaid || isReading) {
      return <MessageCard message={message} />;
    }

    // Si no tiene créditos suficientes, mostrar mensaje de créditos insuficientes
    if (canRead && !canRead.canPerform) {
      return (
        <Card className={`hover:shadow-md transition-shadow border-l-4 border-l-red-400`}>
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <div className="bg-red-100 p-3 rounded-full">
                  <Lock className="w-8 h-8 text-red-600" />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Mensaje Bloqueado - Créditos Insuficientes
                </h3>
                <p className="text-gray-600 mb-4">
                  Necesitas <strong>{canRead.requiredCredits} créditos</strong> para leer este mensaje.
                </p>
                <div className="bg-gray-50 rounded-lg p-3 mb-4">
                  <p className="text-sm text-gray-700">
                    <strong>Tienes:</strong> {canRead.availableCredits} créditos disponibles<br/>
                    <strong>Necesitas:</strong> {canRead.requiredCredits} créditos para leer el mensaje
                  </p>
                </div>
                <Link href="/dashboard/finance">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <CreditCard className="w-4 h-4 mr-2" />
                    Comprar Créditos
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    // Mostrar vista previa borrosa y botón para desbloquear
    return (
      <Card className={`hover:shadow-md transition-shadow border-l-4 border-l-yellow-400`}>
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Vista previa limitada */}
            <div className="flex items-start gap-4">
              {/* Información de la propiedad */}
              <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                {message.property?.images?.[0] ? (
                  <Image
                    src={message.property.images[0]}
                    alt={message.property.title}
                    width={80}
                    height={80}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <Building2 className="h-8 w-8 text-gray-400" />
                  </div>
                )}
              </div>

              {/* Contenido del mensaje (parcialmente visible) */}
              <div className="flex-1">
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h3 className="font-semibold text-gray-900">{message.subject}</h3>
                    <p className="text-sm text-gray-600">
                      De: <span className="font-medium">{message.senderName}</span>
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getLeadTypeBadge(message.leadType).color}>
                      {getLeadTypeBadge(message.leadType).label}
                    </Badge>
                    {message.status === 'unread' && (
                      <Badge variant="default" className="bg-blue-600">
                        Nuevo
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Información de la propiedad */}
                {message.property && (
                  <div className="bg-gray-50 rounded-lg p-3 mb-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-gray-900">{message.property.title}</h4>
                      <span className="text-sm font-semibold text-blue-600">
                        {formatPrice(message.property.price, message.property.currency)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">{message.property.city} • {message.property.type}</p>
                  </div>
                )}

                {/* Mensaje borroso */}
                <div className="relative mb-3">
                  <p className="text-gray-700 line-clamp-2 blur-sm select-none">
                    {message.message}
                  </p>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-white"></div>
                </div>

                {/* Mensaje de desbloqueo */}
                <Alert>
                  <Lock className="h-4 w-4" />
                  <AlertDescription>
                    <div className="flex items-center justify-between">
                      <span>
                        Desbloquea este mensaje por <strong>{canRead?.requiredCredits || 2} créditos</strong>
                      </span>
                      <Button 
                        size="sm" 
                        onClick={handleReadMessage}
                        className="ml-4"
                      >
                        <CreditCard className="w-3 h-3 mr-1" />
                        Leer ({canRead?.requiredCredits || 2} créditos)
                      </Button>
                    </div>
                  </AlertDescription>
                </Alert>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Componente para mostrar el mensaje completo
  function MessageCard({ message }: { message: any }) {
    return (
      <Card className={`hover:shadow-md transition-shadow border-l-4 border-l-green-400`}>
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            {/* Información de la propiedad */}
            <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
              {message.property?.images?.[0] ? (
                <Image
                  src={message.property.images[0]}
                  alt={message.property.title}
                  width={80}
                  height={80}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <Building2 className="h-8 w-8 text-gray-400" />
                </div>
              )}
            </div>

            {/* Contenido del mensaje */}
            <div className="flex-1">
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h3 className="font-semibold text-gray-900">{message.subject}</h3>
                  <p className="text-sm text-gray-600">
                    De: <span className="font-medium">{message.senderName}</span> ({message.senderEmail})
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getLeadTypeBadge(message.leadType).color}>
                    {getLeadTypeBadge(message.leadType).label}
                  </Badge>
                  <Badge className="bg-green-100 text-green-800">
                    Leído
                  </Badge>
                </div>
              </div>

              {/* Información de la propiedad */}
              {message.property && (
                <div className="bg-gray-50 rounded-lg p-3 mb-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900">{message.property.title}</h4>
                    <span className="text-sm font-semibold text-blue-600">
                      {formatPrice(message.property.price, message.property.currency)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">{message.property.city} • {message.property.type}</p>
                </div>
              )}

              {/* Mensaje completo */}
              <p className="text-gray-700 mb-3">{message.message}</p>

              {/* Acciones y timestamp */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Calendar className="h-4 w-4" />
                  {formatDistanceToNow(new Date(message.createdAt), { 
                    addSuffix: true, 
                    locale: es 
                  })}
                  {message.creditsCharged && (
                    <>
                      <span className="mx-2">•</span>
                      <CreditCard className="h-4 w-4" />
                      {message.creditsCharged} créditos
                    </>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleArchive(message._id)}
                  >
                    <Archive className="h-4 w-4 mr-1" />
                    Archivar
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                  >
                    <Reply className="h-4 w-4 mr-1" />
                    Responder
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-semibold tracking-tight">Mensajes</h1>
          <p className="text-muted-foreground mt-2">
            Gestiona tus leads y consultas de propiedades
          </p>
        </div>
        
        <CreditsDisplay variant="compact" />
      </div>

      {/* Estadísticas */}
      {messageStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Mail className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Nuevos</p>
                  <p className="text-2xl font-bold">{messageStats.unreadCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <MessageSquare className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Leads</p>
                  <p className="text-2xl font-bold">{messageStats.totalCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <CreditCard className="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Créditos</p>
                  <p className="text-2xl font-bold">{messageStats.creditsEarned}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Visitas</p>
                  <p className="text-2xl font-bold">{messageStats.leadTypeStats.viewing || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Buscador */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Buscar mensajes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      {/* Tabs de Mensajes */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "inbox" | "sent")}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="inbox" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Recibidos ({messageStats?.unreadCount || 0})
          </TabsTrigger>
          <TabsTrigger value="sent" className="flex items-center gap-2">
            <MailOpen className="h-4 w-4" />
            Enviados
          </TabsTrigger>
        </TabsList>

        <TabsContent value="inbox" className="space-y-4">
          {filteredInboxMessages.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No hay mensajes</h3>
                <p className="text-gray-500">Cuando recibas consultas sobre tus propiedades aparecerán aquí.</p>
              </CardContent>
            </Card>
          ) : (
            filteredInboxMessages.map((message) => (
              <ProtectedMessageCard key={message._id} message={message} />
            ))
          )}
        </TabsContent>

        <TabsContent value="sent" className="space-y-4">
          {filteredSentMessages.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <MailOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No hay mensajes enviados</h3>
                <p className="text-gray-500">Los mensajes que envíes aparecerán aquí.</p>
              </CardContent>
            </Card>
          ) : (
            filteredSentMessages.map((message) => (
              <Card key={message._id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    {/* Información de la propiedad */}
                    <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                      {message.property?.images?.[0] ? (
                        <Image
                          src={message.property.images[0]}
                          alt={message.property.title}
                          width={80}
                          height={80}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Building2 className="h-8 w-8 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Contenido del mensaje */}
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h3 className="font-semibold text-gray-900">{message.subject}</h3>
                          <p className="text-sm text-gray-600">
                            Enviado como: <span className="font-medium">{message.senderName}</span>
                          </p>
                        </div>
                        <Badge className={getLeadTypeBadge(message.leadType).color}>
                          {getLeadTypeBadge(message.leadType).label}
                        </Badge>
                      </div>

                      {/* Información de la propiedad */}
                      {message.property && (
                        <div className="bg-gray-50 rounded-lg p-3 mb-3">
                          <h4 className="font-medium text-gray-900">{message.property.title}</h4>
                          <p className="text-sm text-gray-600">{message.property.city}</p>
                        </div>
                      )}

                      {/* Mensaje */}
                      <p className="text-gray-700 mb-3 line-clamp-2">{message.message}</p>

                      {/* Timestamp */}
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Calendar className="h-4 w-4" />
                        {formatDistanceToNow(new Date(message.createdAt), { 
                          addSuffix: true, 
                          locale: es 
                        })}
                        <span className="mx-2">•</span>
                        <span className="capitalize">{message.status}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
} 