# 🚀 CONFIGURACIÓN COMPLETA DE STRIPE PARA INMO

## 📋 VARIABLES DE ENTORNO REQUERIDAS

Agrega estas variables a tu archivo `.env.local`:

```bash
# ========================================
# STRIPE CONFIGURATION 
# ========================================

# Claves principales de Stripe
STRIPE_SECRET_KEY=sk_test_...  # 👈 Ya la tienes
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...  # 👈 Ya la tienes

# Webhook de Stripe  
STRIPE_WEBHOOK_SECRET=whsec_...  # 👈 Ya la tienes

# Price IDs de los productos (ACTUALIZAR CON LOS IDs REALES)
STRIPE_PRICE_PRO=price_1234567890abcdef...  # 👈 COPIAR DESDE STRIPE
STRIPE_PRICE_PREMIUM=price_1234567890abcdef...  # 👈 COPIAR DESDE STRIPE

# URL de la aplicación
NEXT_PUBLIC_APP_URL=http://localhost:3000  # En producción cambiar por tu dominio
```

## 🛍️ CREAR PRODUCTOS EN STRIPE DASHBOARD

### 1️⃣ Producto "Inmo Pro"
1. Ve a https://dashboard.stripe.com/products
2. Clic en **"Add product"**
3. Completa:
   - **Name:** `Inmo Pro`
   - **Description:** `Plan Pro para agentes inmobiliarios individuales`
   - **Pricing model:** `Standard pricing`
   - **Price:** `$29.00`
   - **Billing period:** `Monthly`
   - **Currency:** `USD`
4. Guarda el producto
5. **⚠️ COPIA EL PRICE ID** (formato: `price_xxxxxxxxxxxxx`)
6. Pégalo en `STRIPE_PRICE_PRO` en tu `.env.local`

### 2️⃣ Producto "Inmo Premium"  
1. Clic en **"Add product"** nuevamente
2. Completa:
   - **Name:** `Inmo Premium`
   - **Description:** `Plan Premium para equipos y agencias inmobiliarias`
   - **Pricing model:** `Standard pricing`
   - **Price:** `$79.00`
   - **Billing period:** `Monthly`
   - **Currency:** `USD`
3. Guarda el producto
4. **⚠️ COPIA EL PRICE ID** (formato: `price_xxxxxxxxxxxxx`)
5. Pégalo en `STRIPE_PRICE_PREMIUM` en tu `.env.local`

## 🔗 CONFIGURAR WEBHOOK

1. Ve a https://dashboard.stripe.com/webhooks
2. Clic en **"Add endpoint"**
3. **Endpoint URL:** `http://localhost:3000/api/stripe/webhook` (en desarrollo)
4. **Events to send:** Selecciona estos eventos:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
5. Guarda el webhook
6. **⚠️ COPIA EL SIGNING SECRET** (formato: `whsec_xxxxxxxxxxxxx`)
7. Pégalo en `STRIPE_WEBHOOK_SECRET` en tu `.env.local`

## ✅ EJEMPLO COMPLETO DE .env.local

```bash
# Existing variables...
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
NEXT_PUBLIC_CONVEX_URL=https://your-deployment.convex.cloud

# STRIPE CONFIGURATION
STRIPE_SECRET_KEY=sk_test_51234567890abcdef...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51234567890abcdef...
STRIPE_WEBHOOK_SECRET=whsec_1234567890abcdef...
STRIPE_PRICE_PRO=price_1234567890abcdef...
STRIPE_PRICE_PREMIUM=price_1234567890abcdef...
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 🧪 PROBAR LA CONFIGURACIÓN

1. Reinicia el servidor de desarrollo:
   ```bash
   npm run dev
   ```

2. Ve a http://localhost:3000/pricing

3. Intenta hacer upgrade a Pro o Premium

4. Usa tarjetas de prueba de Stripe:
   - **Éxito:** `4242 4242 4242 4242`
   - **Fallo:** `4000 0000 0000 0002`
   - **Fecha:** Cualquier fecha futura
   - **CVC:** Cualquier 3 dígitos

## 🌍 CONFIGURACIÓN PARA PRODUCCIÓN

Cuando vayas a producción, cambia:

1. **URL del webhook:** `https://tudominio.com/api/stripe/webhook`
2. **APP_URL:** `https://tudominio.com`
3. **Claves de Stripe:** Cambiar de `sk_test_` a `sk_live_`
4. **Webhook secret:** Crear nuevo webhook para producción

## 🎉 BENEFICIOS DE LA MIGRACIÓN

✅ **Interfaz en español** - Checkout completamente en español
✅ **Mejor UX** - Experiencia más pulida para usuarios guatemaltecos  
✅ **Más confiable** - Stripe es el estándar de la industria
✅ **Fácil configuración** - Solo necesitas los Price IDs
✅ **Webhooks robustos** - Manejo automático de suscripciones 