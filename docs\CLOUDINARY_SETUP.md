# Configuración de Cloudinary para Imágenes

## 1. Crear cuenta en Cloudinary

1. Ve a [cloudinary.com](https://cloudinary.com) y crea una cuenta gratuita
2. Una vez registrado, ve al Dashboard
3. Copia los siguientes valores:
   - Cloud Name
   - API Key
   - API Secret

## 2. Configurar variables de entorno

Agrega estas variables a tu archivo `.env.local`:

```env
# Cloudinary Configuration
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=tu_cloud_name
CLOUDINARY_API_KEY=tu_api_key
CLOUDINARY_API_SECRET=tu_api_secret
```

## 3. Crear Upload Preset

1. En el Dashboard de Cloudinary, ve a Settings > Upload
2. Scroll down hasta "Upload presets"
3. Haz clic en "Add upload preset"
4. Configura:
   - Preset name: `inmo_properties`
   - Signing Mode: `Unsigned`
   - Folder: `inmo/properties` (opcional)
   - Transformations: Puedes agregar transformaciones automáticas como redimensionado
5. Guarda el preset

## 4. Configuraciones recomendadas

### Transformaciones automáticas:
- Resize: `c_limit,w_1200,h_800,q_auto,f_auto`
- Esto optimiza automáticamente las imágenes para web

### Configuración de seguridad:
- En Settings > Security, puedes configurar:
  - Allowed formats: jpg, png, webp
  - Max file size: 10MB
  - Max image dimensions: 4000x4000

## 5. Verificar configuración

Una vez configurado, el sistema de carga de imágenes debería funcionar:
- Drag & drop de imágenes
- Vista previa automática
- Carga a Cloudinary
- URLs optimizadas

## Beneficios de usar Cloudinary

✅ **Control total**: Tus imágenes en tu cuenta
✅ **Optimización automática**: Compresión y formatos modernos
✅ **CDN global**: Carga rápida desde cualquier ubicación
✅ **Transformaciones**: Redimensionado automático
✅ **Backup**: Tus imágenes están seguras
✅ **SEO**: URLs propias mejoran el SEO 