"use client";

import { toast } from "sonner";

interface AlertOptions {
  title?: string;
  message: string;
  onConfirm?: () => void;
  confirmText?: string;
  showCancel?: boolean;
  cancelText?: string;
}

export const useAlert = () => {
  // Solo para alertas críticas que requieren confirmación
  const showConfirm = (
    message: string, 
    onConfirm: () => void,
    options?: {
      title?: string;
      confirmText?: string;
      cancelText?: string;
    }
  ) => {
    toast(options?.title || "Confirmar acción", {
      description: message,
      duration: Infinity,
      position: "top-center",
      action: {
        label: options?.confirmText || "Confirmar",
        onClick: () => {
          onConfirm();
          toast.dismiss();
        },
      },
      cancel: {
        label: options?.cancelText || "Cancelar",
        onClick: () => {
          toast.dismiss();
        },
      },
      style: {
        minWidth: "400px",
        padding: "20px",
        fontSize: "16px",
      },
      classNames: {
        toast: "border-2 shadow-2xl",
        title: "text-lg font-semibold",
        description: "text-base mt-2",
        actionButton: "px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md",
        cancelButton: "px-6 py-3 border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium rounded-md mr-3",
      }
    });
  };

  // Para errores críticos del sistema que requieren atención
  const showCriticalError = (
    message: string,
    onAction?: () => void,
    actionText?: string
  ) => {
    toast.error("Error del Sistema", {
      description: message,
      duration: Infinity,
      position: "top-center",
      action: onAction ? {
        label: actionText || "Reintentar",
        onClick: () => {
          onAction();
          toast.dismiss();
        },
      } : {
        label: "OK",
        onClick: () => toast.dismiss(),
      },
      style: {
        minWidth: "400px",
        padding: "20px",
        fontSize: "16px",
      },
      classNames: {
        toast: "border-2 border-red-200 shadow-2xl",
        title: "text-lg font-semibold text-red-800",
        description: "text-base mt-2",
        actionButton: "px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md",
      }
    });
  };

  // Para mensajes importantes del sistema (como en tu imagen)
  const showSystemMessage = (
    message: string,
    title?: string
  ) => {
    toast(title || "Sistema", {
      description: message,
      duration: Infinity,
      position: "top-center",
      action: {
        label: "OK",
        onClick: () => toast.dismiss(),
      },
      style: {
        minWidth: "400px",
        padding: "20px",
        fontSize: "16px",
      },
      classNames: {
        toast: "border-2 shadow-2xl",
        title: "text-lg font-semibold",
        description: "text-base mt-2",
        actionButton: "px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md",
      }
    });
  };

  return {
    showConfirm,      // Para confirmaciones importantes
    showCriticalError, // Para errores críticos del sistema  
    showSystemMessage, // Para mensajes importantes como en tu imagen
  };
}; 