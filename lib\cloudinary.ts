// Cliente de Cloudinary para frontend (sin dependencias de Node.js)
export const uploadToCloudinary = async (file: File): Promise<string> => {
  const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;
  
  if (!cloudName) {
    throw new Error('NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME no está configurado');
  }

  const formData = new FormData();
  formData.append('file', file);
  formData.append('upload_preset', 'inmo_properties');
  
  const response = await fetch(
    `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`,
    {
      method: 'POST',
      body: formData,
    }
  );
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Error uploading image: ${errorData.error?.message || 'Unknown error'}`);
  }
  
  const data = await response.json();
  return data.secure_url;
};

// Función para generar URLs optimizadas de Cloudinary
export const getOptimizedImageUrl = (publicId: string, options: {
  width?: number;
  height?: number;
  quality?: string;
  format?: string;
} = {}) => {
  const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;
  const { width = 800, height = 600, quality = 'auto', format = 'auto' } = options;
  
  return `https://res.cloudinary.com/${cloudName}/image/upload/w_${width},h_${height},c_limit,q_${quality},f_${format}/${publicId}`;
}; 