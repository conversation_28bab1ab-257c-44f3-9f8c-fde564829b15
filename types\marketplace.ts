export type PropertyType = 'house' | 'apartment' | 'office' | 'land' | 'commercial';
export type PropertyStatus = 'for_sale' | 'for_rent' | 'sold' | 'rented' | 'draft';
export type UserRole = 'buyer' | 'seller' | 'agent' | 'admin';
export type InquiryStatus = 'pending' | 'responded' | 'closed';

export interface Coordinates {
  lat: number;
  lng: number;
}

export interface Property {
  _id: string;
  title: string;
  description: string;
  price: number;
  currency: string;
  type: PropertyType;
  status: PropertyStatus;
  
  // Ubicación
  address: string;
  city: string;
  state: string;
  country: string;
  zipCode?: string;
  coordinates?: Coordinates;
  
  // Características
  bedrooms?: number;
  bathrooms?: number;
  area: number; // m²
  builtYear?: number;
  parking?: number;
  
  // Amenidades
  amenities?: string[];
  
  // Media
  images: string[]; // URLs Cloudinary
  virtualTour?: string;
  floorPlan?: string;
  
  // Relaciones
  ownerId: string; // Usuario vendedor
  agentId?: string; // Agente inmobiliario
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  
  // SEO
  slug?: string;
  featured?: boolean;
}

export interface User {
  _id: string;
  userId: string;
  email: string;
  name?: string;
  image?: string;
  role?: UserRole;
  phone?: string;
  company?: string;
  license?: string; // para agentes
  bio?: string;
  avatar?: string;
  currency?: string;
  notifications?: boolean;
  newsletter?: boolean;
  createdAt: string;
}

export interface Favorite {
  _id: string;
  userId: string;
  propertyId: string;
  createdAt: string;
}

export interface Inquiry {
  _id: string;
  propertyId: string;
  buyerId: string;
  sellerId: string;
  message: string;
  phone?: string;
  email: string;
  status: InquiryStatus;
  createdAt: string;
  respondedAt?: string;
  response?: string;
}

export interface PropertyFilters {
  type?: PropertyType[];
  status?: PropertyStatus[];
  city?: string;
  state?: string;
  country?: string;
  minPrice?: number;
  maxPrice?: number;
  minArea?: number;
  maxArea?: number;
  bedrooms?: number;
  bathrooms?: number;
  parking?: boolean;
  featured?: boolean;
  amenities?: string[];
}

export interface SearchParams {
  query?: string;
  filters?: PropertyFilters;
  page?: number;
  limit?: number;
  sortBy?: 'price_asc' | 'price_desc' | 'area_asc' | 'area_desc' | 'newest' | 'oldest';
} 