"use client";

import { useUser } from "@clerk/nextjs";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useEffect, useState } from "react";

export default function StoreUserEffect() {
  const { user, isLoaded } = useUser();
  const storeUser = useMutation(api.users.store);
  const syncProfile = useMutation(api.users.syncPublicProfile);
  const [hasStored, setHasStored] = useState(false);

  useEffect(() => {
    // Solo ejecutar si Clerk está cargado, el usuario está autenticado y no hemos guardado ya
    if (isLoaded && user && !hasStored) {
      console.log("Storing user in Convex...");
      storeUser()
        .then(async () => {
          console.log("User stored successfully");
          
          // Sincronizar datos del perfil público si existen
          const metadata = (user.unsafeMetadata as any) || {};
          if (metadata.displayName || metadata.bio) {
            console.log("Syncing public profile data...");
            await syncProfile({
              displayName: metadata.displayName,
              bio: metadata.bio
            });
            console.log("Public profile synced successfully");
          }
          
          setHasStored(true);
        })
        .catch((error) => {
          console.error("Error storing user:", error);
        });
    }
  }, [user, isLoaded, storeUser, syncProfile, hasStored]);

  return null; // Este componente no renderiza nada
} 