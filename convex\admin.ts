import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Configuración por defecto
const DEFAULT_SETTINGS = [
  {
    key: "trial_days",
    value: 15,
    description: "Días de trial para nuevos usuarios seller/agent"
  },
  {
    key: "premium_price",
    value: 99,
    description: "Precio mensual del plan premium en USD"
  },
  {
    key: "trial_enabled",
    value: true,
    description: "Si el sistema de trial está habilitado"
  }
];

// Inicializar configuración por defecto
export const initializeDefaultSettings = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Verificar que sea admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!user || user.role !== "admin") {
      throw new Error("Solo administradores pueden inicializar configuración");
    }

    const now = Date.now();
    const results = [];

    for (const setting of DEFAULT_SETTINGS) {
      // Verificar si ya existe
      const existing = await ctx.db
        .query("adminSettings")
        .withIndex("by_key", (q) => q.eq("key", setting.key))
        .first();

      if (!existing) {
        const id = await ctx.db.insert("adminSettings", {
          key: setting.key,
          value: setting.value,
          description: setting.description,
          updatedBy: identity.subject,
          updatedAt: now,
        });
        results.push({ key: setting.key, created: true, id });
      } else {
        results.push({ key: setting.key, created: false, existing: true });
      }
    }

    return { success: true, results };
  },
});

// Función pública para inicializar configuración (solo para setup inicial)
export const initializePublicSettings = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    const results = [];

    for (const setting of DEFAULT_SETTINGS) {
      // Verificar si ya existe
      const existing = await ctx.db
        .query("adminSettings")
        .withIndex("by_key", (q) => q.eq("key", setting.key))
        .first();

      if (!existing) {
        const id = await ctx.db.insert("adminSettings", {
          key: setting.key,
          value: setting.value,
          description: setting.description,
          updatedBy: "system",
          updatedAt: now,
        });
        results.push({ key: setting.key, created: true, id });
      } else {
        results.push({ key: setting.key, created: false, existing: true });
      }
    }

    return { success: true, results };
  },
});

// Obtener configuración admin
export const getAdminSettings = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Verificar que sea admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!user || user.role !== "admin") {
      throw new Error("Solo administradores pueden ver configuración");
    }

    const settings = await ctx.db.query("adminSettings").collect();
    
    // Convertir a objeto para fácil acceso
    const settingsObj: Record<string, any> = {};
    settings.forEach(setting => {
      settingsObj[setting.key] = {
        value: setting.value,
        description: setting.description,
        updatedAt: setting.updatedAt,
        updatedBy: setting.updatedBy
      };
    });

    return settingsObj;
  },
});

// Obtener configuración pública (sin verificar admin)
export const getPublicSettings = query({
  args: {},
  handler: async (ctx) => {
    const settings = await ctx.db.query("adminSettings").collect();
    
    // Solo devolver configuraciones públicas
    const publicKeys = ["trial_days", "premium_price", "trial_enabled"];
    const publicSettings: Record<string, any> = {};
    
    settings.forEach(setting => {
      if (publicKeys.includes(setting.key)) {
        publicSettings[setting.key] = setting.value;
      }
    });

    return publicSettings;
  },
});

// Actualizar configuración
export const updateSetting = mutation({
  args: {
    key: v.string(),
    value: v.union(v.string(), v.number(), v.boolean()),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Verificar que sea admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!user || user.role !== "admin") {
      throw new Error("Solo administradores pueden actualizar configuración");
    }

    const existing = await ctx.db
      .query("adminSettings")
      .withIndex("by_key", (q) => q.eq("key", args.key))
      .first();

    const now = Date.now();

    if (existing) {
      // Actualizar existente
      await ctx.db.patch(existing._id, {
        value: args.value,
        description: args.description || existing.description,
        updatedBy: identity.subject,
        updatedAt: now,
      });
      return { success: true, action: "updated", key: args.key };
    } else {
      // Crear nuevo
      await ctx.db.insert("adminSettings", {
        key: args.key,
        value: args.value,
        description: args.description || "",
        updatedBy: identity.subject,
        updatedAt: now,
      });
      return { success: true, action: "created", key: args.key };
    }
  },
});

// Extender trial de un usuario específico
export const extendUserTrial = mutation({
  args: {
    userId: v.string(),
    additionalDays: v.number(),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Verificar que sea admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!user || user.role !== "admin") {
      throw new Error("Solo administradores pueden extender trials");
    }

    // Buscar suscripción del usuario
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      throw new Error("Usuario no tiene suscripción");
    }

    const now = Date.now();
    const additionalMs = args.additionalDays * 24 * 60 * 60 * 1000;

    let newTrialEndDate: number;
    
    if (subscription.isTrialActive && subscription.trialEndDate) {
      // Extender trial existente
      newTrialEndDate = subscription.trialEndDate + additionalMs;
    } else {
      // Crear nuevo trial
      newTrialEndDate = now + additionalMs;
    }

    await ctx.db.patch(subscription._id, {
      isTrialActive: true,
      trialEndDate: newTrialEndDate,
      trialDaysGranted: (subscription.trialDaysGranted || 0) + args.additionalDays,
      updatedAt: now,
    });

    // Log de la acción (opcional - podrías crear una tabla de logs)
    console.log(`Admin ${identity.subject} extendió trial de usuario ${args.userId} por ${args.additionalDays} días. Razón: ${args.reason || "No especificada"}`);

    return {
      success: true,
      newTrialEndDate,
      totalDaysGranted: (subscription.trialDaysGranted || 0) + args.additionalDays,
      reason: args.reason
    };
  },
});

// Obtener estadísticas de trials
export const getTrialStats = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Verificar que sea admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!user || user.role !== "admin") {
      throw new Error("Solo administradores pueden ver estadísticas");
    }

    const subscriptions = await ctx.db.query("subscriptions").collect();
    
    const stats = {
      totalUsers: subscriptions.length,
      activeTrials: subscriptions.filter(s => s.isTrialActive).length,
      expiredTrials: subscriptions.filter(s => s.hasUsedTrial && !s.isTrialActive).length,
      paidSubscriptions: subscriptions.filter(s => s.plan !== "free" && !s.isTrialActive).length,
      conversionRate: 0,
    };

    // Calcular tasa de conversión
    const totalTrialsUsed = stats.expiredTrials + stats.paidSubscriptions;
    if (totalTrialsUsed > 0) {
      stats.conversionRate = (stats.paidSubscriptions / totalTrialsUsed) * 100;
    }

    return stats;
  },
});
